# API Import Documentation

## Endpoint: POST /api/v2/import

### Opis
Endpoint do importu wizyt z zewnętrznych systemów medycznych. Obsługuje strukturalny format danych z grupowaniem po datach i lekarzach.

### Format danych

```json
{
  "source": "drEryk",
  "syncCode": "kod_synchronizacji",
  "syncData": {
    "days": [
      {
        "date": "2025-01-15",
        "doctors": [
          {
            "doctorId": "id_lekarza",
            "doctorName": "Nazwa lekarza",
            "appointments": [
              {
                "appointmentId": "id_wizyty",
                "patientFirstName": "Jan",
                "patientLastName": "<PERSON><PERSON><PERSON>",
                "appointmentStart": "09:00",
                "appointmentDuration": 20,
                "phone_number": "123456789",
                "status": "waiting",
                "is_confirmed": 1,
                "is_patient_present": 0,
                "is_completed": 0,
                "is_sms_sent": 0
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### Pola główne

| Pole | Typ | Wymagane | Opis |
|------|-----|----------|------|
| `source` | string | Tak | Źródło danych (np. "drEryk", "iGabinet") |
| `syncCode` | string | Tak | Unikalny kod synchronizacji |
| `syncData` | object | Tak | Dane do synchronizacji |

### Struktura syncData

| Pole | Typ | Wymagane | Opis |
|------|-----|----------|------|
| `days` | array | Tak | Lista dni z wizytami |

### Struktura day

| Pole | Typ | Wymagane | Opis |
|------|-----|----------|------|
| `date` | string | Tak | Data w formacie YYYY-MM-DD |
| `doctors` | array | Tak | Lista lekarzy z wizytami |

### Struktura doctor

| Pole | Typ | Wymagane | Opis |
|------|-----|----------|------|
| `doctorId` | string | Tak | Unikalny identyfikator lekarza (MD5 hash dla drEryk) |
| `doctorName` | string | Tak | Nazwa lekarza (surowa z CSV) |
| `appointments` | array | Tak | Lista wizyt lekarza |

### Struktura appointment

| Pole | Typ | Wymagane | Opis |
|------|-----|----------|------|
| `appointmentId` | string | Tak | Unikalny identyfikator wizyty |
| `patientFirstName` | string | Tak | Imię pacjenta |
| `patientLastName` | string | Tak | Nazwisko pacjenta |
| `appointmentStart` | string | Tak | Godzina rozpoczęcia (HH:MM) |
| `appointmentDuration` | integer | Nie | Czas trwania w minutach (domyślnie 20) |
| `phone_number` | string | Nie | Numer telefonu pacjenta |
| `status` | string | Nie | Status wizyty (domyślnie "waiting") |
| `is_confirmed` | integer | Nie | Czy wizyta potwierdzona (0/1, domyślnie 1) |
| `is_patient_present` | integer | Nie | Czy pacjent obecny (0/1, domyślnie 0) |
| `is_completed` | integer | Nie | Czy wizyta zakończona (0/1, domyślnie 0) |
| `is_sms_sent` | integer | Nie | Czy SMS wysłany (0/1, domyślnie 0) |

### Odpowiedzi API

#### Sukces
```json
{
  "success": true,
  "message": "Success",
  "timestamp": "2025-10-14T12:08:17+02:00",
  "data": {
    "imported": 3,
    "total": 3,
    "errors": [],
    "source": "drEryk",
    "syncCode": "test_new_format_2025-10-14"
  }
}
```

#### Błąd - niezmapowani lekarze
```json
{
  "error": true,
  "message": "Nie można przeprowadzić importu - znaleziono niezmapowanych lekarzy",
  "timestamp": "2025-10-14T12:08:17+02:00",
  "details": {
    "unmapped_count": 1,
    "unmapped_doctors_file": "unmapped_doctors.json",
    "unmapped_doctors": [
      {
        "external_id": "bd34e78548578d113c736b9bfd98c6fb",
        "name": "JAN KOWALSKI"
      }
    ]
  }
}
```

#### Błąd - nieprawidłowy format
```json
{
  "error": true,
  "message": "Nieprawidłowy format danych - oczekiwano struktury syncData.days",
  "timestamp": "2025-10-14T12:08:17+02:00"
}
```

### Logika importu

1. **Walidacja formatu** - sprawdzenie czy dane mają strukturę `syncData.days`
2. **Przekształcenie danych** - konwersja strukturalnego formatu na płaską listę wizyt
3. **Mapowanie lekarzy** - ładowanie mapowań do bufora i sprawdzenie czy wszyscy lekarze są zmapowani
4. **Walidacja niezmapowanych** - jeśli są niezmapowani lekarze, import jest przerywany
5. **Import wizyt** - dla każdej wizyty sprawdzenie czy istnieje (UPDATE) czy nie (INSERT)
6. **Transakcja** - wszystkie operacje w ramach jednej transakcji bazy danych

### Mapowanie lekarzy

System używa tabeli `queue_doctors` z kolumną `external_doctor_id` do mapowania:
- **drEryk**: `doctorId` to MD5 hash z nazwy lekarza z CSV
- **Inne systemy**: `doctorId` to identyfikator numeryczny lub tekstowy

### Korzyści nowego formatu

1. **Eliminacja duplikacji** - `doctorId` i `doctorName` nie są powtarzane przy każdej wizycie
2. **Czytelność** - struktura odzwierciedla rzeczywistą organizację danych (dni → lekarze → wizyty)
3. **Elastyczność** - łatwe dodawanie nowych pól na poziomie dnia lub lekarza
4. **Wydajność** - mniej danych do przesłania przez sieć
5. **Logiczność** - naturalne grupowanie wizyt według dat i lekarzy
