# Dokumentacja Systemu Mapowania Lekarzy

## Przegląd

System mapowania lekarzy pozwala na powiązanie lekarzy z systemów zewnętrznych (np. iGabinet, drEryk, mMedica) z lekarzami zapisanymi w bazie danych KtoOstatni. System zapewnia, że import danych jest przeprowadzany tylko wtedy, gdy wszyscy lekarze są prawidłowo zmapowani.

## Architektura Systemu

### Komponenty

1. **Ustawienia importu** - konfiguracja w panelu admina
2. **Model ImportSettings** - zarządzanie ustawieniami i mapowaniami
3. **Kontroler DoctorMappingController** - API do zarządzania mapowaniami
4. **Widok mapowania** - interfejs do powiązywania lekarzy
5. **Modyfikacje kontrolerów importu** - walid<PERSON><PERSON> mapowań przed importem

### Przepł<PERSON>w pracy

```mermaid
graph TD
    A[Administrator włącza import] --> B[Wybiera źródło importu]
    B --> C[System zewnętrzny próbuje importować]
    C --> D{Wszyscy lekarze zmapowani?}
    D -->|Tak| E[Import danych]
    D -->|Nie| F[Utwórz plik JSON z niezmapowanymi lekarzami]
    F --> G[Odrzuć import z błędem]
    G --> H[Administrator otrzymuje komunikat]
    H --> I[Przechodzi do panelu powiązań]
    I --> J[Wybiera mapowania]
    J --> K[Zapisuje mapowania]
    K --> C
```

## Konfiguracja

### 1. Włączenie importu

1. Zaloguj się do panelu administracyjnego
2. Przejdź do `Ustawienia systemu`
3. Znajdź sekcję `Ustawienia importu`
4. Zaznacz `Włącz import danych z systemów zewnętrznych`
5. Wybierz odpowiednie źródło importu:
   - `Domyślny` - standardowy import
   - `iGabinet` - import z systemu iGabinet
   - `drEryk` - import z systemu drEryk
   - `mMedica` - import z systemu mMedica
6. Kliknij `Zapisz ustawienia importu`

### 2. Pierwsza konfiguracja mapowań

Gdy import jest włączony, a system zewnętrzny próbuje importować dane z nieznanymi lekarzami:

1. Import zostanie odrzucony z błędem
2. System utworzy plik JSON z listą niezmapowanych lekarzy
3. Administrator otrzyma komunikat o błędzie z informacją o liczbie niezmapowanych lekarzy
4. Przejdź do panelu lekarzy (`/admin/lekarze`)
5. Kliknij przycisk `Powiąż lekarzy z importu`

## Powiązywanie Lekarzy

### Interfejs mapowania

1. **Wybór pliku** - wybierz plik z listy niezmapowanych lekarzy
2. **Mapowanie** - dla każdego lekarza z importu wybierz odpowiedniego lekarza z bazy
3. **Automatyczne dopasowanie** - system może automatycznie dopasować lekarzy na podstawie nazw
4. **Zapis mapowań** - zapisz wszystkie mapowania w bazie danych

### Automatyczne dopasowanie

System wykorzystuje algorytm podobieństwa do automatycznego dopasowania lekarzy:

1. Porównuje nazwiska lekarzy
2. Oblicza podobieństwo (algorytm Levenshteina)
3. Dopasowuje lekarzy z podobieństwem > 80%
4. Administrator może ręcznie skorygować automatyczne dopasowania

### Ręczne mapowanie

Dla każdego lekarza z importu:

1. Wybierz lekarza z listy rozwijanej
2. Lub pozostaw opcję `-- Wybierz lekarza --` aby nie mapować
3. Powtórz dla wszystkich lekarzy
4. Kliknij `Zapisz mapowania`

## API

### Endpointy mapowania lekarzy

#### Zapisz mapowanie lekarzy
```
POST /api/v2/doctors/mapping/save
```

```json
{
  "sync_code": "kod_synchronizacji",
  "mappings": [
    {
      "external_id": "id_lekarza_zewnętrznego",
      "system_doctor_id": "id_lekarza_w_systemie"
    }
  ]
}
```

#### Pobierz niezmapowanych lekarzy
```
GET /api/v2/doctors/mapping/unmapped
```

#### Pobierz lekarzy z systemu
```
GET /api/v2/doctors/mapping/system
```

#### Pobierz mapowania
```
GET /api/v2/doctors/mapping?sync_code=kod_synchronizacji
```

#### Sprawdź status mapowania
```
POST /api/v2/doctors/mapping/check
```

```json
{
  "external_doctors": [
    {
      "external_id": "id_lekarza_zewnętrznego",
      "name": "Nazwa lekarza"
    }
  ],
  "sync_code": "kod_synchronizacji"
}
```

## Obsługa Błędów

### Import odrzucony

Gdy import jest odrzucony z powodu niezmapowanych lekarzy:

```json
{
  "success": false,
  "error": "Nie można przeprowadzić importu - znaleziono niezmapowanych lekarzy",
  "unmapped_count": 2,
  "unmapped_doctors_file": "unmapped_doctors_20250115_103000.json",
  "unmapped_doctors": [
    {
      "external_id": "ext_123",
      "name": "Dr Jan Kowalski",
      "specialization": "Kardiolog"
    }
  ]
}
```

### Plik z niezmapowanymi lekarzami

Struktura pliku JSON:

```json
{
  "timestamp": "2025-01-15T10:30:00Z",
  "syncCode": "kod_synchronizacji",
  "source": "igabinet",
  "unmapped_doctors": [
    {
      "external_id": "ext_123",
      "name": "Dr Jan Kowalski",
      "specialization": "Kardiolog"
    }
  ]
}
```

## Najlepsze Praktyki

1. **Spójność nazwisk** - Upewnij się, że nazwiska lekarzy w systemie zewnętrznym są spójne z nazwiskami w bazie KtoOstatni
2. **Regularne mapowania** - Mapuj nowych lekarzy zaraz po dodaniu ich do systemu zewnętrznego
3. **Weryfikacja importu** - Sprawdzaj logi importu, aby upewnić się, że wszystkie dane są importowane poprawnie
4. **Testowanie** - Przed wdrożeniem w środowisku produkcyjnym, przetestuj import na danych testowych

## Rozwiązywanie Problemów

### Problem: Import jest odrzucany mimo zmapowanych lekarzy

**Rozwiązanie:**
1. Sprawdź czy sync_code jest zgodny między systemami
2. Zweryfikuj czy mapowania są zapisane dla odpowiedniego sync_code
3. Upewnij się, że źródło importu jest poprawnie skonfigurowane

### Problem: Nie można odnaleźć lekarza w liście rozwijanej

**Rozwiązanie:**
1. Sprawdź czy lekarz istnieje w bazie danych
2. Upewnij się, że lekarz jest aktywny
3. Dodaj lekarza do systemu, jeśli nie istnieje

### Problem: Automatyczne dopasowanie nie działa poprawnie

**Rozwiązanie:**
1. Sprawdź spójność nazwisk między systemami
2. Użyj ręcznego mapowania dla problematycznych lekarzy
3. Rozważ poprawienie nazwisk w systemie zewnętrznym

## Testowanie Systemu

Aby przetestować system mapowania lekarzy:

1. Uruchom skrypt testowy:
   ```bash
   php test_doctor_mapping.php
   ```

2. Skrypt przetestuje:
   - Konfigurację ustawień importu
   - Tworzenie i odczyt mapowań
   - Walidację mapowań
   - Tworzenie plików z niezmapowanymi lekarzami

3. Sprawdź wyniki testu i upewnij się, że wszystkie komponenty działają poprawnie

## Podsumowanie

System mapowania lekarzy zapewnia bezpieczny i kontrolowany import danych z systemów zewnętrznych. Dzięki walidacji mapowań przed importem, system zapobiega importowaniu danych dla nieznanych lekarzy, co zapewnia integralność danych w systemie KtoOstatni.