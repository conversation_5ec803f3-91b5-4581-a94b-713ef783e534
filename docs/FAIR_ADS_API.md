# API Sprawiedliwego Losowania Materiałów Reklamowych

## Opis

System sprawiedliwego losowania materiałów reklamowych zapewnia równomierne wyświetlanie wszystkich zatwierdzonych materiałów. Algorytm automatycznie preferuje materiały z mniejszą liczbą wyświetleń, jednocześnie zapobiegając zapętleniu się na jednym materiale.

## Algorytm

### Zasady działania:

1. **Pobieranie TOP 5**: System pobiera 5 materiałów z najmniejszą liczbą wyświetleń
2. **Sortowanie**: Materiały sortowane według liczby wyświetleń (ASC) i ID (ASC)
3. **Losowanie**: Losuje spośród tych 5 materiałów
4. **Sprawiedliwość**: Materiały z mniejszą liczbą wyświetleń mają większą szansę na wybór
5. **Prostota**: Prosty i przewidywalny algorytm bez skomplikowanych progów

### Korzyści:

- ✅ **Sprawiedliwość**: Materiały z mniejszą liczbą wyświetleń mają większą szansę
- ✅ **Prostota**: Łatwy do zrozumienia i debugowania algorytm
- ✅ **Wydajność**: Tylko jedno zapytanie SQL z LIMIT 5
- ✅ **Przewidywalność**: Zawsze losuje spośród 5 materiałów z najmniejszą liczbą wyświetleń
- ✅ **Automatyczne wyrównywanie**: Z czasem wszystkie materiały będą miały podobną liczbę wyświetleń

## Endpointy API

### 1. Pobierz materiał (algorytm sprawiedliwy)

```
GET /api/v2/ads/{clientId}
```

**Parametry:**
- `clientId` (string) - ID klienta

**Odpowiedź:**
```json
{
  "success": true,
  "data": {
    "ads": [
      {
        "id": 2,
        "name": "Nazwa materiału",
        "media_type": "image",
        "media_url": "/uploads/image.jpg",
        "duration": 10,
        "description": "Opis materiału"
      }
    ],
    "client_id": "test_client",
    "timestamp": "2025-10-19T10:30:00+00:00"
  }
}
```

### 2. Zapisz wyświetlenie

```
POST /api/v2/ads/view
```

**Dane wejściowe:**
```json
{
  "ads_id": 1
}
```

**Odpowiedź:**
```json
{
  "success": true,
  "data": {
    "view_recorded": true,
    "ads_id": 1,
    "total_views": 46
  },
  "message": "View recorded successfully"
}
```

## Obsługa nowych materiałów

Algorytm automatycznie obsługuje nowe materiały:

- **Materiały z 0 wyświetleń** są automatycznie w TOP 5
- **Natychmiastowe włączenie** do rotacji
- **Równe szanse** z innymi materiałami z najmniejszą liczbą wyświetleń
- **Stopniowe wyrównywanie** - z czasem wszystkie materiały będą miały podobną liczbę wyświetleń

## Przykład działania

Jeśli mamy materiały z wyświetleniami: [0, 2, 2, 3, 4, 5, 6, 7, 8, 9]
- Algorytm pobierze TOP 5: [0, 2, 2, 3, 4]
- Losuje spośród tych 5 materiałów
- Materiał z 0 wyświetleń ma 20% szansy na wybór (1/5)

## Przykład użycia

### JavaScript (Frontend)

```javascript
// Pobierz materiał z algorytmem sprawiedliwym
async function getFairAd(clientId) {
    try {
        const response = await fetch(`/api/v2/ads/fair/${clientId}`);
        const data = await response.json();
        
        if (data.success && data.data.ads.length > 0) {
            const ad = data.data.ads[0];
            console.log('Wybrany materiał:', ad.name);
            console.log('Algorytm:', data.data.algorithm);
            return ad;
        }
    } catch (error) {
        console.error('Błąd pobierania materiału:', error);
    }
    return null;
}

// Zapisz wyświetlenie
async function recordAdView(adId) {
    try {
        const response = await fetch('/api/v2/ads/view', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ ads_id: adId })
        });
        
        const data = await response.json();
        if (data.success) {
            console.log('Wyświetlenie zapisane:', data.data.total_views);
        }
    } catch (error) {
        console.error('Błąd zapisywania wyświetlenia:', error);
    }
}

// Pobierz statystyki
async function getAdsStats() {
    try {
        const response = await fetch('/api/v2/ads/stats');
        const data = await response.json();
        
        if (data.success) {
            console.log('Współczynnik sprawiedliwości:', data.data.summary.fairness_ratio);
            return data.data;
        }
    } catch (error) {
        console.error('Błąd pobierania statystyk:', error);
    }
    return null;
}
```

### PHP (Backend)

```php
// Pobierz materiał z algorytmem sprawiedliwym
function getFairAd($clientId) {
    $url = "http://localhost/api/v2/ads/fair/{$clientId}";
    $response = file_get_contents($url);
    $data = json_decode($response, true);
    
    if ($data['success'] && !empty($data['data']['ads'])) {
        return $data['data']['ads'][0];
    }
    
    return null;
}

// Zapisz wyświetlenie
function recordAdView($adId) {
    $url = 'http://localhost/api/v2/ads/view';
    $postData = json_encode(['ads_id' => $adId]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $postData
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    return json_decode($response, true);
}
```

## Testowanie

Uruchom test API:

```bash
php test_fair_ads_api.php
```

Test sprawdzi:
- Statystyki przed i po testach
- Działanie algorytmu sprawiedliwego
- Porównanie z algorytmem tradycyjnym
- Współczynnik sprawiedliwości

## Monitorowanie

Regularnie sprawdzaj statystyki wyświetleń:

1. **Współczynnik sprawiedliwości** - powinien być >0.8
2. **Rozkład wyświetleń** - różnice między materiałami <20%
3. **Logi systemowe** - informacje o wyborze materiałów

## Rozwiązywanie problemów

### Problem: Jeden materiał dominuje

**Przyczyna:** Materiał ma znacznie mniej wyświetleń
**Rozwiązanie:** Algorytm automatycznie to wyrówna w czasie

### Problem: Niska sprawiedliwość

**Przyczyna:** Duże różnice w liczbie wyświetleń
**Rozwiązanie:** Zwiększ częstotliwość żądań lub zresetuj liczniki

### Problem: Brak materiałów

**Przyczyna:** Brak zatwierdzonych materiałów
**Rozwiązanie:** Zatwierdź materiały w panelu administracyjnym
