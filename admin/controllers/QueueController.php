<?php

/**
 * QueueController - zarzą<PERSON><PERSON><PERSON> kolejkami
 */
class QueueController {
    private $db;
    private $doctorModel;
    private $appointmentModel;
    private $settingsController;
    private $importSettings;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->doctorModel = new Doctor();
        $this->appointmentModel = new Appointment();
        $this->settingsController = new SettingsController();
        $this->importSettings = new ImportSettings();
    }

    /**
     * Widok zarządzania kolejkami (przeniesione z DashboardController)
     */
    public function index() {
        // Pobierz wybraną datę z sesji lub użyj dzisiejszej
        $selectedDate = $_GET['date'] ?? $_SESSION['selected_date'] ?? date('Y-m-d');

        // Walidacja daty
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $selectedDate)) {
            $selectedDate = date('Y-m-d');
        }

        // Zapisz datę w sesji
        $_SESSION['selected_date'] = $selectedDate;

        // Pobierz lekarzy z wizytami na wybraną datę
        $doctors = $this->doctorModel->getDoctorsWithAppointments($selectedDate);

        // Pobierz statystyki
        $stats = $this->getStats($selectedDate);

        // Pobierz średnie opóźnienie
        $averageDelay = $this->appointmentModel->getAverageDelay($selectedDate);

        // Dla każdego lekarza pobierz szczegółowe informacje
        foreach ($doctors as &$doctor) {
            $doctor['current_appointment'] = $this->doctorModel->getCurrentAppointment($doctor['id'], $selectedDate);
            $doctor['waiting_appointments'] = $this->doctorModel->getWaitingAppointments($doctor['id'], $selectedDate, 50);
            $doctor['delay_minutes'] = $this->doctorModel->calculateAccurateDelay($doctor['id'], $selectedDate);
            $doctor['status'] = $this->doctorModel->getStatus($doctor['id'], $selectedDate);
            $doctor['photo_url'] = getDoctorPhotoUrl($doctor['photo_url']);
        }

        // Pobierz ustawienia statusów
        $settings = $this->settingsController->getCurrentSettings();
        
        // Pobierz ustawienia importu
        $importSettings = [
            'enabled' => $this->importSettings->isImportEnabled(),
            'source' => $this->importSettings->getImportSource(),
            'sources' => $this->importSettings->getAvailableSources()
        ];

        // Renderuj widok
        $this->render('queue_management', [
            'doctors' => $doctors,
            'stats' => $stats,
            'selectedDate' => $selectedDate,
            'averageDelay' => $averageDelay,
            'user' => getCurrentUser(),
            'settings' => $settings,
            'importSettings' => $importSettings
        ]);
    }

    /**
     * Pobierz statystyki dla dashboardu (przeniesione z DashboardController)
     */
    private function getStats($date) {
        $stats = [];

        // Liczba lekarzy z wizytami
        // UWAGA: Kolumna client_id została usunięta z queue_doctors
        $stats['working_doctors'] = $this->db->fetchCount("
            SELECT COUNT(DISTINCT d.id)
            FROM queue_doctors d
            JOIN queue_appointments a ON d.id = a.doctor_id
            WHERE d.active = 1 AND a.appointment_date = ?
        ", [$date]);

        // Łączna liczba pacjentów
        $stats['total_patients'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments
            WHERE appointment_date = ?
        ", [$date]);

        return $stats;
    }

    /**
     * Wezwij pacjenta (AJAX)
     */
    public function callPatient() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowa metoda']);
            return;
        }

        // Obsługa JSON i form data
        $input = json_decode(file_get_contents('php://input'), true);
        $appointmentId = $input['appointment_id'] ?? $_POST['appointment_id'] ?? null;

        if (!$appointmentId) {
            echo json_encode(['success' => false, 'message' => 'Brak ID wizyty']);
            return;
        }

        try {
            $result = $this->appointmentModel->callPatient($appointmentId);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Pacjent został wezwany']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Nie udało się wezwać pacjenta']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Zakończ wizytę (AJAX)
     */
    public function completeAppointment() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowa metoda']);
            return;
        }

        // Obsługa JSON i form data
        $input = json_decode(file_get_contents('php://input'), true);
        $appointmentId = $input['appointment_id'] ?? $_POST['appointment_id'] ?? null;

        if (!$appointmentId) {
            echo json_encode(['success' => false, 'message' => 'Brak ID wizyty']);
            return;
        }

        try {
            $result = $this->appointmentModel->completeAppointment($appointmentId);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Wizyta została zakończona']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Nie udało się zakończyć wizyty']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Zmień status obecności pacjenta (AJAX)
     */
    public function togglePatientPresence() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowa metoda']);
            return;
        }

        // Obsługa JSON i form data
        $input = json_decode(file_get_contents('php://input'), true);
        $appointmentId = $input['appointment_id'] ?? $_POST['appointment_id'] ?? null;
        $isPresent = $input['is_present'] ?? false;

        if (!$appointmentId) {
            echo json_encode(['success' => false, 'message' => 'Brak ID wizyty']);
            return;
        }

        try {
            // Zmień status obecności pacjenta
            $sql = "
                UPDATE queue_appointments
                SET is_patient_present = ?
                WHERE id = ?
            ";

            $result = $this->db->execute($sql, [$isPresent ? 1 : 0, $appointmentId]);

            if ($result) {
                // Aktualizuj cache
                $this->updateAppointmentsCache();
                
                $message = $isPresent ? 'Potwierdzono obecność pacjenta' : 'Oznaczono pacjenta jako nieobecnego';
                echo json_encode(['success' => true, 'message' => $message]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Nie udało się zaktualizować statusu obecności']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Wezwij następnego pacjenta dla lekarza (AJAX)
     */
    public function callNext() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowa metoda']);
            return;
        }

        // Obsługa JSON i form data
        $input = json_decode(file_get_contents('php://input'), true);
        $doctorId = $input['doctor_id'] ?? $_POST['doctor_id'] ?? null;
        $date = $input['date'] ?? $_POST['date'] ?? date('Y-m-d');

        if (!$doctorId) {
            echo json_encode(['success' => false, 'message' => 'Brak ID lekarza']);
            return;
        }

        try {
            // Pobierz następną oczekującą wizytę
            $nextAppointment = $this->appointmentModel->getNextWaiting($doctorId, $date);

            if (!$nextAppointment) {
                echo json_encode(['success' => false, 'message' => 'Brak oczekujących pacjentów']);
                return;
            }

            // Wezwij pacjenta
            $result = $this->appointmentModel->callPatient($nextAppointment['id']);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Następny pacjent został wezwany',
                    'patient_name' => $nextAppointment['patient_name'],
                    'appointment_time' => $nextAppointment['appointment_time']
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Nie udało się wezwać następnego pacjenta']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Pobierz status kolejki (AJAX)
     */
    public function getQueueStatus() {
        header('Content-Type: application/json');

        $date = $_GET['date'] ?? date('Y-m-d');
        $doctorId = $_GET['doctor_id'] ?? null;

        try {
            if ($doctorId) {
                // Status dla konkretnego lekarza
                $doctor = $this->doctorModel->getById($doctorId);
                $currentAppointment = $this->doctorModel->getCurrentAppointment($doctorId, $date);
                $waitingAppointments = $this->doctorModel->getWaitingAppointments($doctorId, $date);
                $delay = $this->doctorModel->calculateAccurateDelay($doctorId, $date);

                echo json_encode([
                    'success' => true,
                    'doctor' => $doctor,
                    'current_appointment' => $currentAppointment,
                    'waiting_appointments' => $waitingAppointments,
                    'delay_minutes' => $delay,
                    'waiting_count' => count($waitingAppointments)
                ]);
            } else {
                // Status ogólny
                $stats = $this->appointmentModel->getStats($date);
                $averageDelay = $this->appointmentModel->getAverageDelay($date);

                echo json_encode([
                    'success' => true,
                    'stats' => $stats,
                    'average_delay' => $averageDelay,
                    'current_time' => date('H:i:s')
                ]);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Zmień status wizyty (AJAX)
     */
    public function toggleAppointmentStatus() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowa metoda']);
            return;
        }

        $appointmentId = $_POST['appointment_id'] ?? null;
        $fieldName = $_POST['field_name'] ?? null;
        $fieldValue = $_POST['field_value'] ?? null;

        if (!$appointmentId || !$fieldName || $fieldValue === null) {
            echo json_encode(['success' => false, 'message' => 'Brak wymaganych parametrów']);
            return;
        }

        try {
            // Pobierz aktualną wizytę
            $appointment = $this->appointmentModel->getById($appointmentId);
            if (!$appointment) {
                echo json_encode(['success' => false, 'message' => 'Wizyta nie istnieje']);
                return;
            }

            // Bezpośrednia aktualizacja bazy danych dla is_patient_present
            if ($fieldName === 'is_patient_present') {
                $sql = "
                    UPDATE queue_appointments
                    SET is_patient_present = ?
                    WHERE id = ?
                ";
                
                $result = $this->db->execute($sql, [$fieldValue ? 1 : 0, $appointmentId]);
                
                if ($result) {
                    // Aktualizuj cache
                    $this->updateAppointmentsCache();
                    
                    $message = $fieldValue ? 'Obecność pacjenta została potwierdzona' : 'Obecność pacjenta została cofnięta';
                    echo json_encode(['success' => true, 'message' => $message]);
                    return;
                } else {
                    echo json_encode(['success' => false, 'message' => 'Nie udało się zaktualizować statusu obecności']);
                    return;
                }
            }

            // Zaktualizuj odpowiednie pole w zależności od typu statusu
            $updateData = [
                'patientName' => $appointment['patient_name'],
                'appointmentTime' => $appointment['appointment_time'],
                'appointmentDate' => $appointment['appointment_date'],
                'doctorId' => $appointment['doctor_id']
            ];

            switch ($fieldName) {
                case 'is_confirmed':
                    $updateData['isConfirmed'] = (bool)$fieldValue;
                    break;
                case 'is_sms_sent':
                    $updateData['smsSent'] = (bool)$fieldValue;
                    break;
                case 'is_completed':
                    $updateData['isCompleted'] = (bool)$fieldValue;
                    break;
                default:
                    echo json_encode(['success' => false, 'message' => 'Nieznane pole do aktualizacji']);
                    return;
            }

            // Wywołaj metodę aktualizującą wizytę
            $result = $this->appointmentModel->update($appointmentId, ...array_values($updateData));

            if ($result) {
                $statusMessages = [
                    'is_confirmed' => $fieldValue ? 'Wizyta została potwierdzona' : 'Potwierdzenie wizyty zostało cofnięte',
                    'is_sms_sent' => $fieldValue ? 'SMS został wysłany' : 'Wysłanie SMS zostało cofnięte',
                    'is_completed' => $fieldValue ? 'Wizyta została oznaczona jako zakończona' : 'Status zakończenia wizyty został cofnięty'
                ];

                echo json_encode([
                    'success' => true,
                    'message' => $statusMessages[$fieldName] ?? 'Status został zaktualizowany'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Nie udało się zaktualizować statusu']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Renderuj widok
     */
    private function render($view, $data = []) {
        extract($data);

        ob_start();
        include ADMIN_PATH . '/views/' . $view . '.php';
        $content = ob_get_clean();

        include ADMIN_PATH . '/views/layout.php';
    }

    /**
     * Aktualizuj cache po zmianie w wizytach
     */
    private function updateAppointmentsCache() {
        try {
            // Importuj EnhancedCacheManager
            require_once __DIR__ . '/../../api/v2/core/EnhancedCacheManager.php';
            
            $cacheManager = new EnhancedCacheManager();
            
            // Ustaw timestamp na sekundę w przyszłości, aby zapobiec natychmiastowemu przeładowaniu
            $futureTimestamp = time() + 1;
            $cacheManager->markChangesWithTimestamp('appointments', $futureTimestamp);
            
            error_log("QueueController: Zaktualizowano cache dla appointments z timestampem w przyszłości");
        } catch (Exception $e) {
            error_log("QueueController: Błąd aktualizacji cache: " . $e->getMessage());
        }
    }
}
