<?php

/**
 * VideoController - zarządzanie materiałami video
 */
class VideoController {
    private $db;
    private $videoModel;
    private $categoryModel;
    private $advertiserModel;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->videoModel = new Video();
        $this->categoryModel = new VideoCategory();
        $this->advertiserModel = new Advertiser();
    }

    /**
     * Lista kategorii video (domyślny widok)
     */
    public function index() {
        $categories = $this->categoryModel->getAllCategories();

        $this->render('videos/index', [
            'categories' => $categories,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Lista materiałów video w kategorii
     */
    public function categoryVideos() {
        $categoryId = $_GET['id'] ?? null;

        if (!$categoryId) {
            $_SESSION['error'] = 'Brak ID kategorii';
            header('Location: /admin/video');
            exit;
        }

        $category = $this->categoryModel->getById($categoryId);
        if (!$category) {
            $_SESSION['error'] = 'Kategoria nie została znaleziona';
            header('Location: /admin/video');
            exit;
        }

        $videos = $this->videoModel->getVideosByCategory($categoryId);
        $categories = $this->categoryModel->getAllCategories();
        $advertisers = $this->advertiserModel->getAllAdvertisers();

        $this->render('videos/category_videos', [
            'category' => $category,
            'videos' => $videos,
            'categories' => $categories,
            'advertisers' => $advertisers,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Lista wszystkich materiałów video
     */
    public function allVideos() {
        $videos = $this->videoModel->getAllVideos();
        $categories = $this->categoryModel->getAllCategories();
        $advertisers = $this->advertiserModel->getAllAdvertisers();
        $stats = $this->videoModel->getStats();

        $this->render('videos/all_videos', [
            'videos' => $videos,
            'categories' => $categories,
            'advertisers' => $advertisers,
            'stats' => $stats,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Akceptuj materiał video
     */
    public function approve() {
        $id = $_POST['id'] ?? null;

        if (!$id) {
            $_SESSION['error'] = 'Brak ID materiału video';
            header('Location: /admin/video');
            exit;
        }

        try {
            $result = $this->videoModel->approve($id);
            if ($result) {
                $_SESSION['success'] = 'Materiał video został zaakceptowany';
            } else {
                $_SESSION['error'] = 'Nie udało się zaakceptować materiału video';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas akceptowania: ' . $e->getMessage();
        }

        header('Location: /admin/video');
        exit;
    }

    /**
     * Odrzuć materiał video
     */
    public function reject() {
        $id = $_POST['id'] ?? null;

        if (!$id) {
            $_SESSION['error'] = 'Brak ID materiału video';
            header('Location: /admin/video');
            exit;
        }

        try {
            $result = $this->videoModel->reject($id);
            if ($result) {
                $_SESSION['success'] = 'Materiał video został odrzucony';
            } else {
                $_SESSION['error'] = 'Nie udało się odrzucić materiału video';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas odrzucania: ' . $e->getMessage();
        }

        header('Location: /admin/video');
        exit;
    }

    /**
     * Przełącz automatyczną akceptację kategorii
     */
    public function toggleAutoAccept() {
        $id = $_POST['id'] ?? null;

        if (!$id) {
            $_SESSION['error'] = 'Brak ID kategorii';
            header('Location: /admin/video/categories');
            exit;
        }

        try {
            $result = $this->categoryModel->toggleAutoAccept($id);
            if ($result) {
                $_SESSION['success'] = 'Ustawienia automatycznej akceptacji zostały zmienione';
            } else {
                $_SESSION['error'] = 'Nie udało się zmienić ustawień';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas zmiany ustawień: ' . $e->getMessage();
        }

        header('Location: /admin/video/categories');
        exit;
    }

    /**
     * Edytuj kategorię
     */
    public function editCategory() {
        $id = $_GET['id'] ?? null;

        if (!$id) {
            $_SESSION['error'] = 'Brak ID kategorii';
            header('Location: /admin/video');
            exit;
        }

        $category = $this->categoryModel->getById($id);
        if (!$category) {
            $_SESSION['error'] = 'Kategoria nie została znaleziona';
            header('Location: /admin/video');
            exit;
        }

        $this->render('videos/edit_category', [
            'category' => $category,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Zaktualizuj kategorię
     */
    public function updateCategory() {
        $id = $_POST['id'] ?? null;
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $autoAccept = isset($_POST['auto_accept']) ? 1 : 0;

        if (!$id) {
            $_SESSION['error'] = 'Brak ID kategorii';
            header('Location: /admin/video');
            exit;
        }

        if (empty($name)) {
            $_SESSION['error'] = 'Nazwa kategorii jest wymagana';
            header('Location: /admin/video/edit-category?id=' . $id);
            exit;
        }

        try {
            $result = $this->categoryModel->update($id, $name, $description, $autoAccept);
            if ($result) {
                $_SESSION['success'] = 'Kategoria została zaktualizowana';
            } else {
                $_SESSION['error'] = 'Nie udało się zaktualizować kategorii';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas aktualizacji kategorii: ' . $e->getMessage();
        }

        header('Location: /admin/video');
        exit;
    }

    /**
     * Zaakceptuj wszystkie materiały video w kategorii
     */
    public function approveCategoryVideos() {
        $id = $_POST['id'] ?? null;

        if (!$id) {
            $_SESSION['error'] = 'Brak ID kategorii';
            header('Location: /admin/video');
            exit;
        }

        try {
            $result = $this->videoModel->approveCategoryVideos($id);
            if ($result > 0) {
                $_SESSION['success'] = "Zaakceptowano {$result} materiałów video w kategorii";
            } else {
                $_SESSION['info'] = 'Brak materiałów video do zaakceptowania w tej kategorii';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas akceptowania materiałów: ' . $e->getMessage();
        }

        header('Location: /admin/video');
        exit;
    }

    /**
     * Dodaj nową kategorię
     */
    public function addCategory() {
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $autoAccept = isset($_POST['auto_accept']) ? 1 : 0;

        if (empty($name)) {
            $_SESSION['error'] = 'Nazwa kategorii jest wymagana';
            header('Location: /admin/video/categories');
            exit;
        }

        try {
            $result = $this->categoryModel->create($name, $description, $autoAccept);
            if ($result) {
                $_SESSION['success'] = 'Kategoria została dodana';
            } else {
                $_SESSION['error'] = 'Nie udało się dodać kategorii';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas dodawania kategorii: ' . $e->getMessage();
        }

        header('Location: /admin/video/categories');
        exit;
    }

    /**
     * Usuń kategorię
     */
    public function deleteCategory() {
        $id = $_POST['id'] ?? null;

        if (!$id) {
            $_SESSION['error'] = 'Brak ID kategorii';
            header('Location: /admin/video/categories');
            exit;
        }

        try {
            $result = $this->categoryModel->delete($id);
            if ($result) {
                $_SESSION['success'] = 'Kategoria została usunięta';
            } else {
                $_SESSION['error'] = 'Nie udało się usunąć kategorii';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas usuwania kategorii: ' . $e->getMessage();
        }

        header('Location: /admin/video/categories');
        exit;
    }

    /**
     * Formularz dodawania nowego materiału video
     */
    public function addVideo() {
        $categories = $this->categoryModel->getAllCategories();
        $advertisers = $this->advertiserModel->getAllAdvertisers();

        $this->render('videos/add', [
            'categories' => $categories,
            'advertisers' => $advertisers,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Zapisz nowy materiał video
     */
    public function storeVideo() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /admin/video/add');
            exit;
        }

        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $mediaType = $_POST['media_type'] ?? '';
        $categoryId = $_POST['category_id'] ?? null;
        $advertiserId = $_POST['ads_advertiser_id'] ?? null;
        $approvalStatus = $_POST['approval_status'] ?? 'pending';
        $displayDuration = (int)($_POST['display_duration'] ?? 30);

        if (empty($name) || empty($mediaType)) {
            $_SESSION['error'] = 'Nazwa i typ materiału są wymagane';
            header('Location: /admin/video/add');
            exit;
        }

        try {
            $videoData = [
                'name' => $name,
                'description' => $description,
                'media_type' => $mediaType,
                'category_id' => $categoryId ?: null,
                'advertiser_id' => $advertiserId ?: null,
                'approval_status' => $approvalStatus,
                'display_duration' => $displayDuration
            ];

            // Obsługa YouTube URL
            if ($mediaType === 'youtube' && !empty($_POST['youtube_url'])) {
                $youtubeId = $this->extractYouTubeId($_POST['youtube_url']);
                if ($youtubeId) {
                    $videoData['youtube_id'] = $youtubeId;
                    $videoData['media_url'] = '';
                } else {
                    $_SESSION['error'] = 'Nieprawidłowy link YouTube';
                    header('Location: /admin/video/add');
                    exit;
                }
            }

            // Obsługa przesyłania pliku
            if (($mediaType === 'video' || $mediaType === 'image') && isset($_FILES['local_file']) && $_FILES['local_file']['error'] === UPLOAD_ERR_OK) {
                $uploadResult = $this->handleFileUpload($_FILES['local_file'], $mediaType);
                if ($uploadResult['success']) {
                    $videoData['media_url'] = $uploadResult['path'];
                    $videoData['local_file_path'] = $uploadResult['path'];
                    if ($mediaType === 'video') {
                        $videoData['duration'] = $uploadResult['duration'] ?? 30;
                    }
                } else {
                    $_SESSION['error'] = $uploadResult['error'];
                    header('Location: /admin/video/add');
                    exit;
                }
            } elseif ($mediaType !== 'youtube') {
                $_SESSION['error'] = 'Plik jest wymagany dla tego typu materiału';
                header('Location: /admin/video/add');
                exit;
            }

            $result = $this->videoModel->create($videoData);
            if ($result) {
                $_SESSION['success'] = 'Materiał video został dodany pomyślnie';
                header('Location: /admin/video/all');
            } else {
                $_SESSION['error'] = 'Nie udało się dodać materiału video';
                header('Location: /admin/video/add');
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas dodawania: ' . $e->getMessage();
            header('Location: /admin/video/add');
        }

        exit;
    }

    /**
     * Pobierz dane video (AJAX)
     */
    public function getVideoData() {
        $id = $_GET['id'] ?? null;

        if (!$id) {
            echo json_encode(['success' => false, 'error' => 'Brak ID video']);
            exit;
        }

        try {
            $video = $this->videoModel->getById($id);
            if ($video) {
                echo json_encode(['success' => true, 'video' => $video]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Video nie znalezione']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    /**
     * Pobierz listę reklamodawców (AJAX)
     */
    public function getAdvertisers() {
        try {
            $advertisers = $this->advertiserModel->getAllAdvertisers();
            echo json_encode(['success' => true, 'advertisers' => $advertisers]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    /**
     * Zaktualizuj materiał video
     */
    public function updateVideo() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /admin/video/all');
            exit;
        }

        $id = $_POST['id'] ?? null;
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $mediaType = $_POST['media_type'] ?? 'video';
        $categoryId = $_POST['category_id'] ?? null;
        $advertiserId = $_POST['ads_advertiser_id'] ?? null;
        $displayDuration = (int)($_POST['display_duration'] ?? 30);

        if (!$id || empty($name)) {
            $_SESSION['error'] = 'ID video i nazwa są wymagane';
            header('Location: /admin/video/all');
            exit;
        }

        try {
            $updateData = [
                'name' => $name,
                'description' => $description,
                'media_type' => $mediaType,
                'category_id' => $categoryId ?: null,
                'advertiser_id' => $advertiserId ?: null,
                'display_duration' => $displayDuration
            ];

            // Obsługa YouTube URL
            if ($mediaType === 'youtube' && !empty($_POST['youtube_url'])) {
                $youtubeId = $this->extractYouTubeId($_POST['youtube_url']);
                if ($youtubeId) {
                    $updateData['youtube_id'] = $youtubeId;
                    $updateData['media_url'] = '';
                } else {
                    $_SESSION['error'] = 'Nieprawidłowy link YouTube';
                    header('Location: /admin/video/all');
                    exit;
                }
            }

            // Obsługa przesyłania pliku
            if (isset($_FILES['local_file']) && $_FILES['local_file']['error'] === UPLOAD_ERR_OK) {
                $uploadResult = $this->handleFileUpload($_FILES['local_file'], $mediaType);
                if ($uploadResult['success']) {
                    $updateData['media_url'] = $uploadResult['path'];
                    $updateData['local_file_path'] = $uploadResult['path'];
                    if ($mediaType === 'video') {
                        $updateData['duration'] = $uploadResult['duration'] ?? 30;
                    }
                } else {
                    $_SESSION['error'] = $uploadResult['error'];
                    header('Location: /admin/video/all');
                    exit;
                }
            }

            $result = $this->videoModel->update($id, $updateData);
            if ($result) {
                $_SESSION['success'] = 'Materiał video został zaktualizowany';
            } else {
                $_SESSION['error'] = 'Nie udało się zaktualizować materiału video';
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas aktualizacji: ' . $e->getMessage();
        }

        header('Location: /admin/video/all');
        exit;
    }

    /**
     * Wyciągnij ID YouTube z URL
     */
    private function extractYouTubeId($url) {
        $pattern = '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/';
        preg_match($pattern, $url, $matches);
        return isset($matches[1]) ? $matches[1] : null;
    }

    /**
     * Obsługa przesyłania plików
     */
    private function handleFileUpload($file, $mediaType) {
        $uploadDir = __DIR__ . '/../../uploads/ads/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $allowedTypes = [
            'video' => ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'],
            'image' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
        ];

        if (!isset($allowedTypes[$mediaType]) || !in_array($file['type'], $allowedTypes[$mediaType])) {
            return ['success' => false, 'error' => 'Nieprawidłowy typ pliku'];
        }

        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid() . '.' . $extension;
        $filepath = $uploadDir . $filename;

        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            $relativePath = '/uploads/ads/' . $filename;

            $result = ['success' => true, 'path' => $relativePath];

            // Dla video, spróbuj określić długość
            if ($mediaType === 'video') {
                // Tutaj można dodać kod do określenia długości video
                $result['duration'] = 30; // Domyślna wartość
            }

            return $result;
        } else {
            return ['success' => false, 'error' => 'Błąd podczas przesyłania pliku'];
        }
    }

    /**
     * Renderuj widok
     */
    private function render($view, $data = []) {
        extract($data);

        ob_start();
        include ADMIN_PATH . '/views/' . $view . '.php';
        $content = ob_get_clean();

        include ADMIN_PATH . '/views/layout.php';
    }
}
