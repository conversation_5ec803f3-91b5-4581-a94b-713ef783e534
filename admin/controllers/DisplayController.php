<?php

/**
 * Controller do zarządzania wyświetlaczami
 */
class DisplayController {
    private $db;

    public function __construct() {
        // Upewnij się, że sesja jest aktywna dla wywołań AJAX
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $this->db = Database::getInstance()->getPdo();
    }

    /**
     * Lista wyświetlaczy
     */
    public function index() {
        $displays = $this->getDisplays();

        $this->render('displays/index', [
            'displays' => $displays,
            'title' => 'Zarządzanie wyświetlaczami',
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Sparuj wyświetlacz z kodem
     */
    public function pair() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/wyswietlacze');
        }

        $pairingCode = trim($_POST['pairing_code'] ?? '');

        if (empty($pairingCode)) {
            $_SESSION['error'] = 'Kod parowania jest wymagany';
            $this->redirect('/admin/wyswietlacze');
        }

        // Znajdź wyświetlacz z tym kodem parowania
        $stmt = $this->db->prepare("
            SELECT * FROM client_displays 
            WHERE pairing_code = ? AND pairing_status = 'pending'
            AND pairing_expires_at > datetime('now')
        ");
        $stmt->execute([strtolower($pairingCode)]);
        $display = $stmt->fetch();

        if (!$display) {
            $_SESSION['error'] = 'Nieprawidłowy lub wygasły kod parowania';
            $this->redirect('/admin/wyswietlacze');
        }

        // Sparuj wyświetlacz
        $stmt = $this->db->prepare("
            UPDATE client_displays 
            SET pairing_status = 'paired', paired_at = datetime('now'),
                pairing_code = NULL, pairing_expires_at = NULL
            WHERE id = ?
        ");

        if ($stmt->execute([$display['id']])) {
            $_SESSION['success'] = 'Wyświetlacz "' . $display['display_name'] . '" został pomyślnie sparowany';
        } else {
            $_SESSION['error'] = 'Błąd podczas parowania wyświetlacza';
        }

        $this->redirect('/admin/wyswietlacze');
    }


    /**
     * Usuń wyświetlacz
     */
    public function delete() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/wyswietlacze');
        }

        $displayId = $_POST['display_id'] ?? 0;

        if (!$displayId) {
            $_SESSION['error'] = 'Nieprawidłowy ID wyświetlacza';
            $this->redirect('/admin/wyswietlacze');
        }

        $stmt = $this->db->prepare("DELETE FROM client_displays WHERE id = ?");

        if ($stmt->execute([$displayId])) {
            $_SESSION['success'] = 'Wyświetlacz został usunięty';
        } else {
            $_SESSION['error'] = 'Błąd podczas usuwania wyświetlacza';
        }

        $this->redirect('/admin/wyswietlacze');
    }

    /**
     * Pobierz wszystkie wyświetlacze
     */
    private function getDisplays() {
        $stmt = $this->db->prepare("
            SELECT *,
                   CASE
                       WHEN pairing_status = 'paired' THEN 'Sparowany'
                       WHEN pairing_status = 'pending' THEN 'Oczekuje na parowanie'
                       ELSE 'Nie sparowany'
                   END as status_text,
                   CASE
                       WHEN last_heartbeat IS NOT NULL AND last_heartbeat > datetime('now', '-2 minutes') THEN 1
                       ELSE 0
                   END as is_online_calculated,
                   CASE
                       WHEN last_heartbeat IS NOT NULL AND last_heartbeat > datetime('now', '-2 minutes') THEN 'Online'
                       ELSE 'Offline'
                   END as online_text
            FROM client_displays
            ORDER BY created_at DESC
        ");
        $stmt->execute();
        $displays = $stmt->fetchAll();
        
        // Zastąp is_online obliczoną wartością
        foreach ($displays as &$display) {
            $display['is_online'] = $display['is_online_calculated'];
        }
        
        return $displays;
    }


    /**
     * Aktualizuj głośność wyświetlacza
     */
    public function updateVolume() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowa metoda']);
            return;
        }

        // Sprawdź autoryzację
        if (!function_exists('isLoggedIn')) {
            echo json_encode(['success' => false, 'message' => 'Funkcja isLoggedIn nie jest dostępna']);
            return;
        }

        if (!isLoggedIn()) {
            echo json_encode(['success' => false, 'message' => 'Brak autoryzacji']);
            return;
        }

        $displayId = (int)($_POST['display_id'] ?? 0);
        $volume = (int)($_POST['volume'] ?? 100);

        if ($displayId <= 0) {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowe ID wyświetlacza']);
            return;
        }

        if ($volume < 0 || $volume > 100) {
            echo json_encode(['success' => false, 'message' => 'Głośność musi być między 0 a 100%']);
            return;
        }

        try {
            // Aktualizuj głośność w bazie danych
            $stmt = $this->db->prepare("UPDATE client_displays SET volume = ? WHERE id = ?");
            $result = $stmt->execute([$volume, $displayId]);

            if ($result) {
                // Oznacz zmiany w cache aby wyświetlacze pobrały nowe ustawienia
                require_once __DIR__ . '/../../api/v2/core/CacheUpdater.php';
                CacheUpdater::markChanges('settings');

                echo json_encode([
                    'success' => true,
                    'message' => 'Głośność została zaktualizowana',
                    'volume' => $volume
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Błąd podczas aktualizacji głośności']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Błąd bazy danych: ' . $e->getMessage()]);
        }
    }

    /**
     * Aktualizuj ustawienie napisów wyświetlacza
     */
    public function updateSubtitles() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowa metoda']);
            return;
        }

        // Sprawdź autoryzację
        if (!function_exists('isLoggedIn')) {
            echo json_encode(['success' => false, 'message' => 'Funkcja isLoggedIn nie jest dostępna']);
            return;
        }

        if (!isLoggedIn()) {
            echo json_encode(['success' => false, 'message' => 'Brak autoryzacji']);
            return;
        }

        $displayId = (int)($_POST['display_id'] ?? 0);
        $showSubtitles = isset($_POST['show_subtitles']) ? (bool)$_POST['show_subtitles'] : false;

        if ($displayId <= 0) {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowe ID wyświetlacza']);
            return;
        }

        try {
            // Aktualizuj ustawienie napisów w bazie danych
            $stmt = $this->db->prepare("UPDATE client_displays SET show_subtitles = ? WHERE id = ?");
            $result = $stmt->execute([$showSubtitles ? 1 : 0, $displayId]);

            if ($result) {
                // Oznacz zmiany w cache aby wyświetlacze pobrały nowe ustawienia
                require_once __DIR__ . '/../../api/v2/core/CacheUpdater.php';
                CacheUpdater::markChanges('settings');

                echo json_encode([
                    'success' => true,
                    'message' => 'Ustawienie napisów zostało zaktualizowane',
                    'show_subtitles' => $showSubtitles
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Błąd podczas aktualizacji ustawienia napisów']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Błąd bazy danych: ' . $e->getMessage()]);
        }
    }

    /**
     * Aktualizuj wielkość czcionki napisów wyświetlacza
     */
    public function updateSubtitleFontSize() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowa metoda']);
            return;
        }

        // Sprawdź autoryzację
        if (!function_exists('isLoggedIn')) {
            echo json_encode(['success' => false, 'message' => 'Funkcja isLoggedIn nie jest dostępna']);
            return;
        }

        if (!isLoggedIn()) {
            echo json_encode(['success' => false, 'message' => 'Brak autoryzacji']);
            return;
        }

        $displayId = (int)($_POST['display_id'] ?? 0);
        $fontSize = (int)($_POST['subtitle_font_size'] ?? 16);

        if ($displayId <= 0) {
            echo json_encode(['success' => false, 'message' => 'Nieprawidłowe ID wyświetlacza']);
            return;
        }

        // Sprawdź czy rozmiar czcionki jest w dopuszczalnym zakresie
        if ($fontSize < 8 || $fontSize > 48) {
            echo json_encode(['success' => false, 'message' => 'Wielkość czcionki musi być między 8 a 48px']);
            return;
        }

        try {
            // Aktualizuj wielkość czcionki w bazie danych
            $stmt = $this->db->prepare("UPDATE client_displays SET subtitle_font_size = ? WHERE id = ?");
            $result = $stmt->execute([$fontSize, $displayId]);

            if ($result) {
                // Oznacz zmiany w cache aby wyświetlacze pobrały nowe ustawienia
                require_once __DIR__ . '/../../api/v2/core/CacheUpdater.php';
                CacheUpdater::markChanges('settings');

                echo json_encode([
                    'success' => true,
                    'message' => 'Wielkość czcionki napisów została zaktualizowana',
                    'subtitle_font_size' => $fontSize
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Błąd podczas aktualizacji wielkości czcionki napisów']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Błąd bazy danych: ' . $e->getMessage()]);
        }
    }

    /**
     * Renderuj widok
     */
    private function render($view, $data = []) {
        extract($data);

        ob_start();
        include ADMIN_PATH . '/views/' . $view . '.php';
        $content = ob_get_clean();

        include ADMIN_PATH . '/views/layout.php';
    }

    /**
     * Aktualizuj nazwę wyświetlacza
     */
    public function updateName() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/wyswietlacze');
            return;
        }

        $displayId = $_POST['display_id'] ?? null;
        $displayName = trim($_POST['display_name'] ?? '');

        if (!$displayId || !$displayName) {
            $_SESSION['error'] = 'Nieprawidłowe dane formularza';
            $this->redirect('/admin/wyswietlacze');
            return;
        }

        // Sprawdź czy wyświetlacz istnieje
        $stmt = $this->db->prepare("SELECT id FROM client_displays WHERE id = ?");
        $stmt->execute([$displayId]);
        $display = $stmt->fetch();

        if (!$display) {
            $_SESSION['error'] = 'Wyświetlacz nie został znaleziony';
            $this->redirect('/admin/wyswietlacze');
            return;
        }

        // Aktualizuj nazwę
        $stmt = $this->db->prepare("UPDATE client_displays SET display_name = ? WHERE id = ?");

        if ($stmt->execute([$displayName, $displayId])) {
            $_SESSION['success'] = 'Nazwa wyświetlacza została zaktualizowana';
        } else {
            $_SESSION['error'] = 'Błąd podczas aktualizacji nazwy wyświetlacza';
        }

        $this->redirect('/admin/wyswietlacze');
    }

    /**
     * Przekieruj
     */
    private function redirect($url) {
        header('Location: ' . $url);
        exit;
    }
}
