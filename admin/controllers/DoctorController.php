<?php

/**
 * DoctorController - zar<PERSON><PERSON><PERSON><PERSON><PERSON> lekarzami
 */
class DoctorController {
    private $db;
    private $doctorModel;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->doctorModel = new Doctor();
    }

    /**
     * Lista lekarzy
     */
    public function index() {
        $doctors = $this->doctorModel->getAllForClient();

        // Upewnij się że ścieżki do zdjęć mają poprawny format
        foreach ($doctors as &$doctor) {
            if (!empty($doctor['photo_url'])) {
                $photoUrl = $doctor['photo_url'];
                // Upewnij się że ścieżka do zdjęcia zaczyna się od /uploads/
                if (!str_starts_with($photoUrl, '/uploads/')) {
                    // Jeśli ścieżka zaczyna się od uploads/ (bez leading slash), dodaj slash na początku
                    if (str_starts_with($photoUrl, 'uploads/')) {
                        $doctor['photo_url'] = '/' . $photoUrl;
                    }
                    // Jeś<PERSON> ścieżka nie zawiera uploads/ w ogóle, dodaj pełną ścieżkę
                    else if (!str_contains($photoUrl, 'uploads/')) {
                        $doctor['photo_url'] = '/uploads/' . $photoUrl;
                    }
                }
            }
        }

        $this->render('doctors/index', [
            'doctors' => $doctors,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Formularz dodawania lekarza
     */
    public function create() {
        // Pobierz listę gabinetów
        $roomModel = new Room();
        $rooms = $roomModel->getAll();

        // Pobierz listę wyświetlaczy
        $displayModel = new Display();
        $displays = $displayModel->getPaired(); // Tylko sparowane wyświetlacze

        $this->render('doctors/create', [
            'rooms' => $rooms,
            'displays' => $displays,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Zapisz nowego lekarza
     */
    public function store() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /admin/doctors');
            exit;
        }

        $firstName = trim($_POST['first_name'] ?? '');
        $lastName = trim($_POST['last_name'] ?? '');
        $specialization = trim($_POST['specialization'] ?? '');
        $defaultRoomId = !empty($_POST['default_room_id']) ? (int)$_POST['default_room_id'] : null;
        $displayId = !empty($_POST['display_id']) ? (int)$_POST['display_id'] : null;
        $photoUrl = ''; // Default to empty string for new doctors

        if (empty($firstName) || empty($lastName)) {
            $_SESSION['error'] = 'Imię i nazwisko są wymagane';
            header('Location: /admin/doctors/create');
            exit;
        }

        // Handle file upload
        if (!empty($_FILES['photo_upload']['name'])) {
            error_log("DEBUG: Rozpoczynam przesyłanie pliku: " . $_FILES['photo_upload']['name']);
            $file = $_FILES['photo_upload'];
            
            error_log("DEBUG: Dane pliku - type: " . $file['type'] . ", size: " . $file['size'] . ", tmp_name: " . $file['tmp_name']);

            // Check if file is an image
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file['type'], $allowedTypes)) {
                error_log("DEBUG: Nieprawidłowy typ pliku: " . $file['type']);
                $_SESSION['error'] = 'Dozwolone są tylko pliki obrazów (JPEG, PNG, GIF, WebP)';
                header('Location: /admin/doctors/create');
                exit;
            }

            // Check file size (max 5MB)
            if ($file['size'] > 5 * 1024 * 1024) {
                error_log("DEBUG: Plik za duży: " . $file['size'] . " bajtów");
                $_SESSION['error'] = 'Rozmiar pliku nie może przekraczać 5MB';
                header('Location: /admin/doctors/create');
                exit;
            }

            // Create uploads directory if it doesn't exist
            $uploadDir = DOCTORS_PHOTOS_PATH;
            if (!file_exists($uploadDir)) {
                error_log("DEBUG: Tworzę katalog: " . $uploadDir);
                mkdir($uploadDir, 0755, true);
            } else {
                error_log("DEBUG: Katalog istnieje: " . $uploadDir);
                error_log("DEBUG: Uprawnienia katalogu: " . substr(sprintf('%o', fileperms($uploadDir)), -4));
            }

            // Generate unique filename
            $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $fileName = 'doctor_' . time() . '.' . $fileExtension;
            $filePath = $uploadDir . '/' . $fileName;
            
            error_log("DEBUG: Ścieżka docelowa: " . $filePath);

            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $filePath)) {
                error_log("DEBUG: Plik przesłany pomyślnie");
                // Set photo URL to the uploaded file path
                $photoUrl = '/uploads/doctors/' . $fileName;
                error_log("DEBUG: URL zdjęcia: " . $photoUrl);
            } else {
                error_log("DEBUG: Błąd przesyłania pliku. Błędy: " . print_r(error_get_last(), true));
                $_SESSION['error'] = 'Błąd podczas przesyłania pliku';
                header('Location: /admin/doctors/create');
                exit;
            }
        } else {
            error_log("DEBUG: Brak pliku do przesłania");
        }

        try {
            $this->doctorModel->create($firstName, $lastName, $specialization, $photoUrl, $defaultRoomId, $displayId);
            $_SESSION['success'] = 'Lekarz został dodany pomyślnie';
            header('Location: /admin/doctors');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas dodawania lekarza: ' . $e->getMessage();
            header('Location: /admin/doctors/create');
        }
        exit;
    }

    /**
     * Formularz edycji lekarza
     */
    public function edit() {
        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/doctors');
            exit;
        }

        $doctor = $this->doctorModel->getById($id);
        if (!$doctor) {
            $_SESSION['error'] = 'Lekarz nie został znaleziony';
            header('Location: /admin/doctors');
            exit;
        }

        // Upewnij się że ścieżka do zdjęcia ma poprawny format
        if (!empty($doctor['photo_url'])) {
            $photoUrl = $doctor['photo_url'];
            // Upewnij się że ścieżka do zdjęcia zaczyna się od /uploads/
            if (!str_starts_with($photoUrl, '/uploads/')) {
                // Jeśli ścieżka zaczyna się od uploads/ (bez leading slash), dodaj slash na początku
                if (str_starts_with($photoUrl, 'uploads/')) {
                    $doctor['photo_url'] = '/' . $photoUrl;
                }
                // Jeśli ścieżka nie zawiera uploads/ w ogóle, dodaj pełną ścieżkę
                else if (!str_contains($photoUrl, 'uploads/')) {
                    $doctor['photo_url'] = '/uploads/' . $photoUrl;
                }
            }
        }

        // Pobierz listę gabinetów
        $roomModel = new Room();
        $rooms = $roomModel->getAll();

        // Pobierz listę wyświetlaczy
        $displayModel = new Display();
        $displays = $displayModel->getPaired(); // Tylko sparowane wyświetlacze

        $this->render('doctors/edit', [
            'doctor' => $doctor,
            'rooms' => $rooms,
            'displays' => $displays,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Aktualizuj lekarza
     */
    public function update() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /admin/doctors');
            exit;
        }

        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/doctors');
            exit;
        }

        $firstName = trim($_POST['first_name'] ?? '');
        $lastName = trim($_POST['last_name'] ?? '');
        $specialization = trim($_POST['specialization'] ?? '');
        $defaultRoomId = !empty($_POST['default_room_id']) ? (int)$_POST['default_room_id'] : null;
        $displayId = !empty($_POST['display_id']) ? (int)$_POST['display_id'] : null;
        $login = trim($_POST['login'] ?? '');
        $photoUrl = null; // Default to null, will be set if a file is uploaded

        if (empty($firstName) || empty($lastName)) {
            $_SESSION['error'] = 'Imię i nazwisko są wymagane';
            header('Location: /admin/doctors/edit/' . $id);
            exit;
        }

        // Walidacja loginu jeśli podano
        if (!empty($login)) {
            // Pobierz aktualne dane lekarza aby sprawdzić czy login się zmienił
            $currentDoctor = $this->doctorModel->getById($id);
            $currentLogin = $currentDoctor['login'] ?? '';
            
            // Sprawdź unikalność loginu tylko jeśli się zmienił
            if ($login !== $currentLogin) {
                if (!$this->doctorModel->isLoginUnique($login, $id)) {
                    $_SESSION['error'] = 'Podany login jest już używany przez innego lekarza';
                    header('Location: /admin/doctors/edit/' . $id);
                    exit;
                }
            }
            
            // Walidacja formatu email
            if (!filter_var($login, FILTER_VALIDATE_EMAIL)) {
                $_SESSION['error'] = 'Login musi być prawidłowym adresem email';
                header('Location: /admin/doctors/edit/' . $id);
                exit;
            }
        }

        // Handle file upload
        if (!empty($_FILES['photo_upload']['name'])) {
            error_log("DEBUG UPDATE: Rozpoczynam przesyłanie pliku: " . $_FILES['photo_upload']['name']);
            $file = $_FILES['photo_upload'];
            
            error_log("DEBUG UPDATE: Dane pliku - type: " . $file['type'] . ", size: " . $file['size'] . ", tmp_name: " . $file['tmp_name']);

            // Check if file is an image
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file['type'], $allowedTypes)) {
                error_log("DEBUG UPDATE: Nieprawidłowy typ pliku: " . $file['type']);
                $_SESSION['error'] = 'Dozwolone są tylko pliki obrazów (JPEG, PNG, GIF, WebP)';
                header('Location: /admin/doctors/edit/' . $id);
                exit;
            }

            // Check file size (max 5MB)
            if ($file['size'] > 5 * 1024 * 1024) {
                error_log("DEBUG UPDATE: Plik za duży: " . $file['size'] . " bajtów");
                $_SESSION['error'] = 'Rozmiar pliku nie może przekraczać 5MB';
                header('Location: /admin/doctors/edit/' . $id);
                exit;
            }

            // Create uploads directory if it doesn't exist
            $uploadDir = DOCTORS_PHOTOS_PATH;
            if (!file_exists($uploadDir)) {
                error_log("DEBUG UPDATE: Tworzę katalog: " . $uploadDir);
                mkdir($uploadDir, 0755, true);
            } else {
                error_log("DEBUG UPDATE: Katalog istnieje: " . $uploadDir);
                error_log("DEBUG UPDATE: Uprawnienia katalogu: " . substr(sprintf('%o', fileperms($uploadDir)), -4));
            }

            // Generate unique filename
            $fileExtension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $fileName = 'doctor_' . $id . '_' . time() . '.' . $fileExtension;
            $filePath = $uploadDir . '/' . $fileName;
            
            error_log("DEBUG UPDATE: Ścieżka docelowa: " . $filePath);

            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $filePath)) {
                error_log("DEBUG UPDATE: Plik przesłany pomyślnie");
                // Set photo URL to the uploaded file path
                $photoUrl = '/uploads/doctors/' . $fileName;
                error_log("DEBUG UPDATE: URL zdjęcia: " . $photoUrl);
            } else {
                error_log("DEBUG UPDATE: Błąd przesyłania pliku. Błędy: " . print_r(error_get_last(), true));
                $_SESSION['error'] = 'Błąd podczas przesyłania pliku';
                header('Location: /admin/doctors/edit/' . $id);
                exit;
            }
        } else {
            error_log("DEBUG UPDATE: Brak nowego pliku do przesłania");
            // If no new photo was uploaded, keep the existing one
            $doctor = $this->doctorModel->getById($id);
            if ($doctor && !empty($doctor['photo_url'])) {
                $photoUrl = $doctor['photo_url'];
                error_log("DEBUG UPDATE: Zachowuję istniejące zdjęcie: " . $photoUrl);
            }
        }

        try {
            // Logowanie danych przed aktualizacją
            error_log("DEBUG: Próba aktualizacji lekarza ID: $id");
            error_log("DEBUG: Dane: first_name=$firstName, last_name=$lastName, specialization=$specialization");
            error_log("DEBUG: Dodatkowe dane: default_room_id=$defaultRoomId, display_id=$displayId, photo_url=$photoUrl, login=$login");
            
            $result = $this->doctorModel->update($id, $firstName, $lastName, $specialization, $photoUrl, $defaultRoomId, $displayId, $login);
            error_log("DEBUG: Wynik aktualizacji: " . ($result ? 'SUCCESS' : 'FAILED'));
            
            $_SESSION['success'] = 'Lekarz został zaktualizowany pomyślnie';
            header('Location: /admin/doctors');
        } catch (Exception $e) {
            error_log("DEBUG: Błąd podczas aktualizacji lekarza: " . $e->getMessage());
            error_log("DEBUG: Stack trace: " . $e->getTraceAsString());
            $_SESSION['error'] = 'Błąd podczas aktualizacji lekarza: ' . $e->getMessage();
            header('Location: /admin/doctors/edit/' . $id);
        }
        exit;
    }

    /**
     * Usuń lekarza
     */
    public function delete() {
        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/doctors');
            exit;
        }

        try {
            $this->doctorModel->delete($id);
            $_SESSION['success'] = 'Lekarz został usunięty pomyślnie';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas usuwania lekarza: ' . $e->getMessage();
        }

        header('Location: /admin/doctors');
        exit;
    }


    /**
     * Zmień hasło lekarza
     */
    public function changePassword() {
        header('Content-Type: application/json');

        $id = $_GET['id'] ?? null;
        $newPassword = $_POST['new_password'] ?? null;

        if (!$id) {
            echo json_encode(['success' => false, 'error' => 'Brak ID lekarza']);
            exit;
        }

        if (!$newPassword || strlen($newPassword) < 8) {
            echo json_encode(['success' => false, 'error' => 'Hasło musi mieć co najmniej 8 znaków']);
            exit;
        }

        try {
            $result = $this->doctorModel->changePassword($id, $newPassword);
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Hasło zostało zmienione pomyślnie']);
            } else {
                echo json_encode(['success' => false, 'error' => 'Nie udało się zmienić hasła']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    /**
     * Sprawdź unikalność loginu
     */
    public function checkLoginUniqueness() {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['unique' => false, 'error' => 'Nieprawidłowa metoda']);
            exit;
        }

        $input = json_decode(file_get_contents('php://input'), true);
        $login = trim($input['login'] ?? '');
        $doctorId = $input['doctor_id'] ?? null;

        if (empty($login)) {
            echo json_encode(['unique' => false, 'error' => 'Login jest wymagany']);
            exit;
        }

        try {
            $isUnique = $this->doctorModel->isLoginUnique($login, $doctorId);
            echo json_encode(['unique' => $isUnique]);
        } catch (Exception $e) {
            echo json_encode(['unique' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    /**
     * API - statystyki lekarza
     */
    public function getStats() {
        $doctorId = $_GET['doctor_id'] ?? null;
        $date = $_GET['date'] ?? date('Y-m-d');

        if (!$doctorId) {
            http_response_code(400);
            echo json_encode(['error' => 'Brak ID lekarza']);
            exit;
        }

        try {
            $stats = $this->doctorModel->getStats($doctorId, $date);
            echo json_encode(['success' => true, 'stats' => $stats]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        exit;
    }

    /**
     * Widok mapowania lekarzy z importu
     */
    public function mapImport() {
        // Wczytaj dane o niezmapowanych lekarzach bezpośrednio z pliku JSON
        $cacheDir = __DIR__ . '/../../api/v2/cache';
        $unmappedDoctorsFile = $cacheDir . '/unmapped_doctors.json';
        $unmappedDoctorsData = null;
        
        if (file_exists($unmappedDoctorsFile)) {
            $jsonContent = file_get_contents($unmappedDoctorsFile);
            if ($jsonContent !== false) {
                $unmappedDoctorsData = json_decode($jsonContent, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $unmappedDoctorsData = null;
                    error_log('Błąd JSON w pliku unmapped_doctors.json: ' . json_last_error_msg());
                }
            }
        }
        
        $systemDoctors = $this->doctorModel->getAllForClient();

        // Formatuj lekarzy systemu dla frontendu
        $formattedSystemDoctors = [];
        foreach ($systemDoctors as $doctor) {
            $formattedSystemDoctors[] = [
                'id' => $doctor['id'],
                'name' => $doctor['first_name'] . ' ' . $doctor['last_name'],
                'first_name' => $doctor['first_name'],
                'last_name' => $doctor['last_name'],
                'specialization' => $doctor['specialization'] ?? ''
            ];
        }

        // Pobierz aktualnie zmapowanych lekarzy
        $mappedDoctorsSql = "SELECT id, first_name, last_name, specialization, external_doctor_id, external_doctor_name, external_last_sync
                            FROM queue_doctors
                            WHERE external_doctor_id IS NOT NULL
                            ORDER BY last_name, first_name";
        $mappedDoctors = $this->db->fetchAll($mappedDoctorsSql);

        $this->render('doctors/map_import', [
            'user' => getCurrentUser(),
            'unmappedDoctorsData' => $unmappedDoctorsData,
            'systemDoctors' => $formattedSystemDoctors,
            'mappedDoctors' => $mappedDoctors
        ]);
    }
    
    /**
     * Zapisz mapowania lekarzy
     */
    public function saveMappings() {
        // Dodatkowe logowanie na początku - zapisz do pliku w katalogu aplikacji
        $logFile = __DIR__ . '/../../mapping_debug.log';
        $logMessage = date('Y-m-d H:i:s') . " - DoctorMapping - saveMappings() called\n";
        $logMessage .= date('Y-m-d H:i:s') . " - REQUEST_METHOD: " . ($_SERVER['REQUEST_METHOD'] ?? 'UNDEFINED') . "\n";
        $logMessage .= date('Y-m-d H:i:s') . " - POST data: " . json_encode($_POST) . "\n";
        $logMessage .= date('Y-m-d H:i:s') . " - Session ID: " . session_id() . "\n";
        $logMessage .= date('Y-m-d H:i:s') . " - Current timestamp: " . time() . "\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND);

        error_log("DoctorMapping - saveMappings() called");
        error_log("DoctorMapping - REQUEST_METHOD: " . ($_SERVER['REQUEST_METHOD'] ?? 'UNDEFINED'));
        error_log("DoctorMapping - POST data: " . json_encode($_POST));
        error_log("DoctorMapping - Session ID: " . session_id());

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            error_log("DoctorMapping - Not POST request, redirecting");
            header('Location: /admin/doctors/map-import');
            exit;
        }

        $mappings = $_POST['mappings'] ?? [];
        error_log("DoctorMapping - Extracted mappings: " . json_encode($mappings));

        if (empty($mappings) || !is_array($mappings)) {
            error_log("DoctorMapping - Empty or invalid mappings");
            $_SESSION['error'] = 'Brak mapowań do zapisania';
            header('Location: /admin/doctors/map-import');
            exit;
        }

        // Logowanie otrzymanych mapowań
        error_log("DoctorMapping - Received mappings: " . json_encode($mappings));
        error_log("DoctorMapping - REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
        error_log("DoctorMapping - POST data: " . json_encode($_POST));
        
        try {
            $cacheDir = __DIR__ . '/../../api/v2/cache';
            $unmappedDoctorsFile = $cacheDir . '/unmapped_doctors.json';
            
            // Wczytaj dane z pliku unmapped_doctors.json
            $unmappedDoctorsData = [];
            if (file_exists($unmappedDoctorsFile)) {
                $jsonContent = file_get_contents($unmappedDoctorsFile);
                if ($jsonContent !== false) {
                    $unmappedDoctorsData = json_decode($jsonContent, true) ?: [];
                }
            }
            
            $successCount = 0;
            $errorCount = 0;
            $doctorsToRemove = [];
            
            // Przetwarzaj mapowania
            foreach ($mappings as $externalId => $systemDoctorId) {
                // Logowanie początku przetwarzania
                $logMessage = date('Y-m-d H:i:s') . " - Processing mapping: external_id=$externalId, system_id=$systemDoctorId\n";
                file_put_contents(__DIR__ . '/../../mapping_debug.log', $logMessage, FILE_APPEND);

                if ($systemDoctorId === '') {
                    // Pomiń puste mapowania
                    $logMessage = date('Y-m-d H:i:s') . " - Skipping empty mapping for external_id=$externalId\n";
                    file_put_contents(__DIR__ . '/../../mapping_debug.log', $logMessage, FILE_APPEND);
                    continue;
                }
                
                // Znajdź dane lekarza z pliku unmapped_doctors
                $doctorData = null;
                foreach ($unmappedDoctorsData['unmapped_doctors'] ?? [] as $doctor) {
                    if ((string)$doctor['external_id'] === (string)$externalId) {
                        $doctorData = $doctor;
                        break;
                    }
                }

                // Dodatkowe logowanie
                $logMessage = date('Y-m-d H:i:s') . " - Searching for external_id: $externalId (type: " . gettype($externalId) . ")\n";
                $logMessage .= "  Found in unmapped_doctors: " . ($doctorData ? 'YES' : 'NO') . "\n";
                if ($doctorData) {
                    $logMessage .= "  Doctor name: {$doctorData['name']}\n";
                }
                file_put_contents(__DIR__ . '/../../mapping_debug.log', $logMessage, FILE_APPEND);

                // Jeśli nie ma danych w pliku unmapped_doctors, sprawdź czy lekarz już istnieje w bazie
                if (!$doctorData) {
                    error_log("DoctorMapping - Doctor not found in unmapped_doctors file: external_id=$externalId");

                    // Sprawdź czy lekarz już istnieje w bazie z tym external_id
                    $existingMappingSql = "SELECT external_doctor_name FROM queue_doctors WHERE external_doctor_id = ? LIMIT 1";
                    $existingMapping = $this->db->fetchOne($existingMappingSql, [$externalId]);

                    if ($existingMapping && !empty($existingMapping['external_doctor_name'])) {
                        // Użyj nazwy z istniejącego mapowania
                        $doctorData = [
                            'external_id' => $externalId,
                            'name' => $existingMapping['external_doctor_name']
                        ];
                        error_log("DoctorMapping - Using existing name from database: {$existingMapping['external_doctor_name']}");
                    } else {
                        // Utwórz podstawowe dane jako fallback
                        $doctorData = [
                            'external_id' => $externalId,
                            'name' => 'Lekarz zewnętrzny ID: ' . $externalId
                        ];
                        error_log("DoctorMapping - Creating basic fallback data");
                    }
                }
                
                // Sprawdź czy lekarz istnieje w bazie
                $checkSql = "SELECT id, first_name, last_name FROM queue_doctors WHERE id = ?";
                $existingDoctor = $this->db->fetchOne($checkSql, [$systemDoctorId]);

                if (!$existingDoctor) {
                    error_log("DoctorMapping - Doctor not found in database: ID=$systemDoctorId");
                    $errorCount++;
                    continue;
                }

                // Sprawdź czy external_doctor_id nie jest już przypisany do innego lekarza
                $duplicateCheckSql = "SELECT id, first_name, last_name FROM queue_doctors WHERE external_doctor_id = ? AND id != ?";
                $duplicateDoctor = $this->db->fetchOne($duplicateCheckSql, [$externalId, $systemDoctorId]);

                if ($duplicateDoctor) {
                    error_log("DoctorMapping - External ID already mapped to another doctor: external_id=$externalId, existing_doctor_id={$duplicateDoctor['id']}");
                    $errorCount++;
                    continue;
                }
                
                error_log("DoctorMapping - Found doctor in database: " . json_encode($existingDoctor));
                
                // Zaktualizuj lekarza w bazie danych
                $sql = "
                    UPDATE queue_doctors
                    SET external_doctor_id = ?,
                        external_doctor_name = ?,
                        external_last_sync = ?,
                        updated_at = datetime('now')
                    WHERE id = ?
                ";
                
                $params = [
                    $externalId,
                    $doctorData['name'],
                    date('Y-m-d H:i:s'),
                    $systemDoctorId
                ];
                
                error_log("DoctorMapping - Updating doctor: SQL=" . $sql);
                error_log("DoctorMapping - Updating doctor: Params=" . json_encode($params));
                
                // Sprawdź stan przed aktualizacją
                $beforeSql = "SELECT id, external_doctor_id, external_doctor_name FROM queue_doctors WHERE id = ?";
                $beforeState = $this->db->fetchOne($beforeSql, [$systemDoctorId]);
                error_log("DoctorMapping - State before update: " . json_encode($beforeState));
                error_log("DoctorMapping - SQL to execute: " . $sql);
                error_log("DoctorMapping - SQL params: " . json_encode($params));
                
                $result = $this->db->execute($sql, $params);
                error_log("DoctorMapping - Execute result: " . ($result ? 'true' : 'false'));
                
                if ($result) {
                    // Sprawdź stan po aktualizacji
                    $afterState = $this->db->fetchOne($beforeSql, [$systemDoctorId]);
                    error_log("DoctorMapping - State after update: " . json_encode($afterState));
                    
                    // Sprawdź czy dane faktycznie się zmieniły
                    $logMessage = date('Y-m-d H:i:s') . " - Checking update result:\n";
                    $logMessage .= "  afterState: " . json_encode($afterState) . "\n";
                    $logMessage .= "  expected external_id: $externalId\n";
                    $logMessage .= "  expected name: {$doctorData['name']}\n";
                    $logMessage .= "  match external_id: " . ((string)$afterState['external_doctor_id'] === (string)$externalId ? 'YES' : 'NO') . "\n";
                    $logMessage .= "  match name: " . ($afterState['external_doctor_name'] === $doctorData['name'] ? 'YES' : 'NO') . "\n";
                    file_put_contents(__DIR__ . '/../../mapping_debug.log', $logMessage, FILE_APPEND);

                    if ($afterState &&
                        (string)$afterState['external_doctor_id'] === (string)$externalId &&
                        $afterState['external_doctor_name'] === $doctorData['name']) {

                        $successCount++;
                        $doctorsToRemove[] = $externalId;
                        error_log("DoctorMapping - Update successful: external_id=$externalId, system_id=$systemDoctorId");

                        // Dodatkowe logowanie do pliku
                        $logMessage = date('Y-m-d H:i:s') . " - SUCCESS: external_id=$externalId, system_id=$systemDoctorId\n";
                        file_put_contents(__DIR__ . '/../../mapping_debug.log', $logMessage, FILE_APPEND);
                    } else {
                        $errorCount++;
                        error_log("DoctorMapping - Update appeared successful but data not changed: external_id=$externalId, system_id=$systemDoctorId");

                        $logMessage = date('Y-m-d H:i:s') . " - FAILED: external_id=$externalId, system_id=$systemDoctorId\n";
                        file_put_contents(__DIR__ . '/../../mapping_debug.log', $logMessage, FILE_APPEND);
                    }
                } else {
                    $errorCount++;
                    error_log("DoctorMapping - Failed to map doctor: external_id=$externalId, system_id=$systemDoctorId");
                }
            }
            
            // Usuń zmapowanych lekarzy z pliku unmapped_doctors.json
            $logMessage = date('Y-m-d H:i:s') . " - Doctors to remove: " . json_encode($doctorsToRemove) . "\n";
            file_put_contents(__DIR__ . '/../../mapping_debug.log', $logMessage, FILE_APPEND);

            if (!empty($doctorsToRemove) && !empty($unmappedDoctorsData['unmapped_doctors'])) {
                $updatedUnmappedDoctors = array_filter(
                    $unmappedDoctorsData['unmapped_doctors'],
                    function($doctor) use ($doctorsToRemove) {
                        return !in_array($doctor['external_id'], $doctorsToRemove);
                    }
                );
                
                // Zaktualizuj plik
                $unmappedDoctorsData['unmapped_doctors'] = array_values($updatedUnmappedDoctors);
                $unmappedDoctorsData['total_count'] = count($updatedUnmappedDoctors);
                $unmappedDoctorsData['last_updated'] = date('Y-m-d H:i:s');
                
                $jsonData = json_encode($unmappedDoctorsData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                error_log("DoctorMapping - Updating unmapped doctors file, doctors to remove: " . json_encode($doctorsToRemove));
                
                $fileResult = file_put_contents(
                    $unmappedDoctorsFile,
                    $jsonData
                );
                
                if ($fileResult === false) {
                    error_log("DoctorMapping - Failed to update unmapped doctors file");
                } else {
                    error_log("DoctorMapping - Successfully updated unmapped doctors file, bytes written: $fileResult");
                }
            }

            // Logowanie końcowych statystyk
            $logMessage = date('Y-m-d H:i:s') . " - Final stats: successCount=$successCount, errorCount=$errorCount\n";
            file_put_contents(__DIR__ . '/../../mapping_debug.log', $logMessage, FILE_APPEND);

            if ($successCount > 0) {
                $message = "Zmapowano pomyślnie {$successCount} lekarzy" . ($errorCount > 0 ? " ({$errorCount} błędów)" : "");
                $_SESSION['success'] = $message;

                // Logowanie do pliku
                $logMessage = date('Y-m-d H:i:s') . " - Setting success message: $message\n";
                $logMessage .= date('Y-m-d H:i:s') . " - Session ID: " . session_id() . "\n";
                file_put_contents(__DIR__ . '/../../mapping_debug.log', $logMessage, FILE_APPEND);

                error_log("DoctorMapping - Setting success message: $message");
                error_log("DoctorMapping - Session ID: " . session_id());
                header('Location: /admin/doctors/map-import');
            } else {
                $_SESSION['error'] = 'Nie zmapowano żadnego lekarza';

                // Logowanie do pliku
                $logMessage = date('Y-m-d H:i:s') . " - Setting error message: Nie zmapowano żadnego lekarza\n";
                file_put_contents(__DIR__ . '/../../mapping_debug.log', $logMessage, FILE_APPEND);

                error_log("DoctorMapping - Setting error message");
                header('Location: /admin/doctors/map-import');
            }
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas mapowania lekarzy: ' . $e->getMessage();
            header('Location: /admin/doctors/map-import');
        }
        exit;
    }

    /**
     * Usuń mapowanie lekarza (wyczyść external_doctor_id i external_doctor_name)
     */
    public function removeMappings() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /admin/doctors/map-import');
            exit;
        }

        $doctorIds = $_POST['doctor_ids'] ?? [];

        if (empty($doctorIds) || !is_array($doctorIds)) {
            $_SESSION['error'] = 'Nie wybrano lekarzy do usunięcia mapowania';
            header('Location: /admin/doctors/map-import');
            exit;
        }

        $successCount = 0;
        $errorCount = 0;

        try {
            foreach ($doctorIds as $doctorId) {
                $doctorId = (int)$doctorId;
                if ($doctorId <= 0) {
                    $errorCount++;
                    continue;
                }

                // Usuń mapowanie (wyczyść external_doctor_id i external_doctor_name)
                $sql = "UPDATE queue_doctors
                        SET external_doctor_id = NULL,
                            external_doctor_name = NULL,
                            external_last_sync = NULL,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?";

                $result = $this->db->execute($sql, [$doctorId]);

                if ($result) {
                    $successCount++;
                    error_log("DoctorMapping - Removed mapping for doctor ID: $doctorId");
                } else {
                    $errorCount++;
                    error_log("DoctorMapping - Failed to remove mapping for doctor ID: $doctorId");
                }
            }

            if ($successCount > 0) {
                $_SESSION['success'] = "Usunięto mapowanie dla {$successCount} lekarzy" . ($errorCount > 0 ? " ({$errorCount} błędów)" : "");
            } else {
                $_SESSION['error'] = 'Nie udało się usunąć żadnego mapowania';
            }

        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas usuwania mapowań: ' . $e->getMessage();
            error_log("DoctorMapping - Error removing mappings: " . $e->getMessage());
        }

        header('Location: /admin/doctors/map-import');
        exit;
    }

    /**
     * Szybkie dodanie lekarza z danych zewnętrznych
     */
    public function quickAddDoctor() {
        // Dodaj logowanie na początku metody
        $logFile = __DIR__ . '/../../mapping_debug.log';
        $logMessage = date('Y-m-d H:i:s') . " - QuickAddDoctor - Method called\n";
        $logMessage .= date('Y-m-d H:i:s') . " - REQUEST_METHOD: " . ($_SERVER['REQUEST_METHOD'] ?? 'UNDEFINED') . "\n";
        $logMessage .= date('Y-m-d H:i:s') . " - POST data: " . json_encode($_POST) . "\n";
        $logMessage .= date('Y-m-d H:i:s') . " - Session ID: " . session_id() . "\n";
        $logMessage .= date('Y-m-d H:i:s') . " - Current timestamp: " . time() . "\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND);

        error_log("QuickAddDoctor - Method called");
        error_log("QuickAddDoctor - REQUEST_METHOD: " . ($_SERVER['REQUEST_METHOD'] ?? 'UNDEFINED'));
        error_log("QuickAddDoctor - POST data: " . json_encode($_POST));

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            error_log("QuickAddDoctor - Not POST request, redirecting");
            header('Location: /admin/doctors/map-import');
            exit;
        }

        $externalId = $_POST['external_id'] ?? '';
        error_log("QuickAddDoctor - Extracted external_id: " . $externalId);
        
        $logMessage = date('Y-m-d H:i:s') . " - QuickAddDoctor - Extracted external_id: $externalId\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND);

        if (empty($externalId)) {
            $_SESSION['error'] = 'Nie podano ID zewnętrznego lekarza';
            header('Location: /admin/doctors/map-import');
            exit;
        }

        error_log("QuickAddDoctor - Starting try block");
        
        try {
            error_log("QuickAddDoctor - Loading unmapped doctors file");
            // Wczytaj dane z pliku unmapped_doctors.json
            $cacheDir = __DIR__ . '/../../api/v2/cache';
            $unmappedDoctorsFile = $cacheDir . '/unmapped_doctors.json';
            $unmappedDoctorsData = [];

            if (file_exists($unmappedDoctorsFile)) {
                $jsonContent = file_get_contents($unmappedDoctorsFile);
                if ($jsonContent !== false) {
                    $unmappedDoctorsData = json_decode($jsonContent, true) ?: [];
                }
            }
            error_log("QuickAddDoctor - Found " . count($unmappedDoctorsData['unmapped_doctors'] ?? []) . " unmapped doctors");

            // Znajdź dane lekarza
            $doctorData = null;
            foreach ($unmappedDoctorsData['unmapped_doctors'] ?? [] as $doctor) {
                if ((string)$doctor['external_id'] === (string)$externalId) {
                    $doctorData = $doctor;
                    break;
                }
            }

            if (!$doctorData) {
                error_log("QuickAddDoctor - Doctor not found in unmapped file: $externalId");
                $_SESSION['error'] = 'Nie znaleziono danych lekarza o ID: ' . $externalId;
                header('Location: /admin/doctors/map-import');
                exit;
            }
            error_log("QuickAddDoctor - Found doctor: " . $doctorData['name']);

            // Parsuj nazwę lekarza
            $fullName = $doctorData['name'];
            $nameParts = $this->parseExternalDoctorName($fullName);
            error_log("QuickAddDoctor - Parsed name: {$nameParts['first_name']} {$nameParts['last_name']} (specialization: {$nameParts['specialization']})");

            // Wygeneruj unikalny kod dostępu
            $accessCode = $this->generateUniqueAccessCode();
            error_log("QuickAddDoctor - Generated access code: $accessCode");

            // Dodaj lekarza do bazy
            $sql = "INSERT INTO queue_doctors (
                        first_name, last_name, specialization,
                        external_doctor_id, external_doctor_name, external_last_sync,
                        access_code, active, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)";

            error_log("QuickAddDoctor - Executing SQL: $sql");
            error_log("QuickAddDoctor - SQL params: " . json_encode([
                $nameParts['first_name'],
                $nameParts['last_name'],
                $nameParts['specialization'],
                $externalId,
                $fullName,
                $accessCode
            ]));

            $result = $this->db->execute($sql, [
                $nameParts['first_name'],
                $nameParts['last_name'],
                $nameParts['specialization'],
                $externalId,
                $fullName,
                $accessCode
            ]);

            if ($result) {
                error_log("QuickAddDoctor - Successfully inserted doctor into database");
                // Usuń lekarza z pliku unmapped_doctors.json
                $this->removeFromUnmappedFile($externalId, $unmappedDoctorsData, $unmappedDoctorsFile);

                $_SESSION['success'] = "Dodano lekarza: {$nameParts['first_name']} {$nameParts['last_name']} i automatycznie zmapowano";
                error_log("QuickAdd - Successfully added doctor: {$nameParts['first_name']} {$nameParts['last_name']} (external_id: $externalId)");
            } else {
                error_log("QuickAddDoctor - Failed to insert doctor into database");
                $_SESSION['error'] = 'Błąd podczas dodawania lekarza do bazy danych';
            }

        } catch (Exception $e) {
            error_log("QuickAddDoctor - Exception: " . $e->getMessage());
            error_log("QuickAddDoctor - Exception trace: " . $e->getTraceAsString());
            $_SESSION['error'] = 'Błąd podczas dodawania lekarza: ' . $e->getMessage();
            error_log("QuickAdd - Error: " . $e->getMessage());
        }

        header('Location: /admin/doctors/map-import');
        exit;
    }

    /**
     * Parsuj nazwę zewnętrznego lekarza na części
     */
    private function parseExternalDoctorName($fullName) {
        // Przykłady nazw:
        // "kardiolog dr hab. n.med. Anna Kowalska"
        // "neurolog dr n.med. Piotr Nowak"
        // "ortopeda dr Marek Wiśniewski"

        $specialization = '';
        $firstName = '';
        $lastName = '';

        // Usuń tytuły naukowe i znajdź specjalizację
        $cleanName = $fullName;

        // Lista specjalizacji do rozpoznania
        $specializations = [
            'kardiolog', 'neurolog', 'ortopeda', 'pediatra', 'psychiatra',
            'ginekolog', 'dermatolog', 'okulista', 'laryngolog', 'urolog',
            'onkolog', 'endokrynolog', 'gastroenterolog', 'pulmonolog',
            'reumatolog', 'nefrolog', 'hematolog', 'genetyk'
        ];

        foreach ($specializations as $spec) {
            if (stripos($cleanName, $spec) !== false) {
                $specialization = ucfirst($spec);
                break;
            }
        }

        // Usuń tytuły i specjalizację, zostaw tylko imię i nazwisko
        $patterns = [
            '/^(kardiolog|neurolog|ortopeda|pediatra|psychiatra|ginekolog|dermatolog|okulista|laryngolog|urolog|onkolog|endokrynolog|gastroenterolog|pulmonolog|reumatolog|nefrolog|hematolog|genetyk)\s+/i',
            '/\s*(dr\.?\s*hab\.?\s*n\.?\s*med\.?|dr\.?\s*n\.?\s*med\.?|dr\.?\s*hab\.?|dr\.?)\s*/i'
        ];

        foreach ($patterns as $pattern) {
            $cleanName = preg_replace($pattern, ' ', $cleanName);
        }

        // Wyczyść dodatkowe spacje
        $cleanName = trim(preg_replace('/\s+/', ' ', $cleanName));

        // Podziel na imię i nazwisko
        $nameParts = explode(' ', $cleanName);
        if (count($nameParts) >= 2) {
            $firstName = $nameParts[0];
            $lastName = implode(' ', array_slice($nameParts, 1));
        } else {
            // Fallback - jeśli nie można sparsować
            $firstName = 'Lekarz';
            $lastName = $cleanName ?: 'Zewnętrzny';
        }

        return [
            'first_name' => $firstName,
            'last_name' => $lastName,
            'specialization' => $specialization
        ];
    }

    /**
     * Wygeneruj unikalny kod dostępu
     */
    private function generateUniqueAccessCode() {
        do {
            $code = str_pad(rand(100000, 999999), 6, '0', STR_PAD_LEFT);
            $exists = $this->db->fetchOne("SELECT id FROM queue_doctors WHERE access_code = ?", [$code]);
        } while ($exists);

        return $code;
    }

    /**
     * Usuń lekarza z pliku unmapped_doctors.json
     */
    private function removeFromUnmappedFile($externalId, $unmappedDoctorsData, $unmappedDoctorsFile) {
        error_log("removeFromUnmappedFile - Removing doctor: $externalId");
        error_log("removeFromUnmappedFile - Before removal, count: " . count($unmappedDoctorsData['unmapped_doctors'] ?? []));
        
        $filteredDoctors = [];
        foreach ($unmappedDoctorsData['unmapped_doctors'] ?? [] as $doctor) {
            if ((string)$doctor['external_id'] !== (string)$externalId) {
                $filteredDoctors[] = $doctor;
            } else {
                error_log("removeFromUnmappedFile - Found and removed: " . $doctor['name']);
            }
        }

        $updatedData = [
            'last_updated' => date('Y-m-d H:i:s'),
            'total_count' => count($filteredDoctors),
            'unmapped_doctors' => $filteredDoctors
        ];

        error_log("removeFromUnmappedFile - After removal, count: " . count($filteredDoctors));
        
        $jsonData = json_encode($updatedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        $result = file_put_contents($unmappedDoctorsFile, $jsonData);
        
        error_log("removeFromUnmappedFile - Write result: " . ($result !== false ? 'SUCCESS (' . $result . ' bytes)' : 'FAILED'));
        
        if ($result === false) {
            error_log("removeFromUnmappedFile - Failed to write to file: $unmappedDoctorsFile");
            error_log("removeFromUnmappedFile - File exists: " . (file_exists($unmappedDoctorsFile) ? 'YES' : 'NO'));
            error_log("removeFromUnmappedFile - File writable: " . (is_writable($unmappedDoctorsFile) ? 'YES' : 'NO'));
        }
    }

    /**
     * Renderuj widok
     */
    private function render($view, $data = []) {
        extract($data);
        
        // Przekaż komunikaty sesji do widoku
        $error = $_SESSION['error'] ?? null;
        $success = $_SESSION['success'] ?? null;
        
        // Wyczyść komunikaty sesji
        unset($_SESSION['error']);
        unset($_SESSION['success']);

        ob_start();
        include ADMIN_PATH . '/views/' . $view . '.php';
        $content = ob_get_clean();

        include ADMIN_PATH . '/views/layout.php';
    }
}
