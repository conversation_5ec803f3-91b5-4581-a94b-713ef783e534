<?php

/**
 * SettingsController - zarządzanie ustawieniami systemu
 */
class SettingsController {
    private $db;
    private $importSettings;
    private $user;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->importSettings = new ImportSettings();
        $this->user = new User();
    }

    /**
     * Panel ustawień
     */
    public function index() {
        $success = null;
        $error = null;

        // Obsługa formularza
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';

            switch ($action) {
                case 'update_general':
                    $result = $this->updateGeneralSettings();
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = $result['message'];
                    }
                    break;

                case 'update_status_settings':
                    $result = $this->updateStatusSettings();
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = $result['message'];
                    }
                    break;

                case 'update_import_settings':
                    $result = $this->updateImportSettings();
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = $result['message'];
                    }
                    break;

                case 'create_user':
                    $result = $this->createUser();
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = implode(', ', $result['errors'] ?? [$result['message']]);
                    }
                    break;

                case 'update_user':
                    $result = $this->updateUser();
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = implode(', ', $result['errors'] ?? [$result['message']]);
                    }
                    break;

                case 'delete_user':
                    $result = $this->deleteUser();
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = $result['message'];
                    }
                    break;

                case 'clear_cache':
                    $result = $this->clearCache();
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = $result['message'];
                    }
                    break;
            }
        }

        // Pobierz aktualne ustawienia
        $settings = $this->getCurrentSettings();
        
        // Pobierz listę użytkowników
        $users = $this->user->getAll();

        $this->render('settings/index', [
            'settings' => $settings,
            'users' => $users,
            'success' => $success,
            'error' => $error,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Aktualizuj ustawienia ogólne
     */
    private function updateGeneralSettings() {
        try {
            $defaultAppointmentDuration = (int)($_POST['default_appointment_duration'] ?? 15);

            if ($defaultAppointmentDuration < 5 || $defaultAppointmentDuration > 120) {
                return ['success' => false, 'message' => 'Czas wizyty musi być między 5 a 120 minut'];
            }

            // Zapisz ustawienie do bazy danych
            $this->saveSetting('default_appointment_duration', $defaultAppointmentDuration);

            return ['success' => true, 'message' => 'Ustawienia ogólne zostały zaktualizowane'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Błąd podczas zapisywania ustawień: ' . $e->getMessage()];
        }
    }


    /**
     * Aktualizuj ustawienia statusów
     */
    private function updateStatusSettings() {
        try {
            $enableAppointmentConfirmation = isset($_POST['enable_appointment_confirmation']) ? 1 : 0;
            $enableAttendanceConfirmation = isset($_POST['enable_attendance_confirmation']) ? 1 : 0;
            $enableSmsSending = isset($_POST['enable_sms_sending']) ? 1 : 0;

            // Zapisz ustawienia statusów do bazy danych
            $this->saveSetting('enable_appointment_confirmation', $enableAppointmentConfirmation);
            $this->saveSetting('enable_attendance_confirmation', $enableAttendanceConfirmation);
            $this->saveSetting('enable_sms_sending', $enableSmsSending);

            return ['success' => true, 'message' => 'Ustawienia statusów zostały zaktualizowane'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Błąd podczas zapisywania ustawień statusów: ' . $e->getMessage()];
        }
    }

    /**
     * Aktualizuj ustawienia importu
     */
    private function updateImportSettings() {
        try {
            $importEnabled = isset($_POST['import_enabled']) ? 1 : 0;
            $importSource = $_POST['import_source'] ?? 'default';

            // Walidacja źródła importu
            if (!$this->importSettings->isValidSource($importSource)) {
                return ['success' => false, 'message' => 'Nieprawidłowe źródło importu'];
            }

            // Zapisz ustawienia importu
            $result = $this->importSettings->updateSettings([
                'import_enabled' => $importEnabled,
                'import_source' => $importSource
            ]);

            if ($result) {
                return ['success' => true, 'message' => 'Ustawienia importu zostały zaktualizowane'];
            } else {
                return ['success' => false, 'message' => 'Błąd podczas zapisywania ustawień importu'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Błąd podczas zapisywania ustawień importu: ' . $e->getMessage()];
        }
    }



    /**
     * Zapisz ustawienie do bazy danych
     */
    private function saveSetting($key, $value) {
        // Sprawdź czy ustawienie już istnieje
        $existing = $this->db->fetchOne("SELECT key FROM settings WHERE key = ?", [$key]);

        if ($existing) {
            // Aktualizuj istniejące ustawienie
            $sql = "UPDATE settings SET value = ?, updated_at = datetime('now') WHERE key = ?";
            return $this->db->execute($sql, [$value, $key]);
        } else {
            // Wstaw nowe ustawienie
            $sql = "INSERT INTO settings (key, value, created_at, updated_at) VALUES (?, ?, datetime('now'), datetime('now'))";
            return $this->db->execute($sql, [$key, $value]);
        }
    }

    /**
     * Pobierz ustawienie z bazy danych
     */
    private function getSetting($key, $defaultValue = null) {
        $sql = "SELECT value FROM settings WHERE key = ?";
        $result = $this->db->fetchOne($sql, [$key]);

        return $result ? $result['value'] : $defaultValue;
    }

    /**
     * Wyczyść cache
     */
    private function clearCache() {
        try {
            // Tutaj można dodać logikę czyszczenia cache
            // Na przykład usunięcie plików tymczasowych, cache'u sesji itp.

            return ['success' => true, 'message' => 'Cache został wyczyszczony'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Błąd podczas czyszczenia cache: ' . $e->getMessage()];
        }
    }

    /**
     * Pobierz aktualne ustawienia
     */
    public function getCurrentSettings() {
        return [
            'default_appointment_duration' => (int)($this->getSetting('default_appointment_duration', 15)),
            'enable_appointment_confirmation' => (bool)($this->getSetting('enable_appointment_confirmation', 1)),
            'enable_attendance_confirmation' => (bool)($this->getSetting('enable_attendance_confirmation', 1)),
            'enable_sms_sending' => (bool)($this->getSetting('enable_sms_sending', 1)),

            // Ustawienia importu
            'import_enabled' => $this->importSettings->isImportEnabled(),
            'import_source' => $this->importSettings->getImportSource(),
            'import_sources' => $this->importSettings->getAvailableSources(),
            // Informacje o systemie
            'system_version' => '4.0',
            'database_size' => $this->getDatabaseSize(),
            'total_appointments' => $this->getTotalAppointments(),
            'total_doctors' => $this->getTotalDoctors(),
            // Statystyki video
            'video_stats' => $this->getVideoStats(),
            'total_video_views' => $this->getTotalVideoViews(),
            'active_displays' => $this->getActiveDisplaysCount()
        ];
    }

    /**
     * Pobierz rozmiar bazy danych
     */
    private function getDatabaseSize() {
        try {
            $dbPath = __DIR__ . '/../../db/reklama.db';
            if (file_exists($dbPath)) {
                $size = filesize($dbPath);
                return $this->formatBytes($size);
            }
            return 'Nieznany';
        } catch (Exception $e) {
            return 'Błąd';
        }
    }

    /**
     * Pobierz liczbę wizyt
     */
    private function getTotalAppointments() {
        try {
            return $this->db->fetchCount("SELECT COUNT(*) FROM queue_appointments");
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Pobierz liczbę lekarzy
     */
    private function getTotalDoctors() {
        try {
            return $this->db->fetchCount("SELECT COUNT(*) FROM queue_doctors WHERE active = 1");
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Formatuj bajty na czytelny format
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Pobierz statystyki materiałów video
     */
    private function getVideoStats() {
        try {
            $video = new Video();
            return $video->getStats();
        } catch (Exception $e) {
            error_log("Błąd pobierania statystyk video: " . $e->getMessage());
            return [
                'total' => 0,
                'approved' => 0,
                'pending' => 0,
                'rejected' => 0
            ];
        }
    }

    /**
     * Pobierz całkowitą liczbę wyświetleń materiałów video
     */
    private function getTotalVideoViews() {
        try {
            // Pobierz sumę wyświetleń z tabeli ads (kolumna ads_views)
            $result = $this->db->fetchOne("SELECT SUM(ads_views) as total_views FROM ads");
            return $result['total_views'] ?? 0;
        } catch (Exception $e) {
            error_log("Błąd pobierania liczby wyświetleń video: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Pobierz liczbę aktywnych wyświetlaczy
     */
    private function getActiveDisplaysCount() {
        try {
            return $this->db->fetchCount("SELECT COUNT(*) FROM client_displays WHERE is_online = 1");
        } catch (Exception $e) {
            error_log("Błąd pobierania liczby aktywnych wyświetlaczy: " . $e->getMessage());
            return 0;
        }
    }


    /**
     * Utwórz nowego użytkownika
     */
    private function createUser() {
        try {
            $userData = [
                'username' => trim($_POST['username'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'password' => $_POST['password'] ?? ''
            ];
            
            return $this->user->create($userData);
        } catch (Exception $e) {
            error_log("Błąd podczas tworzenia użytkownika: " . $e->getMessage());
            return ['success' => false, 'message' => 'Wystąpił błąd serwera'];
        }
    }

    /**
     * Aktualizuj użytkownika
     */
    private function updateUser() {
        try {
            $userId = (int)($_POST['user_id'] ?? 0);
            $userData = [
                'username' => trim($_POST['username'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'password' => $_POST['password'] ?? ''
            ];
            
            return $this->user->update($userId, $userData);
        } catch (Exception $e) {
            error_log("Błąd podczas aktualizacji użytkownika: " . $e->getMessage());
            return ['success' => false, 'message' => 'Wystąpił błąd serwera'];
        }
    }

    /**
     * Usuń użytkownika
     */
    private function deleteUser() {
        try {
            $userId = (int)($_POST['user_id'] ?? 0);
            
            return $this->user->delete($userId);
        } catch (Exception $e) {
            error_log("Błąd podczas usuwania użytkownika: " . $e->getMessage());
            return ['success' => false, 'message' => 'Wystąpił błąd serwera'];
        }
    }

    /**
     * Renderuj widok
     */
    private function render($view, $data = []) {
        extract($data);

        ob_start();
        include ADMIN_PATH . '/views/' . $view . '.php';
        $content = ob_get_clean();

        include ADMIN_PATH . '/views/layout.php';
    }
}
