<?php

/**
 * RoomController - zarządzanie gabinetami
 */
class RoomController {
    private $db;
    private $roomModel;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->roomModel = new Room();
    }

    /**
     * Lista gabinetów
     */
    public function index() {
        $rooms = $this->roomModel->getAll();

        $this->render('rooms/index', [
            'rooms' => $rooms,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Formularz dodawania gabinetu
     */
    public function create() {
        $this->render('rooms/create', [
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Zapisz nowy gabinet
     */
    public function store() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /admin/rooms');
            exit;
        }

        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');

        if (empty($name)) {
            $_SESSION['error'] = 'Nazwa gabinetu jest wymagana';
            header('Location: /admin/rooms/create');
            exit;
        }

        try {
            $this->roomModel->create($name, $description);
            $_SESSION['success'] = 'Gabinet został dodany pomyślnie';
            header('Location: /admin/rooms');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas dodawania gabinetu: ' . $e->getMessage();
            header('Location: /admin/rooms/create');
        }
        exit;
    }

    /**
     * Formularz edycji gabinetu
     */
    public function edit() {
        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/rooms');
            exit;
        }

        $room = $this->roomModel->getById($id);
        if (!$room) {
            $_SESSION['error'] = 'Gabinet nie został znaleziony';
            header('Location: /admin/rooms');
            exit;
        }

        // Pobierz lekarzy przypisanych do tego gabinetu
        $doctors = $this->roomModel->getDoctors($id);

        $this->render('rooms/edit', [
            'room' => $room,
            'doctors' => $doctors,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Aktualizuj gabinet
     */
    public function update() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /admin/rooms');
            exit;
        }

        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/rooms');
            exit;
        }

        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');

        if (empty($name)) {
            $_SESSION['error'] = 'Nazwa gabinetu jest wymagana';
            header('Location: /admin/rooms/edit/' . $id);
            exit;
        }

        try {
            $this->roomModel->update($id, $name, $description);
            $_SESSION['success'] = 'Gabinet został zaktualizowany pomyślnie';
            header('Location: /admin/rooms');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas aktualizacji gabinetu: ' . $e->getMessage();
            header('Location: /admin/rooms/edit/' . $id);
        }
        exit;
    }

    /**
     * Usuń gabinet
     */
    public function delete() {
        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/rooms');
            exit;
        }

        try {
            $this->roomModel->delete($id);
            $_SESSION['success'] = 'Gabinet został usunięty pomyślnie';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas usuwania gabinetu: ' . $e->getMessage();
        }

        header('Location: /admin/rooms');
        exit;
    }

    /**
     * Pokaż szczegóły gabinetu
     */
    public function show() {
        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/rooms');
            exit;
        }

        $room = $this->roomModel->getById($id);
        if (!$room) {
            $_SESSION['error'] = 'Gabinet nie został znaleziony';
            header('Location: /admin/rooms');
            exit;
        }

        // Pobierz lekarzy przypisanych do tego gabinetu
        $doctors = $this->roomModel->getDoctors($id);

        // Pobierz statystyki gabinetu
        $stats = $this->roomModel->getStats($id);

        $this->render('rooms/show', [
            'room' => $room,
            'doctors' => $doctors,
            'stats' => $stats,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * API - statystyki gabinetu
     */
    public function getStats() {
        $roomId = $_GET['room_id'] ?? null;
        $date = $_GET['date'] ?? date('Y-m-d');

        if (!$roomId) {
            http_response_code(400);
            echo json_encode(['error' => 'Brak ID gabinetu']);
            exit;
        }

        try {
            $stats = $this->roomModel->getStats($roomId, $date);
            echo json_encode(['success' => true, 'stats' => $stats]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        exit;
    }

    /**
     * Renderuj widok
     */
    private function render($view, $data = []) {
        extract($data);

        ob_start();
        include ADMIN_PATH . '/views/' . $view . '.php';
        $content = ob_get_clean();

        include ADMIN_PATH . '/views/layout.php';
    }
}