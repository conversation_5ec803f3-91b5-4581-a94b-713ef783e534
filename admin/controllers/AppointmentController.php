<?php

/**
 * AppointmentController - zarząd<PERSON>ie wizytami
 */
class AppointmentController {
    private $db;
    private $appointmentModel;
    private $doctorModel;
    private $settingsController;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->appointmentModel = new Appointment();
        $this->doctorModel = new Doctor();
        $this->settingsController = new SettingsController();
    }

    /**
     * Formularz dodawania nowej wizyty
     */
    public function create() {
        $selectedDate = $_GET['date'] ?? $_SESSION['selected_date'] ?? date('Y-m-d');
        $selectedDoctor = $_GET['doctor'] ?? null;

        // Pobierz lekarzy
        $doctors = $this->doctorModel->getAllForClient();

        $error = null;
        $success = null;

        // Obsługa formularza
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $doctorId = $_POST['doctor_id'] ?? null;
            $patientName = trim($_POST['patient_name'] ?? '');
            $appointmentTime = $_POST['appointment_time'] ?? '';
            $appointmentDate = $_POST['appointment_date'] ?? $selectedDate;
            $phoneNumber = trim($_POST['phone_number'] ?? '');
            $appointmentDuration = intval($_POST['appointment_duration'] ?? 20);
            $externalId = trim($_POST['external_id'] ?? '');

            // Walidacja
            if (empty($doctorId) || empty($patientName) || empty($appointmentTime) || empty($appointmentDate)) {
                $error = 'Wszystkie pola są wymagane';
            } else {
                try {
                    $appointmentId = $this->appointmentModel->create($doctorId, $patientName, $appointmentTime, $appointmentDate, $phoneNumber, $appointmentDuration, $externalId);
                    $success = 'Wizyta została dodana pomyślnie';

                    // Przekieruj na pulpit po sukcesie
                    redirect('/admin/pulpit?date=' . $appointmentDate);
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
            }
        }

        $this->render('appointment_create', [
            'doctors' => $doctors,
            'selectedDate' => $selectedDate,
            'selectedDoctor' => $selectedDoctor,
            'error' => $error,
            'success' => $success,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Zapisz nową wizytę (POST)
     */
    public function store() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            redirect('/admin/appointments/create');
        }

        $this->create(); // Obsługa w metodzie create
    }

    /**
     * Formularz edycji wizyty
     */
    public function edit() {
        $id = $_GET['id'] ?? null;

        if (!$id) {
            redirect('/admin/pulpit');
        }

        // Pobierz wizytę
        $appointment = $this->appointmentModel->getById($id);
        if (!$appointment) {
            redirect('/admin/pulpit');
        }

        // Pobierz lekarzy
        $doctors = $this->doctorModel->getAllForClient();

        $error = null;
        $success = null;

        // Obsługa formularza
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $doctorId = $_POST['doctor_id'] ?? null;
            $patientName = trim($_POST['patient_name'] ?? '');
            $appointmentTime = $_POST['appointment_time'] ?? '';
            $appointmentDate = $_POST['appointment_date'] ?? '';
            $status = $_POST['status'] ?? 'waiting';
            $isConfirmed = isset($_POST['is_confirmed']) ? 1 : 0;
            $smsSent = isset($_POST['is_sms_sent']) ? 1 : 0;
            $isPatientPresent = isset($_POST['is_patient_present']) ? 1 : 0;
            $isCompleted = isset($_POST['is_completed']) ? 1 : 0;
            $phoneNumber = trim($_POST['phone_number'] ?? '');
            $appointmentDuration = intval($_POST['appointment_duration'] ?? 20);
            $externalId = trim($_POST['external_id'] ?? '');

            // Walidacja
            if (empty($doctorId) || empty($patientName) || empty($appointmentTime) || empty($appointmentDate)) {
                $error = 'Wszystkie pola są wymagane';
            } else {
                try {
                    $this->appointmentModel->update($id, $patientName, $appointmentTime, $appointmentDate, $doctorId, $status, $isConfirmed, $smsSent, $isPatientPresent, $phoneNumber, $appointmentDuration, $externalId, $isCompleted);
                    $success = 'Wizyta została zaktualizowana pomyślnie';

                    // Przekieruj na dashboard po sukcesie
                    redirect('/admin/dashboard?date=' . $appointmentDate);
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
            }
        }

        $this->render('appointment_edit', [
            'appointment' => $appointment,
            'doctors' => $doctors,
            'error' => $error,
            'success' => $success,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Aktualizuj wizytę (POST)
     */
    public function update() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $id = $_GET['id'] ?? null;
            if ($id) {
                redirect('/admin/appointments/edit/' . $id);
            } else {
                redirect('/admin/dashboard');
            }
        }

        $this->edit(); // Obsługa w metodzie edit
    }

    /**
     * Usuń wizytę
     */
    public function delete() {
        $id = $_GET['id'] ?? null;

        if (!$id) {
            redirect('/admin/dashboard');
        }

        // Pobierz wizytę dla sprawdzenia
        $appointment = $this->appointmentModel->getById($id);
        if (!$appointment) {
            redirect('/admin/dashboard');
        }

        // Obsługa potwierdzenia usunięcia
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $this->appointmentModel->delete($id);
                redirect('/admin/dashboard?date=' . $appointment['appointment_date']);
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }

        $this->render('appointment_delete', [
            'appointment' => $appointment,
            'error' => $error ?? null,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Generuj sugerowane godziny wizyt
     */
    private function generateTimeSlots($doctorId, $date) {
        $slots = [];
        $startHour = 8;
        $endHour = 18;
        
        // Pobierz domyślny czas trwania wizyty z ustawień
        $settings = $this->settingsController->getCurrentSettings();
        $interval = $settings['default_appointment_duration'] ?? 15; // minuty

        // Pobierz zajęte godziny
        $existingAppointments = $this->appointmentModel->getForDoctor($doctorId, $date);
        $occupiedTimes = array_column($existingAppointments, 'appointment_time');

        // Generuj sloty
        for ($hour = $startHour; $hour < $endHour; $hour++) {
            for ($minute = 0; $minute < 60; $minute += $interval) {
                $time = sprintf('%02d:%02d:00', $hour, $minute);
                $timeDisplay = sprintf('%02d:%02d', $hour, $minute);

                $slots[] = [
                    'time' => $time,
                    'display' => $timeDisplay,
                    'occupied' => in_array($time, $occupiedTimes)
                ];
            }
        }

        return $slots;
    }

    /**
     * Pobierz szczegóły wizyty (AJAX)
     */
    public function details() {
        header('Content-Type: application/json');

        $appointmentId = $_GET['id'] ?? null;

        if (!$appointmentId) {
            echo json_encode(['success' => false, 'message' => 'Brak ID wizyty']);
            return;
        }

        try {
            $appointment = $this->appointmentModel->getById($appointmentId);

            if (!$appointment) {
                echo json_encode(['success' => false, 'message' => 'Wizyta nie została znaleziona']);
                exit;
            }

            // Dodaj informacje o lekarzu
            $doctor = $this->doctorModel->getById($appointment['doctor_id']);
            $appointment['doctor_name'] = $doctor ? $doctor['first_name'] . ' ' . $doctor['last_name'] : 'Nieznany';
            $appointment['doctor_specialization'] = $doctor ? $doctor['specialization'] : '';

            // Dodaj sformatowane informacje
            $appointment['formatted_time'] = formatTime($appointment['appointment_time']);
            $appointment['formatted_date'] = formatDate($appointment['appointment_date']);
            $appointment['delay'] = calculateDelay($appointment['appointment_time'], $appointment['appointment_date'], $appointment['status']);
            $appointment['normalized_patient_name'] = normalizePatientName($appointment['patient_name']);

            // Dodaj statusy tekstowe
            $statusLabels = [
                'waiting' => 'Oczekuje',
                'current' => 'Aktualna',
                'completed' => 'Zakończona',
                'closed' => 'Zakończona'
            ];
            $appointment['status_label'] = $statusLabels[$appointment['status']] ?? 'Nieznany';

            // Dodaj informacje o czasach
            if ($appointment['called_at']) {
                $appointment['formatted_called_at'] = formatDateTime($appointment['called_at']);
            }
            if ($appointment['completed_at']) {
                $appointment['formatted_completed_at'] = formatDateTime($appointment['completed_at']);
            }

            // Upewnij się, że pole is_sms_sent jest dostępne
            if (!isset($appointment['is_sms_sent'])) {
                $appointment['is_sms_sent'] = $appointment['sms_sent'] ?? 0;
            }

            echo json_encode([
                'success' => true,
                'appointment' => $appointment
            ]);
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Błąd podczas pobierania szczegółów wizyty']);
        }
        exit;
    }



    /**
     * Renderuj widok
     */
    private function render($view, $data = []) {
        extract($data);

        ob_start();
        include ADMIN_PATH . '/views/' . $view . '.php';
        $content = ob_get_clean();

        include ADMIN_PATH . '/views/layout.php';
    }
}
