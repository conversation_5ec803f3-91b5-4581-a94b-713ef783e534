<?php

/**
 * Model User - zarząd<PERSON><PERSON> użytkownikami systemu
 */
class User {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Pobierz wszystkich użytkowników
     */
    public function getAll() {
        $sql = "SELECT id, username, email, last_activity, created_at 
                FROM users 
                ORDER BY created_at DESC";
        return $this->db->fetchAll($sql);
    }
    
    /**
     * Pobierz użytkownika po ID
     */
    public function getById($id) {
        $sql = "SELECT id, username, email, last_activity, created_at 
                FROM users 
                WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }
    
    /**
     * Pobierz użytkownika po nazwie użytkownika
     */
    public function getByUsername($username) {
        $sql = "SELECT id, username, email, last_activity, created_at 
                FROM users 
                WHERE username = ?";
        return $this->db->fetchOne($sql, [$username]);
    }
    
    /**
     * <PERSON><PERSON><PERSON> użytkownika po emailu
     */
    public function getByEmail($email) {
        $sql = "SELECT id, username, email, last_activity, created_at 
                FROM users 
                WHERE email = ?";
        return $this->db->fetchOne($sql, [$email]);
    }
    
    /**
     * Utwórz nowego użytkownika
     */
    public function create($data) {
        try {
            // Walidacja danych
            $errors = $this->validateUserData($data, true);
            if (!empty($errors)) {
                return ['success' => false, 'errors' => $errors];
            }
            
            // Sprawdź czy nazwa użytkownika już istnieje
            if ($this->getByUsername($data['username'])) {
                return ['success' => false, 'errors' => ['username' => 'Nazwa użytkownika już istnieje']];
            }
            
            // Sprawdź czy email już istnieje
            if ($this->getByEmail($data['email'])) {
                return ['success' => false, 'errors' => ['email' => 'Email już istnieje']];
            }
            
            // Hash hasła
            $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
            
            // Wstaw nowego użytkownika
            $sql = "INSERT INTO users (username, email, password, created_at) 
                    VALUES (?, ?, ?, datetime('now'))";
            $result = $this->db->execute($sql, [
                $data['username'],
                $data['email'],
                $hashedPassword
            ]);
            
            if ($result) {
                return ['success' => true, 'message' => 'Użytkownik został utworzony', 'id' => $this->db->lastInsertId()];
            } else {
                return ['success' => false, 'errors' => ['general' => 'Błąd podczas tworzenia użytkownika']];
            }
        } catch (Exception $e) {
            error_log("Błąd podczas tworzenia użytkownika: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'Wystąpił błąd serwera']];
        }
    }
    
    /**
     * Aktualizuj dane użytkownika
     */
    public function update($id, $data) {
        try {
            // Walidacja danych
            $errors = $this->validateUserData($data, false);
            if (!empty($errors)) {
                return ['success' => false, 'errors' => $errors];
            }
            
            // Sprawdź czy użytkownik istnieje
            $existingUser = $this->getById($id);
            if (!$existingUser) {
                return ['success' => false, 'errors' => ['general' => 'Użytkownik nie istnieje']];
            }
            
            // Sprawdź czy nazwa użytkownika już istnieje (u innego użytkownika)
            $userWithSameUsername = $this->getByUsername($data['username']);
            if ($userWithSameUsername && $userWithSameUsername['id'] != $id) {
                return ['success' => false, 'errors' => ['username' => 'Nazwa użytkownika już istnieje']];
            }
            
            // Sprawdź czy email już istnieje (u innego użytkownika)
            $userWithSameEmail = $this->getByEmail($data['email']);
            if ($userWithSameEmail && $userWithSameEmail['id'] != $id) {
                return ['success' => false, 'errors' => ['email' => 'Email już istnieje']];
            }
            
            // Przygotuj zapytanie aktualizujące
            $sql = "UPDATE users SET username = ?, email = ?";
            $params = [$data['username'], $data['email']];
            
            // Jeśli podano nowe hasło, dodaj je do aktualizacji
            if (!empty($data['password'])) {
                $sql .= ", password = ?";
                $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
            }
            
            $sql .= " WHERE id = ?";
            $params[] = $id;
            
            $result = $this->db->execute($sql, $params);
            
            if ($result) {
                return ['success' => true, 'message' => 'Dane użytkownika zostały zaktualizowane'];
            } else {
                return ['success' => false, 'errors' => ['general' => 'Błąd podczas aktualizacji użytkownika']];
            }
        } catch (Exception $e) {
            error_log("Błąd podczas aktualizacji użytkownika: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'Wystąpił błąd serwera']];
        }
    }
    
    /**
     * Usuń użytkownika
     */
    public function delete($id) {
        try {
            // Sprawdź czy użytkownik istnieje
            $user = $this->getById($id);
            if (!$user) {
                return ['success' => false, 'message' => 'Użytkownik nie istnieje'];
            }
            
            // Nie pozwól usunąć ostatniego administratora
            $adminCount = $this->db->fetchCount("SELECT COUNT(*) FROM users");
            if ($adminCount <= 1) {
                return ['success' => false, 'message' => 'Nie można usunąć ostatniego użytkownika systemu'];
            }
            
            // Usuń użytkownika
            $sql = "DELETE FROM users WHERE id = ?";
            $result = $this->db->execute($sql, [$id]);
            
            if ($result) {
                return ['success' => true, 'message' => 'Użytkownik został usunięty'];
            } else {
                return ['success' => false, 'message' => 'Błąd podczas usuwania użytkownika'];
            }
        } catch (Exception $e) {
            error_log("Błąd podczas usuwania użytkownika: " . $e->getMessage());
            return ['success' => false, 'message' => 'Wystąpił błąd serwera'];
        }
    }
    
    /**
     * Zaktualizuj ostatnią aktywność użytkownika
     */
    public function updateLastActivity($id) {
        try {
            $sql = "UPDATE users SET last_activity = datetime('now') WHERE id = ?";
            return $this->db->execute($sql, [$id]);
        } catch (Exception $e) {
            error_log("Błąd podczas aktualizacji ostatniej aktywności: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Waliduj dane użytkownika
     */
    private function validateUserData($data, $isNew = false) {
        $errors = [];
        
        // Walidacja nazwy użytkownika
        if (empty($data['username'])) {
            $errors['username'] = 'Nazwa użytkownika jest wymagana';
        } elseif (strlen($data['username']) < 3) {
            $errors['username'] = 'Nazwa użytkownika musi mieć minimum 3 znaki';
        } elseif (strlen($data['username']) > 50) {
            $errors['username'] = 'Nazwa użytkownika może mieć maksymalnie 50 znaków';
        } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
            $errors['username'] = 'Nazwa użytkownika może zawierać tylko litery, cyfry i podkreślniki';
        }
        
        // Walidacja emailu
        if (empty($data['email'])) {
            $errors['email'] = 'Email jest wymagany';
        } elseif (strlen($data['email']) > 100) {
            $errors['email'] = 'Email może mieć maksymalnie 100 znaków';
        } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Nieprawidłowy format email';
        }
        
        // Walidacja hasła (tylko przy tworzeniu nowego użytkownika lub gdy jest podane)
        if ($isNew || !empty($data['password'])) {
            if (empty($data['password'])) {
                $errors['password'] = 'Hasło jest wymagane';
            } elseif (strlen($data['password']) < 8) {
                $errors['password'] = 'Hasło musi mieć minimum 8 znaków';
            }
        }
        
        return $errors;
    }
    
    /**
     * Sprawdź format ostatniej aktywności
     */
    public function formatLastActivity($lastActivity) {
        if (empty($lastActivity)) {
            return 'Nigdy nie logował się';
        }
        
        $timestamp = strtotime($lastActivity);
        $now = time();
        $diff = $now - $timestamp;
        
        if ($diff < 60) {
            return 'Just teraz';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . ' minut temu';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . ' godzin temu';
        } elseif ($diff < 604800) {
            return floor($diff / 86400) . ' dni temu';
        } else {
            return date('d.m.Y H:i', $timestamp);
        }
    }
}