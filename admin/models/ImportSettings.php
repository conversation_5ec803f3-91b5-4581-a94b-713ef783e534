<?php

/**
 * Model ImportSettings - zarządzanie ustawieniami importu
 * Używa istniejącej tabeli settings do przechowywania konfiguracji
 */
class ImportSettings {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * <PERSON><PERSON><PERSON> warto<PERSON> konkretnego ustawienia
     */
    public function getSetting($key, $defaultValue = null) {
        try {
            $sql = "SELECT value FROM settings WHERE key = ?";
            $result = $this->db->fetchOne($sql, [$key]);
            
            // Logowanie dla debugowania
            if ($result) {
                error_log("ImportSettings - Odczytano ustawienie {$key}: {$result['value']}");
            } else {
                error_log("ImportSettings - Brak ustawienia {$key}, użyto wartości domyślnej");
            }
            
            return $result ? $result['value'] : $defaultValue;
        } catch (Exception $e) {
            error_log("ImportSettings - Błąd pobierania ustawienia {$key}: " . $e->getMessage());
            return $defaultValue;
        }
    }

    /**
     * Zapisz ustawienie do bazy danych
     */
    public function saveSetting($key, $value) {
        try {
            // Sprawdź czy ustawienie już istnieje
            $existing = $this->db->fetchOne("SELECT key FROM settings WHERE key = ?", [$key]);

            if ($existing) {
                // Aktualizuj istniejące ustawienie
                $sql = "UPDATE settings SET value = ?, updated_at = datetime('now') WHERE key = ?";
                $params = [$value, $key];
            } else {
                // Wstaw nowe ustawienie
                $sql = "INSERT INTO settings (key, value, created_at, updated_at) VALUES (?, ?, datetime('now'), datetime('now'))";
                $params = [$key, $value];
            }

            $result = $this->db->execute($sql, $params);
            
            // Logowanie dla debugowania
            error_log("ImportSettings - Zapis ustawienia {$key}: " . ($result ? "Sukces" : "Błąd"));
            
            return $result;
        } catch (Exception $e) {
            error_log("ImportSettings - Błąd zapisu ustawienia {$key}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Sprawdź czy import jest włączony
     */
    public function isImportEnabled() {
        return (bool)$this->getSetting('import_enabled', 0);
    }

    /**
     * Pobierz źródło importu
     */
    public function getImportSource() {
        return $this->getSetting('import_source', 'default');
    }

    /**
     * Aktualizuj ustawienia importu
     */
    public function updateSettings($data) {
        try {
            $success = true;
            
            if (isset($data['import_enabled'])) {
                $success = $success && $this->saveSetting('import_enabled', (int)$data['import_enabled']);
            }
            
            if (isset($data['import_source'])) {
                $success = $success && $this->saveSetting('import_source', $data['import_source']);
            }
            
            return $success;
        } catch (Exception $e) {
            error_log("ImportSettings - Błąd aktualizacji ustawień: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Włącz import
     */
    public function enableImport($source = 'default') {
        return $this->updateSettings([
            'import_enabled' => 1,
            'import_source' => $source
        ]);
    }

    /**
     * Wyłącz import
     */
    public function disableImport() {
        return $this->updateSettings([
            'import_enabled' => 0
        ]);
    }

    /**
     * Pobierz dostępne źródła importu
     */
    public function getAvailableSources() {
        return [
            'default' => 'Domyślny',
            'igabinet' => 'iGabinet',
            'dreryk' => 'drEryk',
            'mmedica' => 'mMedica'
        ];
    }

    /**
     * Sprawdź czy źródło importu jest prawidłowe
     */
    public function isValidSource($source) {
        $availableSources = $this->getAvailableSources();
        return array_key_exists($source, $availableSources);
    }

    /**
     * Pobierz mapowania lekarzy dla danego źródła
     */
    public function getDoctorMappings($syncCode = null) {
        try {
            $sql = "
                SELECT qd.id as system_doctor_id, qd.external_doctor_id, qd.external_last_sync as last_seen,
                       qd.first_name, qd.last_name, qd.specialization
                FROM queue_doctors qd
                WHERE qd.external_doctor_id IS NOT NULL AND qd.external_doctor_id != ''
            ";
            
            $params = [];
            
            if ($syncCode) {
                $sql .= " AND qd.external_doctor_id LIKE ?";
                $params[] = "%{$syncCode}%";
            }
            
            $sql .= " ORDER BY qd.external_last_sync DESC";
            
            return $this->db->fetchAll($sql, $params);
        } catch (Exception $e) {
            error_log("ImportSettings - Błąd pobierania mapowań lekarzy: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Zapisz mapowanie lekarza
     */
    public function saveDoctorMapping($externalDoctorId, $systemDoctorId, $syncCode) {
        try {
            // Aktualizuj istniejącego lekarza z external_doctor_id
            $sql = "
                UPDATE queue_doctors
                SET external_doctor_id = ?, external_last_sync = datetime('now', 'localtime')
                WHERE id = ?
            ";
            $params = [$externalDoctorId, $systemDoctorId];
            
            return $this->db->execute($sql, $params);
        } catch (Exception $e) {
            error_log("ImportSettings - Błąd zapisu mapowania lekarza: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Usuń mapowanie lekarza
     */
    public function removeDoctorMapping($externalDoctorId, $syncCode) {
        try {
            $sql = "
                UPDATE queue_doctors
                SET external_doctor_id = NULL, external_last_sync = NULL
                WHERE external_doctor_id = ?
            ";
            
            return $this->db->execute($sql, [$externalDoctorId]);
        } catch (Exception $e) {
            error_log("ImportSettings - Błąd usuwania mapowania lekarza: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Sprawdź czy lekarz z importu jest zmapowany
     */
    public function isDoctorMapped($externalDoctorId, $syncCode) {
        try {
            $sql = "
                SELECT id FROM queue_doctors
                WHERE external_doctor_id = ? AND external_doctor_id IS NOT NULL AND external_doctor_id != ''
            ";
            
            $result = $this->db->fetchOne($sql, [$externalDoctorId]);
            return !empty($result);
        } catch (Exception $e) {
            error_log("ImportSettings - Błąd sprawdzania mapowania lekarza: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Pobierz ID zmapowanego lekarza
     */
    public function getMappedDoctorId($externalDoctorId, $syncCode) {
        try {
            $sql = "
                SELECT id FROM queue_doctors
                WHERE external_doctor_id = ? AND external_doctor_id IS NOT NULL AND external_doctor_id != ''
            ";
            
            $result = $this->db->fetchOne($sql, [$externalDoctorId]);
            return $result ? $result['id'] : null;
        } catch (Exception $e) {
            error_log("ImportSettings - Błąd pobierania ID zmapowanego lekarza: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Utwórz/zaktualizuj zbiorczy plik JSON z niezmapowanymi lekarzami
     */
    public function createUnmappedDoctorsFile($unmappedDoctors, $syncCode, $source = 'default') {
        try {
            // Upewnij się że katalog cache istnieje
            $cacheDir = __DIR__ . '/../../api/v2/cache';
            if (!is_dir($cacheDir)) {
                mkdir($cacheDir, 0755, true);
            }

            // Nazwa stałego pliku
            $filename = "unmapped_doctors.json";
            $filepath = $cacheDir . '/' . $filename;

            // Odczytaj istniejące dane, jeśli plik istnieje
            $existingData = [];
            if (file_exists($filepath)) {
                $content = file_get_contents($filepath);
                if ($content !== false) {
                    $decoded = json_decode($content, true);
                    if (json_last_error() === JSON_ERROR_NONE && isset($decoded['unmapped_doctors'])) {
                        $existingData = $decoded['unmapped_doctors'];
                    }
                }
            }

            // Połącz istniejące dane z nowymi, unikając duplikatów
            $combinedData = $existingData;
            
            // Indeks do szybkiego sprawdzania duplikatów
            $existingIndex = [];
            foreach ($existingData as $doctor) {
                $existingIndex[$doctor['external_id']] = true;
            }

            // Dodaj tylko nowe lekarzy
            foreach ($unmappedDoctors as $doctor) {
                if (!isset($existingIndex[$doctor['external_id']])) {
                    $combinedData[] = [
                        'external_id' => $doctor['external_id'],
                        'name' => $doctor['name'],
                        'added_timestamp' => date('Y-m-d H:i:s')
                    ];
                    $existingIndex[$doctor['external_id']] = true;
                }
            }

            // Przygotuj dane do zapisu
            $data = [
                'last_updated' => date('Y-m-d H:i:s'),
                'total_count' => count($combinedData),
                'unmapped_doctors' => $combinedData
            ];

            // Zapisz plik
            $result = file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            
            if ($result) {
                error_log("ImportSettings - Zaktualizowano plik z niezmapowanymi lekarzami: {$filename} ({$data['total_count']} lekarzy)");
                return $filename;
            } else {
                error_log("ImportSettings - Błąd tworzenia pliku z niezmapowanymi lekarzami");
                return false;
            }
        } catch (Exception $e) {
            error_log("ImportSettings - Błąd tworzenia pliku JSON: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Pobierz informacje o zbiorczym pliku z niezmapowanymi lekarzami
     */
    public function getUnmappedDoctorsFiles() {
        try {
            $cacheDir = __DIR__ . '/../../api/v2/cache';
            $filename = "unmapped_doctors.json";
            $filepath = $cacheDir . '/' . $filename;
            
            if (!file_exists($filepath)) {
                return [];
            }

            $fileInfo = [
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => filesize($filepath),
                'modified' => filemtime($filepath)
            ];
            
            // Dodaj informacje o liczbie lekarzy
            $content = file_get_contents($filepath);
            if ($content !== false) {
                $data = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE && isset($data['total_count'])) {
                    $fileInfo['doctor_count'] = $data['total_count'];
                    $fileInfo['last_updated'] = $data['last_updated'] ?? date('Y-m-d H:i:s', $fileInfo['modified']);
                }
            }
            
            return [$fileInfo];
        } catch (Exception $e) {
            error_log("ImportSettings - Błąd pobierania informacji o pliku: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Odczytaj zbiorczy plik JSON z niezmapowanymi lekarzami
     */
    public function readUnmappedDoctorsFile($filename) {
        try {
            // Ignoruj nazwę pliku, zawsze używaj stałego pliku
            $cacheDir = __DIR__ . '/../../api/v2/cache';
            $filepath = $cacheDir . '/unmapped_doctors.json';
            
            if (!file_exists($filepath)) {
                error_log("ImportSettings - Plik nie istnieje: {$filepath}");
                return false;
            }
            
            $content = file_get_contents($filepath);
            if ($content === false) {
                error_log("ImportSettings - Błąd odczytu pliku: {$filepath}");
                return false;
            }
            
            $data = json_decode($content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log("ImportSettings - Błąd JSON: " . json_last_error_msg());
                return false;
            }
            
            // Dodaj dodatkowe informacje dla kompatybilności z istniejącym kodem
            if (isset($data['unmapped_doctors']) && !empty($data['unmapped_doctors'])) {
                $data['syncCode'] = 'default';
                $data['source'] = 'default';
                $data['timestamp'] = $data['last_updated'] ?? date('Y-m-d H:i:s');
                
                // Dodaj puste pola specialization dla kompatybilności
                foreach ($data['unmapped_doctors'] as &$doctor) {
                    if (!isset($doctor['specialization'])) {
                        $doctor['specialization'] = '';
                    }
                }
            }
            
            return $data;
        } catch (Exception $e) {
            error_log("ImportSettings - Błąd odczytu pliku JSON: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Sprawdź czy wszyscy lekarze z importu są zmapowani
     */
    public function validateDoctorsMapping($externalDoctors, $syncCode) {
        try {
            // Pobierz wszystkich lekarzy z bazy do tablicy tymczasowej
            // gdzie kluczem jest external_doctor_id
            $sql = "
                SELECT id, external_doctor_id, first_name, last_name
                FROM queue_doctors
                WHERE external_doctor_id IS NOT NULL AND external_doctor_id != ''
            ";
            
            $mappedDoctors = $this->db->fetchAll($sql);
            
            // Stwórz tablicę asocjacyjną z external_doctor_id jako kluczem
            $mappedDoctorsIndex = [];
            foreach ($mappedDoctors as $doctor) {
                $mappedDoctorsIndex[$doctor['external_doctor_id']] = $doctor;
            }
            
            error_log("ImportSettings - Pobrano " . count($mappedDoctorsIndex) . " zmapowanych lekarzy z bazy");
            
            $unmappedDoctors = [];
            
            foreach ($externalDoctors as $doctor) {
                $externalId = $doctor['external_id'] ?? $doctor['doctorId'] ?? null;
                
                if (!$externalId) {
                    continue;
                }
                
                // Sprawdź czy lekarz istnieje w tablicy zmapowanych lekarzy
                if (!isset($mappedDoctorsIndex[$externalId])) {
                    $unmappedDoctors[] = [
                        'external_id' => $externalId,
                        'name' => $doctor['name'] ?? $doctor['doctorName'] ?? 'Nieznany lekarz'
                    ];
                } else {
                    error_log("ImportSettings - Lekarz z external_id {$externalId} jest już zmapowany");
                }
            }
            
            error_log("ImportSettings - Znaleziono " . count($unmappedDoctors) . " niezmapowanych lekarzy");
            
            return [
                'all_mapped' => empty($unmappedDoctors),
                'unmapped_count' => count($unmappedDoctors),
                'unmapped_doctors' => $unmappedDoctors
            ];
        } catch (Exception $e) {
            error_log("ImportSettings - Błąd walidacji mapowania lekarzy: " . $e->getMessage());
            return [
                'all_mapped' => false,
                'unmapped_count' => 0,
                'unmapped_doctors' => []
            ];
        }
    }
}