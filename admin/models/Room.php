<?php

/**
 * Model Room - zarządzanie gabinetami
 */
class Room {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Pobierz wszystkie gabinety
     */
    public function getAll() {
        $sql = "
            SELECT 
                r.*,
                COUNT(d.id) as doctors_count
            FROM queue_rooms r
            LEFT JOIN queue_doctors d ON r.id = d.default_room_id AND d.active = 1
            GROUP BY r.id
            ORDER BY r.name
        ";

        return $this->db->fetchAll($sql);
    }

    /**
     * Pobierz gabinet po ID
     */
    public function getById($id) {
        $sql = "
            SELECT 
                r.*,
                COUNT(d.id) as doctors_count
            FROM queue_rooms r
            LEFT JOIN queue_doctors d ON r.id = d.default_room_id AND d.active = 1
            WHERE r.id = ?
            GROUP BY r.id
        ";

        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * <PERSON>bie<PERSON> lekarzy przypisanych do gabinetu
     */
    public function getDoctors($roomId) {
        $sql = "
            SELECT d.*
            FROM queue_doctors d
            WHERE d.default_room_id = ? AND d.active = 1
            ORDER BY d.last_name, d.first_name
        ";

        return $this->db->fetchAll($sql, [$roomId]);
    }

    /**
     * Dodaj nowy gabinet
     */
    public function create($name, $description = '') {
        if (empty($name)) {
            throw new Exception('Nazwa gabinetu jest wymagana');
        }

        // Sprawdź czy gabinet o tej nazwie już istnieje
        $existing = $this->db->fetchOne("SELECT id FROM queue_rooms WHERE name = ?", [$name]);
        if ($existing) {
            throw new Exception('Gabinet o tej nazwie już istnieje');
        }

        $sql = "
            INSERT INTO queue_rooms (name, description, created_at)
            VALUES (?, ?, datetime('now'))
        ";

        $this->db->execute($sql, [$name, $description]);
        return $this->db->lastInsertId();
    }

    /**
     * Aktualizuj gabinet
     */
    public function update($id, $name, $description = '') {
        if (empty($name)) {
            throw new Exception('Nazwa gabinetu jest wymagana');
        }

        // Sprawdź czy gabinet o tej nazwie już istnieje (wyłączając aktualny)
        $existing = $this->db->fetchOne("SELECT id FROM queue_rooms WHERE name = ? AND id != ?", [$name, $id]);
        if ($existing) {
            throw new Exception('Gabinet o tej nazwie już istnieje');
        }

        $sql = "
            UPDATE queue_rooms 
            SET name = ?, description = ?, updated_at = datetime('now')
            WHERE id = ?
        ";

        return $this->db->execute($sql, [$name, $description, $id]);
    }

    /**
     * Usuń gabinet
     */
    public function delete($id) {
        // Sprawdź czy są lekarze przypisani do tego gabinetu
        $doctorsCount = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_doctors 
            WHERE default_room_id = ? AND active = 1
        ", [$id]);

        if ($doctorsCount > 0) {
            throw new Exception('Nie można usunąć gabinetu, do którego są przypisani lekarze');
        }

        return $this->db->execute("DELETE FROM queue_rooms WHERE id = ?", [$id]);
    }

    /**
     * Pobierz statystyki gabinetu
     */
    public function getStats($roomId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $stats = [];

        // Liczba lekarzy w gabinecie
        $stats['doctors_count'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_doctors 
            WHERE default_room_id = ? AND active = 1
        ", [$roomId]);

        // Liczba wizyt dzisiaj w tym gabinecie
        $stats['appointments_today'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            WHERE d.default_room_id = ? AND a.appointment_date = ?
        ", [$roomId, $date]);

        // Liczba zakończonych wizyt
        $stats['completed_today'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            WHERE d.default_room_id = ? AND a.appointment_date = ? AND (a.status = 'completed' OR a.status = 'closed')
        ", [$roomId, $date]);

        // Liczba oczekujących
        $stats['waiting_today'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            WHERE d.default_room_id = ? AND a.appointment_date = ? AND a.status = 'waiting'
        ", [$roomId, $date]);

        return $stats;
    }

    /**
     * Sprawdź czy gabinet istnieje
     */
    public function exists($id) {
        return $this->db->fetchOne("SELECT id FROM queue_rooms WHERE id = ?", [$id]) !== false;
    }

    /**
     * Pobierz liczbę wszystkich gabinetów
     */
    public function getCount() {
        return $this->db->fetchCount("SELECT COUNT(*) FROM queue_rooms");
    }
}