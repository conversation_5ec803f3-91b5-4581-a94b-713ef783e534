<?php

/**
 * Model Appointment - zarząd<PERSON><PERSON> wizytami
 */
class Appointment {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Pobierz wizytę po ID
     */
    public function getById($id) {
        $sql = "
            SELECT
                a.*,
                d.first_name as doctor_first_name,
                d.last_name as doctor_last_name,
                d.specialization as doctor_specialization,
                r.name as room_name
            FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            WHERE a.id = ?
        ";

        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Pobierz wszystkie wizyty na dany dzień
     * UWAGA: Kolumna client_id została usunięta z queue_appointments
     */
    public function getAllForDate($date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT
                a.*,
                d.first_name as doctor_first_name,
                d.last_name as doctor_last_name,
                d.specialization as doctor_specialization,
                r.name as room_name
            FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            WHERE a.appointment_date = ?
            ORDER BY a.appointment_time ASC
        ";

        return $this->db->fetchAll($sql, [$date]);
    }

    /**
     * Pobierz wizyty dla konkretnego lekarza
     */
    public function getForDoctor($doctorId, $date = null, $status = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT a.*
            FROM queue_appointments a
            WHERE a.doctor_id = ? AND a.appointment_date = ?
        ";
        $params = [$doctorId, $date];

        if ($status !== null) {
            $sql .= " AND a.status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY a.appointment_time ASC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Dodaj nową wizytę
     */
    public function create($doctorId, $patientName, $appointmentTime, $appointmentDate = null, $phoneNumber = null, $appointmentDuration = 20, $externalId = null) {
        if ($appointmentDate === null) {
            $appointmentDate = date('Y-m-d');
        }

        // Debug
        error_log("Creating appointment: doctorId=$doctorId, patientName=$patientName, time=$appointmentTime, date=$appointmentDate, phone=$phoneNumber, duration=$appointmentDuration, externalId=$externalId");

        // Sprawdź czy lekarz istnieje w tabeli queue_doctors
        $doctorCheck = $this->db->fetchOne(
            "SELECT id FROM queue_doctors WHERE id = ? AND active = 1",
            [$doctorId]
        );
        error_log("Doctor check result: " . json_encode($doctorCheck));
        if (empty($doctorCheck)) {
            throw new Exception('Wybrany lekarz nie istnieje (ID: ' . $doctorId . ')');
        }

        // Sprawdź czy nie ma konfliktu czasowego
        if ($this->hasTimeConflict($doctorId, $appointmentTime, $appointmentDate)) {
            throw new Exception('Lekarz ma już wizytę o tej godzinie');
        }

        // Wyłącz foreign key constraints tymczasowo
        $this->db->execute("PRAGMA foreign_keys = OFF");

        // Generuj tracking code
        $trackingCode = $this->generateTrackingCode();

        $sql = "
            INSERT INTO queue_appointments (
                doctor_id, patient_name, appointment_time, appointment_date,
                appointment_duration, phone_number, status, tracking_code,
                external_id, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, 'waiting', ?, ?, datetime('now'))
        ";

        error_log("Executing SQL: $sql with params: " . json_encode([$doctorId, $patientName, $appointmentTime, $appointmentDate, $appointmentDuration, $phoneNumber, $trackingCode, $externalId]));
        $this->db->execute($sql, [$doctorId, $patientName, $appointmentTime, $appointmentDate, $appointmentDuration, $phoneNumber, $trackingCode, $externalId]);
        $appointmentId = $this->db->lastInsertId();
        error_log("Created appointment with ID: $appointmentId");

        // Włącz z powrotem foreign key constraints
        $this->db->execute("PRAGMA foreign_keys = ON");

        // Aktualizuj cache
        $this->updateCache();

        return $appointmentId;
    }

    /**
     * Aktualizuj wizytę
     */
    public function update($id, $patientName, $appointmentTime, $appointmentDate = null, $doctorId = null, $status = null, $isConfirmed = null, $smsSent = null, $isPatientPresent = null, $phoneNumber = null, $appointmentDuration = null, $externalId = null, $isCompleted = null) {
        if ($appointmentDate === null) {
            $appointmentDate = date('Y-m-d');
        }

        // Pobierz aktualną wizytę
        $appointment = $this->getById($id);
        if (!$appointment) {
            throw new Exception('Wizyta nie istnieje');
        }

        // Użyj podanego doctor_id lub zachowaj istniejący
        if ($doctorId === null) {
            $doctorId = $appointment['doctor_id'];
        }

        // Sprawdź konflikt czasowy (pomiń aktualną wizytę)
        if ($this->hasTimeConflict($doctorId, $appointmentTime, $appointmentDate, $id)) {
            throw new Exception('Lekarz ma już wizytę o tej godzinie');
        }

        $sql = "
            UPDATE queue_appointments
            SET patient_name = ?, appointment_time = ?, appointment_date = ?, doctor_id = ?
        ";

        $params = [$patientName, $appointmentTime, $appointmentDate, $doctorId];

        // Dodaj status do aktualizacji jeśli został podany
        if ($status !== null) {
            $sql .= ", status = ?";
            $params[] = $status;

            // Jeśli status zmienia się na 'current', ustaw called_at
            if ($status === 'current' && $appointment['status'] !== 'current') {
                $sql .= ", called_at = datetime('now')";
            }

            // Jeśli status zmienia się na 'completed' lub 'closed', ustaw completed_at
            if (($status === 'completed' || $status === 'closed') &&
                ($appointment['status'] !== 'completed' && $appointment['status'] !== 'closed')) {
                $sql .= ", completed_at = datetime('now')";
            }
        }

        // Dodaj dodatkowe statusy
        if ($isConfirmed !== null) {
            $sql .= ", is_confirmed = ?";
            $params[] = $isConfirmed;
        }

        if ($smsSent !== null) {
            $sql .= ", is_sms_sent = ?";
            $params[] = $smsSent;
        }

        if ($isPatientPresent !== null) {
            $sql .= ", is_patient_present = ?";
            $params[] = $isPatientPresent;
        }

        if ($isCompleted !== null) {
            $sql .= ", is_completed = ?";
            $params[] = $isCompleted;
        }

        // Dodaj dodatkowe pola
        if ($phoneNumber !== null) {
            $sql .= ", phone_number = ?";
            $params[] = $phoneNumber;
        }

        if ($appointmentDuration !== null) {
            $sql .= ", appointment_duration = ?";
            $params[] = $appointmentDuration;
        }

        if ($externalId !== null) {
            $sql .= ", external_id = ?";
            $params[] = $externalId;
        }

        $sql .= " WHERE id = ?";
        $params[] = $id;

        $result = $this->db->execute($sql, $params);
        
        // Aktualizuj cache
        if ($result) {
            $this->updateCache();
        }
        
        return $result;
    }

    /**
     * Usuń wizytę
     */
    public function delete($id) {
        $sql = "DELETE FROM queue_appointments WHERE id = ?";
        $result = $this->db->execute($sql, [$id]);
        
        // Aktualizuj cache
        if ($result) {
            $this->updateCache();
        }
        
        return $result;
    }

    /**
     * Wezwij pacjenta (zmień status na current)
     */
    public function callPatient($id) {
        // Pobierz wizytę
        $appointment = $this->getById($id);
        if (!$appointment) {
            throw new Exception('Wizyta nie istnieje');
        }

        $this->db->beginTransaction();

        try {
            // Zakończ aktualną wizytę tego lekarza jeśli istnieje
            $this->completeCurrentAppointment($appointment['doctor_id'], $appointment['appointment_date']);

            // Ustaw nową wizytę jako aktualną
            // UWAGA: Kolumna started_at została usunięta
            $sql = "
                UPDATE queue_appointments
                SET status = 'current', called_at = datetime('now')
                WHERE id = ?
            ";
            $this->db->execute($sql, [$id]);

            $this->db->commit();
            
            // Aktualizuj cache
            $this->updateCache();
            
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * Zakończ wizytę
     */
    public function completeAppointment($id) {
        $sql = "
            UPDATE queue_appointments
            SET status = 'completed', completed_at = datetime('now'), is_completed = 1
            WHERE id = ?
        ";

        $result = $this->db->execute($sql, [$id]);
        
        // Aktualizuj cache
        if ($result) {
            $this->updateCache();
        }
        
        return $result;
    }

    /**
     * Zakończ aktualną wizytę lekarza
     */
    public function completeCurrentAppointment($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            UPDATE queue_appointments
            SET status = 'completed', completed_at = datetime('now'), is_completed = 1
            WHERE doctor_id = ? AND appointment_date = ? AND status = 'current'
        ";

        $result = $this->db->execute($sql, [$doctorId, $date]);
        
        // Aktualizuj cache
        if ($result) {
            $this->updateCache();
        }
        
        return $result;
    }

    /**
     * Sprawdź konflikt czasowy
     * UWAGA: Kolumna client_id została usunięta z queue_appointments
     */
    public function hasTimeConflict($doctorId, $appointmentTime, $appointmentDate, $excludeId = null) {
        $sql = "
            SELECT COUNT(*)
            FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ? AND appointment_time = ? AND status IN ('waiting', 'current')
        ";
        $params = [$doctorId, $appointmentDate, $appointmentTime];

        if ($excludeId !== null) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        error_log("Checking time conflict: SQL=$sql, params=" . json_encode($params));
        $count = $this->db->fetchCount($sql, $params);
        error_log("Time conflict count: $count");

        return $count > 0;
    }

    /**
     * Pobierz następną oczekującą wizytę dla lekarza
     */
    public function getNextWaiting($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT a.*
            FROM queue_appointments a
            WHERE a.doctor_id = ? AND a.appointment_date = ? AND a.status = 'waiting'
            ORDER BY a.appointment_time ASC
            LIMIT 1
        ";

        return $this->db->fetchOne($sql, [$doctorId, $date]);
    }

    /**
     * Pobierz aktualną wizytę lekarza
     */
    public function getCurrentForDoctor($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT a.*
            FROM queue_appointments a
            WHERE a.doctor_id = ? AND a.appointment_date = ? AND a.status = 'current'
            LIMIT 1
        ";

        return $this->db->fetchOne($sql, [$doctorId, $date]);
    }

    /**
     * Pobierz statystyki wizyt
     * UWAGA: Kolumna client_id została usunięta z queue_appointments
     */
    public function getStats($date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $stats = [];

        // Łączna liczba wizyt - UWAGA: Kolumna client_id została usunięta
        $stats['total'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments
            WHERE appointment_date = ?
        ", [$date]);

        // Oczekujące
        $stats['waiting'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments
            WHERE appointment_date = ? AND status = 'waiting'
        ", [$date]);

        // Aktualne
        $stats['current'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments
            WHERE appointment_date = ? AND status = 'current'
        ", [$date]);

        // Zakończone
        $stats['completed'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments
            WHERE appointment_date = ? AND (status = 'completed' OR status = 'closed')
        ", [$date]);

        return $stats;
    }

    /**
     * Pobierz wizyty z opóźnieniem
     * UWAGA: Kolumna client_id została usunięta z queue_appointments
     */
    public function getDelayedAppointments($date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $currentTime = date('H:i:s');

        $sql = "
            SELECT 
                a.*,
                d.first_name as doctor_first_name,
                d.last_name as doctor_last_name
            FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            WHERE a.appointment_date = ?
                AND a.status = 'waiting' AND a.appointment_time < ?
            ORDER BY a.appointment_time ASC
        ";

        return $this->db->fetchAll($sql, [$date, $currentTime]);
    }

    /**
     * Oblicz średnie opóźnienie
     * UWAGA: Kolumna client_id została usunięta z queue_appointments
     */
    public function getAverageDelay($date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $delayedAppointments = $this->getDelayedAppointments($date);

        if (empty($delayedAppointments)) {
            return 0;
        }

        $totalDelay = 0;

        foreach ($delayedAppointments as $appointment) {
            $delay = calculateDelayMinutes($appointment['appointment_time'], $date, $appointment['status'] ?? 'waiting');
            $totalDelay += $delay;
        }

        return round($totalDelay / count($delayedAppointments));
    }

    /**
     * Generuj unikalny 16-znakowy kod śledzenia
     */
    private function generateTrackingCode() {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $code = '';

        do {
            $code = '';
            for ($i = 0; $i < 16; $i++) {
                $code .= $characters[random_int(0, strlen($characters) - 1)];
            }

            // Sprawdź czy kod już istnieje
            $existing = $this->db->fetchOne(
                "SELECT id FROM queue_appointments WHERE tracking_code = ?",
                [$code]
            );
        } while ($existing);

        return $code;
    }

    /**
     * Aktualizuj cache po zmianie w wizytach
     */
    private function updateCache() {
        try {
            // Importuj EnhancedCacheManager
            require_once __DIR__ . '/../../api/v2/core/EnhancedCacheManager.php';
            
            $cacheManager = new EnhancedCacheManager();
            $cacheManager->markChanges('appointments');
            
            error_log("Appointment: Zaktualizowano cache dla appointments");
        } catch (Exception $e) {
            error_log("Appointment: Błąd aktualizacji cache: " . $e->getMessage());
        }
    }

    /**
     * Znajdź wizytę po kodzie śledzenia
     */
    public function getByTrackingCode($trackingCode) {
        $sql = "
            SELECT
                a.*,
                d.first_name as doctor_first_name,
                d.last_name as doctor_last_name,
                d.specialization as doctor_specialization,
                r.name as room_name
            FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            WHERE a.tracking_code = ?
        ";

        return $this->db->fetchOne($sql, [$trackingCode]);
    }

    /**
     * Znajdź wizytę po external_id
     */
    public function getByExternalId($externalId) {
        $sql = "
            SELECT
                a.*,
                d.first_name as doctor_first_name,
                d.last_name as doctor_last_name,
                d.specialization as doctor_specialization,
                r.name as room_name
            FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            WHERE a.external_id = ?
        ";

        return $this->db->fetchOne($sql, [$externalId]);
    }

    /**
     * Pobierz wizyty z numerem telefonu
     */
    public function getByPhoneNumber($phoneNumber, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT
                a.*,
                d.first_name as doctor_first_name,
                d.last_name as doctor_last_name,
                d.specialization as doctor_specialization,
                r.name as room_name
            FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            WHERE a.phone_number = ? AND a.appointment_date = ?
            ORDER BY a.appointment_time ASC
        ";

        return $this->db->fetchAll($sql, [$phoneNumber, $date]);
    }

    /**
     * Potwierdź wizytę
     */
    public function confirmAppointment($id) {
        $sql = "
            UPDATE queue_appointments
            SET is_confirmed = 1
            WHERE id = ?
        ";

        $result = $this->db->execute($sql, [$id]);
        
        // Aktualizuj cache
        if ($result) {
            $this->updateCache();
        }
        
        return $result;
    }

    /**
     * Oznacz SMS jako wysłany
     */
    public function markSmsSent($id) {
        $sql = "
            UPDATE queue_appointments
            SET is_sms_sent = 1
            WHERE id = ?
        ";

        $result = $this->db->execute($sql, [$id]);
        
        // Aktualizuj cache
        if ($result) {
            $this->updateCache();
        }
        
        return $result;
    }

    /**
     * Oznacz obecność pacjenta
     */
    public function markPatientPresent($id) {
        $sql = "
            UPDATE queue_appointments
            SET is_patient_present = 1
            WHERE id = ?
        ";

        $result = $this->db->execute($sql, [$id]);
        
        // Aktualizuj cache
        if ($result) {
            $this->updateCache();
        }
        
        return $result;
    }

    /**
     * Pobierz potwierdzone wizyty
     */
    public function getConfirmedAppointments($date = null, $doctorId = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT
                a.*,
                d.first_name as doctor_first_name,
                d.last_name as doctor_last_name,
                d.specialization as doctor_specialization,
                r.name as room_name
            FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            WHERE a.appointment_date = ? AND a.is_confirmed = 1
        ";

        $params = [$date];

        if ($doctorId !== null) {
            $sql .= " AND a.doctor_id = ?";
            $params[] = $doctorId;
        }

        $sql .= " ORDER BY a.appointment_time ASC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Pobierz wizyty, dla których nie wysłano SMS
     */
    public function getAppointmentsWithoutSms($date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT
                a.*,
                d.first_name as doctor_first_name,
                d.last_name as doctor_last_name,
                d.specialization as doctor_specialization,
                r.name as room_name
            FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            WHERE a.appointment_date = ? AND a.is_sms_sent = 0 AND a.phone_number IS NOT NULL AND a.phone_number != ''
            ORDER BY a.appointment_time ASC
        ";

        return $this->db->fetchAll($sql, [$date]);
    }
}
