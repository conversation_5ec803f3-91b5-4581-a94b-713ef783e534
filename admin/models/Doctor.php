<?php

/**
 * Model Doctor - zar<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>i
 */
class Doctor {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }
    

    /**
     * Pobierz wszystkich lekarzy
     */
    public function getAllForClient() {
        $sql = "
            SELECT
                d.*,
                r.name as default_room_name,
                cd.display_name as display_name
            FROM queue_doctors d
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            LEFT JOIN client_displays cd ON d.display_id = cd.id
            WHERE d.active = 1
            ORDER BY d.last_name, d.first_name
        ";

        return $this->db->fetchAll($sql);
    }

    /**
     * Zahashuj hasło
     */
    private function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Sprawdź hasło
     */
    private function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }

    /**
     * Pobierz lekarza po ID
     */
    public function getById($id) {
        $sql = "
            SELECT
                d.*,
                r.name as default_room_name,
                cd.display_name as display_name
            FROM queue_doctors d
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            LEFT JOIN client_displays cd ON d.display_id = cd.id
            WHERE d.id = ?
        ";

        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Pobierz lekarzy z dzisiejszymi wizytami
     */
    public function getDoctorsWithAppointments($date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT
                d.*,
                r.name as default_room_name,
                cd.display_name as display_name,
                COUNT(a.id) as appointments_count,
                COUNT(CASE WHEN a.status = 'waiting' THEN 1 END) as waiting_count,
                COUNT(CASE WHEN a.status = 'current' THEN 1 END) as current_count,
                COUNT(CASE WHEN a.status = 'completed' OR a.status = 'closed' THEN 1 END) as completed_count,
                MIN(CASE WHEN a.status = 'waiting' THEN a.appointment_time END) as next_appointment_time
            FROM queue_doctors d
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            LEFT JOIN client_displays cd ON d.display_id = cd.id
            LEFT JOIN queue_appointments a ON d.id = a.doctor_id AND a.appointment_date = ?
            WHERE d.active = 1
            GROUP BY d.id
            ORDER BY COUNT(a.id) DESC, d.last_name, d.first_name
        ";

        return $this->db->fetchAll($sql, [$date]);
    }

    /**
     * Pobierz aktualną wizytę lekarza
     */
    public function getCurrentAppointment($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT a.*, d.first_name, d.last_name
            FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            WHERE a.doctor_id = ? AND a.appointment_date = ? AND a.status = 'current'
            LIMIT 1
        ";

        return $this->db->fetchOne($sql, [$doctorId, $date]);
    }

    /**
     * Pobierz wszystkie wizyty lekarza (oczekujące, aktualne, zakończone)
     */
    public function getWaitingAppointments($doctorId, $date = null, $limit = 10) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT a.*,
                CASE
                    WHEN a.appointment_time LIKE '%-%'
                    THEN substr(a.appointment_time, 12, 5)
                    ELSE substr(a.appointment_time, 1, 5)
                END as normalized_time
            FROM queue_appointments a
            WHERE a.doctor_id = ? AND a.appointment_date = ?
            ORDER BY normalized_time ASC
            LIMIT ?
        ";

        return $this->db->fetchAll($sql, [$doctorId, $date, $limit]);
    }

    /**
     * Pobierz wszystkie wizyty lekarza na dany dzień
     */
    public function getAllAppointments($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT a.*
            FROM queue_appointments a
            WHERE a.doctor_id = ? AND a.appointment_date = ?
            ORDER BY a.appointment_time ASC
        ";

        return $this->db->fetchAll($sql, [$doctorId, $date]);
    }

    /**
     * Oblicz opóźnienie lekarza
     */
    public function calculateDelay($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $currentTime = date('H:i:s');

        // Pobierz aktualną wizytę
        $currentAppointment = $this->getCurrentAppointment($doctorId, $date);

        if (!$currentAppointment) {
            // Sprawdź czy są oczekujące wizyty które już powinny się rozpocząć
            $sql = "
                SELECT a.*
                FROM queue_appointments a
                WHERE a.doctor_id = ? AND a.appointment_date = ? AND a.status = 'waiting'
                    AND a.appointment_time <= ?
                ORDER BY a.appointment_time ASC
                LIMIT 1
            ";

            $overdueAppointment = $this->db->fetchOne($sql, [$doctorId, $date, $currentTime]);

            if ($overdueAppointment) {
                // Oblicz opóźnienie na podstawie pierwszej zaległej wizyty
                $delayMinutes = calculateDelayMinutes($overdueAppointment['appointment_time'], $date, $overdueAppointment['status'] ?? 'waiting');

                // Dodaj czas oczekujących wizyt przed nią
                $waitingCount = $this->db->fetchCount("
                    SELECT COUNT(*)
                    FROM queue_appointments
                    WHERE doctor_id = ? AND appointment_date = ? AND status = 'waiting'
                        AND appointment_time < ?
                ", [$doctorId, $date, $overdueAppointment['appointment_time']]);

                $queueDelay = $waitingCount * DEFAULT_APPOINTMENT_DURATION;

                return $delayMinutes + $queueDelay;
            }

            return 0; // Brak opóźnienia
        }

        // Oblicz opóźnienie aktualnej wizyty w minutach
        $appointmentDelay = calculateDelayMinutes($currentAppointment['appointment_time'], $date, $currentAppointment['status'] ?? 'current');

        // Dodaj czas oczekujących wizyt
        $waitingCount = $this->db->fetchCount("
            SELECT COUNT(*)
            FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ? AND status = 'waiting'
        ", [$doctorId, $date]);

        $queueDelay = $waitingCount * DEFAULT_APPOINTMENT_DURATION;

        return $appointmentDelay + $queueDelay;
    }

    /**
     * Oblicz dokładne opóźnienie lekarza uwzględniające kumulujące się opóźnienia
     */
    public function calculateAccurateDelay($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $currentTime = time();
        $appointments = $this->getAllAppointments($doctorId, $date);

        if (empty($appointments)) {
            return 0;
        }

        // Ustalenie punktu odniesienia
        $referenceTime = $currentTime;
        $currentAppointment = $this->getCurrentAppointment($doctorId, $date);

        if ($currentAppointment) {
            // Oblicz planowany czas zakończenia aktualnej wizyty
            $appointmentStart = strtotime($date . ' ' . $currentAppointment['appointment_time']);
            $appointmentDuration = DEFAULT_APPOINTMENT_DURATION;
            $referenceTime = $appointmentStart + ($appointmentDuration * 60);
        }

        // Oblicz rzeczywiste czasy rozpoczęcia i zakończenia dla wszystkich wizyt
        $maxDelay = 0;
        $lastCompletionTime = $referenceTime;

        foreach ($appointments as $appointment) {
            if ($appointment['status'] === 'completed' || $appointment['status'] === 'closed') {
                continue; // Pomiń zakończone wizyty
            }

            $plannedStart = strtotime($date . ' ' . $appointment['appointment_time']);
            $actualStart = max($plannedStart, $lastCompletionTime);
            $appointmentDuration = DEFAULT_APPOINTMENT_DURATION;
            $completionTime = $actualStart + ($appointmentDuration * 60);

            // Oblicz opóźnienie dla tej wizyty
            $delay = $actualStart - $plannedStart;
            if ($delay > $maxDelay) {
                $maxDelay = $delay;
            }

            $lastCompletionTime = $completionTime;
        }

        // Zwróć opóźnienie w minutach
        return floor($maxDelay / 60);
    }

    /**
     * Pobierz status lekarza na podstawie wizyt
     */
    public function getStatus($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        // Sprawdź czy ma aktualną wizytę
        $currentAppointment = $this->getCurrentAppointment($doctorId, $date);
        if ($currentAppointment) {
            return 'working';
        }

        // Sprawdź czy ma oczekujące wizyty
        $waitingCount = $this->db->fetchCount("
            SELECT COUNT(*)
            FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ? AND status = 'waiting'
        ", [$doctorId, $date]);

        if ($waitingCount > 0) {
            return 'ready';
        }

        // Sprawdź czy miał dziś jakieś wizyty
        $totalCount = $this->db->fetchCount("
            SELECT COUNT(*)
            FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ?
        ", [$doctorId, $date]);

        if ($totalCount > 0) {
            return 'finished';
        }

        return 'no_appointments';
    }

    /**
     * Dodaj nowego lekarza
     */
    public function create($firstName, $lastName, $specialization = '', $photoUrl = '', $defaultRoomId = null, $displayId = null) {
        $sql = "
            INSERT INTO queue_doctors (first_name, last_name, specialization, photo_url, default_room_id, display_id, active, created_at)
            VALUES (?, ?, ?, ?, ?, ?, 1, datetime('now'))
        ";

        $this->db->execute($sql, [$firstName, $lastName, $specialization, $photoUrl, $defaultRoomId, $displayId]);
        return $this->db->lastInsertId();
    }

    /**
     * Aktualizuj lekarza
     */
    public function update($id, $firstName, $lastName, $specialization = '', $photoUrl = null, $defaultRoomId = null, $displayId = null, $login = null, $password = null) {
        $sql = "
            UPDATE queue_doctors
            SET first_name = ?, last_name = ?, specialization = ?, photo_url = ?, default_room_id = ?, display_id = ?, updated_at = datetime('now')
        ";
        $params = [$firstName, $lastName, $specialization, $photoUrl, $defaultRoomId, $displayId, $id];
        
        // Dodaj login do aktualizacji jeśli podany
        if ($login !== null) {
            $sql .= ", login = ?";
            array_splice($params, -1, 0, $login);
        }
        
        // Dodaj hasło do aktualizacji jeśli podane
        if ($password !== null) {
            $sql .= ", password = ?";
            array_splice($params, -1, 0, $this->hashPassword($password));
        }
        
        $sql .= " WHERE id = ?";
        
        error_log("DEBUG: SQL do aktualizacji: " . $sql);
        error_log("DEBUG: Parametry: " . json_encode($params));

        try {
            $result = $this->db->execute($sql, $params);
            error_log("DEBUG: Wynik execute: " . ($result ? 'SUCCESS' : 'FAILED'));
            return $result;
        } catch (Exception $e) {
            error_log("DEBUG: Błąd execute: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Sprawdź czy login jest unikalny
     */
    public function isLoginUnique($login, $excludeId = null) {
        $sql = "SELECT id FROM queue_doctors WHERE login = ? AND active = 1";
        $params = [$login];
        
        // Jeśli podano ID do wykluczenia, dodaj warunek
        if ($excludeId !== null) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $existing = $this->db->fetchOne($sql, $params);
        return !$existing;
    }
    
    /**
     * Zmień hasło lekarza
     */
    public function changePassword($id, $newPassword) {
        $hashedPassword = $this->hashPassword($newPassword);
        $sql = "UPDATE queue_doctors SET password = ?, updated_at = datetime('now') WHERE id = ?";
        return $this->db->execute($sql, [$hashedPassword, $id]);
    }
    
    /**
     * Sprawdź dane logowania lekarza
     */
    public function verifyLogin($login, $password) {
        $sql = "SELECT * FROM queue_doctors WHERE login = ? AND active = 1";
        $doctor = $this->db->fetchOne($sql, [$login]);
        
        if ($doctor && $this->verifyPassword($password, $doctor['password'])) {
            return $doctor;
        }
        
        return false;
    }

    /**
     * Usuń lekarza (oznacz jako nieaktywny)
     */
    public function delete($id) {
        $sql = "UPDATE queue_doctors SET active = 0, updated_at = datetime('now') WHERE id = ?";
        return $this->db->execute($sql, [$id]);
    }

    /**
     * Pobierz statystyki lekarza
     */
    public function getStats($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $stats = [];

        // Liczba wizyt dzisiaj
        $stats['appointments_today'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments 
            WHERE doctor_id = ? AND appointment_date = ?
        ", [$doctorId, $date]);

        // Liczba zakończonych wizyt
        $stats['completed_today'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ? AND (status = 'completed' OR status = 'closed')
        ", [$doctorId, $date]);

        // Liczba oczekujących
        $stats['waiting_today'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments 
            WHERE doctor_id = ? AND appointment_date = ? AND status = 'waiting'
        ", [$doctorId, $date]);

        // Opóźnienie
        $stats['delay_minutes'] = $this->calculateAccurateDelay($doctorId, $date);

        return $stats;
    }

}
