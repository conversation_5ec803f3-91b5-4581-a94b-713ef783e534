<?php

/**
 * Klasa Database - zarządzanie połączeniem z bazą danych SQLite
 */
class Database {
    private static $instance = null;
    private $pdo;
    
    public function __construct() {
        $this->connect();
    }
    
    /**
     * Singleton pattern - jedna instancja bazy danych
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Nawiązanie połączenia z bazą danych
     */
    private function connect() {
        try {
            $this->pdo = new PDO('sqlite:' . DB_PATH);
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            
            // Włącz foreign keys dla SQLite
            $this->pdo->exec('PRAGMA foreign_keys = ON');
            
        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                die('Błąd połączenia z bazą danych: ' . $e->getMessage());
            } else {
                error_log('Błąd bazy danych: ' . $e->getMessage());
                die('Błąd połączenia z bazą danych');
            }
        }
    }
    
    /**
     * Pobierz instancję PDO
     */
    public function getPdo() {
        return $this->pdo;
    }
    
    /**
     * Przygotuj zapytanie SQL
     */
    public function prepare($sql) {
        return $this->pdo->prepare($sql);
    }
    
    /**
     * Wykonaj zapytanie SQL
     */
    public function query($sql) {
        return $this->pdo->query($sql);
    }
    
    /**
     * Wykonaj zapytanie i zwróć wszystkie wyniki
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Wykonaj zapytanie i zwróć jeden wynik
     */
    public function fetchOne($sql, $params = []) {
        error_log("Database::fetchOne - SQL: " . $sql);
        error_log("Database::fetchOne - Params: " . json_encode($params));
        
        $stmt = $this->prepare($sql);
        $result = $stmt->execute($params);
        
        if (!$result) {
            error_log("Database::fetchOne - Execute failed: " . json_encode($stmt->errorInfo()));
            return false;
        }
        
        $fetchResult = $stmt->fetch();
        error_log("Database::fetchOne - Fetch result: " . json_encode($fetchResult));
        
        return $fetchResult;
    }
    
    /**
     * Wykonaj zapytanie i zwróć liczbę wierszy
     */
    public function fetchCount($sql, $params = []) {
        $stmt = $this->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchColumn();
    }
    
    /**
     * Wykonaj zapytanie INSERT/UPDATE/DELETE
     */
    public function execute($sql, $params = []) {
        error_log("Database::execute - SQL: " . $sql);
        error_log("Database::execute - Params: " . json_encode($params));
        
        $stmt = $this->prepare($sql);
        $result = $stmt->execute($params);
        
        error_log("Database::execute - Result: " . ($result ? 'true' : 'false'));
        error_log("Database::execute - Error: " . json_encode($stmt->errorInfo()));
        
        return $result;
    }
    
    /**
     * Pobierz ID ostatnio wstawionego rekordu
     */
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Rozpocznij transakcję
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * Zatwierdź transakcję
     */
    public function commit() {
        return $this->pdo->commit();
    }
    
    /**
     * Cofnij transakcję
     */
    public function rollback() {
        return $this->pdo->rollback();
    }
    
    /**
     * Sprawdź czy tabela istnieje
     */
    public function tableExists($tableName) {
        $sql = "SELECT name FROM sqlite_master WHERE type='table' AND name=?";
        $stmt = $this->prepare($sql);
        $stmt->execute([$tableName]);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Pobierz strukturę tabeli
     */
    public function getTableStructure($tableName) {
        $sql = "PRAGMA table_info($tableName)";
        return $this->fetchAll($sql);
    }
    
    /**
     * Sprawdź czy kolumna istnieje w tabeli
     */
    public function columnExists($tableName, $columnName) {
        $structure = $this->getTableStructure($tableName);
        foreach ($structure as $column) {
            if ($column['name'] === $columnName) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Wykonaj backup bazy danych
     */
    public function backup($backupPath = null) {
        if ($backupPath === null) {
            $backupPath = dirname(DB_PATH) . '/reklama_backup_' . date('Y-m-d_H-i-s') . '.db';
        }
        
        return copy(DB_PATH, $backupPath);
    }
    
    /**
     * Pobierz statystyki bazy danych
     */
    public function getStats() {
        $stats = [];
        
        // Liczba tabel
        $sql = "SELECT COUNT(*) FROM sqlite_master WHERE type='table'";
        $stats['tables_count'] = $this->fetchCount($sql);
        
        // Rozmiar bazy danych
        $stats['database_size'] = filesize(DB_PATH);
        $stats['database_size_mb'] = round($stats['database_size'] / 1024 / 1024, 2);
        
        // Liczba rekordów w głównych tabelach
        $mainTables = ['users', 'queue_doctors', 'queue_appointments', 'queue_rooms'];
        foreach ($mainTables as $table) {
            if ($this->tableExists($table)) {
                $stats[$table . '_count'] = $this->fetchCount("SELECT COUNT(*) FROM $table");
            }
        }
        
        return $stats;
    }
    
    /**
     * Optymalizuj bazę danych
     */
    public function optimize() {
        $this->pdo->exec('VACUUM');
        $this->pdo->exec('ANALYZE');
    }
    
    /**
     * Zamknij połączenie
     */
    public function close() {
        $this->pdo = null;
    }
    
    /**
     * Destruktor - zamknij połączenie
     */
    public function __destruct() {
        $this->close();
    }
}

?>
