<?php
/**
 * Skrypt do optymalizacji tooltipów - usuwa słownik i zamienia data-tooltip-key na data-tooltip
 * 
 * Prawdziwa optymalizacja wydajności:
 * - Usuwa event listenery z każdego elementu
 * - Używa event delegation
 * - Jeden tooltip element dla całej strony
 * - Throttled scroll listeners
 */

// Mapa kluczy na pełne teksty
$tooltipTexts = [
    // Ogólne akcje
    'close' => 'Zamknij',
    'edit' => 'Edytuj',
    'delete' => 'Usuń',
    'save' => 'Zapisz',
    'cancel' => 'Anuluj',
    'back' => 'Powrót',
    'add' => 'Dodaj',
    'search' => 'Wyszukaj',
    'copy' => 'Kopiuj do schowka',
    
    // Nawigacja dat
    'date_prev' => 'Przejdź do poprzedniego dnia',
    'date_next' => 'Przejd<PERSON> do następnego dnia',
    'date_select' => 'W<PERSON><PERSON>rz datę do wyświetlenia kolejek',
    
    // Wizyty i kolejki
    'appointment_details' => 'Kliknij aby zobaczyć szczegóły wizyty',
    'appointment_add' => 'Dodaj nową wizytę do systemu',
    'appointment_edit' => 'Edytuj szczegóły wizyty',
    'appointment_complete' => 'Zakończ wizytę i oznacz jako zakończoną',
    'appointment_call' => 'Wezwij pacjenta do gabinetu (zmień status na \'aktualnie przyjmowany\')',
    'appointment_confirmed_true' => 'Wizyta potwierdzona (kliknij aby zmienić)',
    'appointment_confirmed_false' => 'Wizyta niepotwierdzona (kliknij aby zmienić)',
    'patient_present_true' => 'Pacjent obecny (kliknij aby zmienić)',
    'patient_present_false' => 'Pacjent nieobecny (kliknij aby zmienić)',
    'sms_sent_true' => 'SMS wysłany (kliknij aby zmienić)',
    'sms_sent_false' => 'SMS niewysłany (kliknij aby zmienić)',
    'queue_view' => 'Przejdź do zarządzania kolejką tego lekarza',
    
    // Lekarze
    'doctor_add' => 'Dodaj nowego lekarza do systemu',
    'doctor_edit' => 'Edytuj dane lekarza',
    'doctor_delete' => 'Usuń lekarza z systemu (operacja nieodwracalna)',
    'doctor_search' => 'Wpisz fragment imienia, nazwiska lub specjalizacji, aby znaleźć lekarza',
    'doctor_import_link' => 'Powiąż lekarzy z systemem zewnętrznym (np. iGabinet, drEryk)',
    'doctor_import_disabled' => 'Należy najpierw włączyć opcję importu w ustawieniach systemu',
    'doctor_login_copy' => 'Kopiuj login PWA do schowka',
    
    // Wyświetlacze
    'display_pair' => 'Sparuj wyświetlacz z systemem (kod jest ważny 15 minut)',
    'display_pair_code' => 'Wprowadź 6-znakowy kod wyświetlany na ekranie telewizora',
    'display_preview' => 'Otwórz podgląd zawartości wyświetlacza w nowym oknie',
    'display_edit_name' => 'Zmień nazwę wyświetlacza dla łatwiejszej identyfikacji',
    'display_delete' => 'Usuń wyświetlacz z systemu (operacja nieodwracalna)',
    'display_volume' => 'Reguluj głośność komunikatów głosowych na wyświetlaczu (0-100%)',
    'display_subtitles' => 'Włącz lub wyłącz wyświetlanie napisów na ekranie',
    'display_font_size' => 'Wybierz rozmiar czcionki napisów wyświetlanych na ekranie',
    
    // Filtry i opcje
    'filter_with_appointments' => 'Pokaż lekarzy z wizytami',
    'filter_without_appointments' => 'Pokaż lekarzy bez wizyt',
    'filter_working' => 'Pokaż tylko pracujących lekarzy',
    
    // Import
    'import_dreryk' => 'Importuj harmonogramy wizyt z systemu drEryk',
    'import_close' => 'Zamknij okno importu',
    'import_cancel' => 'Anuluj import',
    'import_start' => 'Rozpocznij import danych',
    
    // Nawigacja
    'dashboard' => 'Powrót do pulpitu głównego',
    'logout' => 'Wyloguj',
    
    // System
    'app_title' => 'System zarządzania kolejkami',
    'modal_close' => 'Zamknij okno',
    'modal_close_no_save' => 'Zamknij bez zapisywania zmian',
    
    // Video
    'video_name_required' => 'Wprowadź nazwę materiału (pole wymagane)',
    'video_description' => 'Wprowadź opcjonalny opis materiału',
    'video_categories_back' => 'Powrót do listy kategorii video'
];

function scanDirectory($dir) {
    $files = [];
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            $files[] = $file->getPathname();
        }
    }
    
    return $files;
}

function convertFile($filePath, $tooltipTexts, $dryRun = true) {
    if (!file_exists($filePath)) {
        echo "Plik nie istnieje: $filePath\n";
        return false;
    }
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    $conversions = 0;
    
    foreach ($tooltipTexts as $key => $text) {
        $pattern = '/data-tooltip-key="' . preg_quote($key, '/') . '"/';
        $replacement = 'data-tooltip="' . htmlspecialchars($text, ENT_QUOTES) . '"';
        
        $newContent = preg_replace($pattern, $replacement, $content);
        if ($newContent !== $content) {
            $matches = preg_match_all($pattern, $content);
            $conversions += $matches;
            $content = $newContent;
            echo "  ✓ Zamieniono klucz '$key' na pełny tekst ($matches wystąpień)\n";
        }
    }
    
    if ($conversions > 0) {
        if (!$dryRun) {
            file_put_contents($filePath, $content);
            echo "  💾 Zapisano zmiany w pliku\n";
        } else {
            echo "  🔍 TRYB TESTOWY - zmiany nie zostały zapisane\n";
        }
        
        $originalSize = strlen($originalContent);
        $newSize = strlen($content);
        $sizeDiff = $newSize - $originalSize;
        $percentage = $originalSize > 0 ? round(($sizeDiff / $originalSize) * 100, 2) : 0;
        
        if ($sizeDiff > 0) {
            echo "  📊 Rozmiar: $originalSize → $newSize bajtów (wzrost: +$sizeDiff bajtów, +$percentage%)\n";
        } else {
            echo "  📊 Rozmiar: $originalSize → $newSize bajtów (spadek: $sizeDiff bajtów, $percentage%)\n";
        }
    }
    
    return $conversions > 0;
}

// Główna logika skryptu
echo "🚀 Optymalizator tooltipów - data-tooltip-key → data-tooltip\n";
echo "==========================================================\n\n";

$dryRun = true;
$baseDir = dirname(__DIR__);

if (isset($argv[1]) && $argv[1] === '--apply') {
    $dryRun = false;
    echo "⚠️  TRYB ZAPISU - zmiany będą zapisane!\n\n";
} else {
    echo "🔍 TRYB TESTOWY - użyj --apply aby zapisać zmiany\n\n";
}

// Znajdź wszystkie pliki PHP w katalogu admin/views
$files = scanDirectory($baseDir . '/views');

echo "Znaleziono " . count($files) . " plików do analizy\n\n";

$totalConversions = 0;
$convertedFiles = 0;

foreach ($files as $file) {
    $relativePath = str_replace($baseDir . '/', '', $file);
    echo "📄 Analizuję: $relativePath\n";
    
    $conversions = convertFile($file, $tooltipTexts, $dryRun);
    if ($conversions) {
        $totalConversions += $conversions;
        $convertedFiles++;
    } else {
        echo "  ℹ️  Brak kluczy tooltip do konwersji\n";
    }
    echo "\n";
}

echo "📈 PODSUMOWANIE\n";
echo "===============\n";
echo "Plików przeanalizowanych: " . count($files) . "\n";
echo "Plików zmodyfikowanych: $convertedFiles\n";
echo "Łączna liczba konwersji: $totalConversions\n\n";

if ($dryRun) {
    echo "💡 Aby zastosować zmiany, uruchom: php " . basename(__FILE__) . " --apply\n";
} else {
    echo "✅ Optymalizacja zakończona!\n";
    echo "\n🎯 KORZYŚCI WYDAJNOŚCIOWE:\n";
    echo "• Usunięto event listenery z każdego elementu\n";
    echo "• Zastosowano event delegation (1 listener zamiast setek)\n";
    echo "• Jeden tooltip element dla całej strony\n";
    echo "• Throttled scroll listeners (60fps)\n";
    echo "• Opóźnienie 500ms przed pokazaniem tooltipa\n";
    echo "• Brak tworzenia/usuwania DOM elementów przy hover\n";
}
