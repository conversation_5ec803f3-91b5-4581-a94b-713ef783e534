<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demonstracja optymalizacji wydajności tooltipów</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .header p {
            margin: 10px 0 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .before, .after {
            padding: 25px;
            border-radius: 12px;
            border: 2px solid;
        }
        
        .before {
            background: #fef2f2;
            border-color: #fca5a5;
        }
        
        .after {
            background: #f0fdf4;
            border-color: #86efac;
        }
        
        .before h3 {
            color: #dc2626;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .after h3 {
            color: #16a34a;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .metric {
            background: rgba(0, 0, 0, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: #6b7280;
            margin-top: 5px;
        }
        
        .demo-section {
            margin: 40px 0;
            padding: 30px;
            background: #f8fafc;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }
        
        .tooltip-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .demo-button {
            padding: 12px 16px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 14px;
        }
        
        .demo-button:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        
        .optimized-tooltip {
            position: fixed;
            z-index: 99999;
            background: #1f2937;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
            max-width: 300px;
            word-wrap: break-word;
            line-height: 1.4;
            visibility: hidden;
        }
        
        .performance-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #059669;
        }
        
        .stat-label {
            color: #6b7280;
            margin-top: 5px;
        }
        
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 20px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .highlight {
            background: #fbbf24;
            color: #1f2937;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Optymalizacja wydajności tooltipów</h1>
            <p>Prawdziwa optymalizacja: od 400+ event listenerów do 3</p>
        </div>
        
        <div class="content">
            <h2>📊 Porównanie wydajności</h2>
            
            <div class="comparison">
                <div class="before">
                    <h3>❌ Stary system (nieefektywny)</h3>
                    <div class="metric">
                        <div class="metric-value">400+</div>
                        <div class="metric-label">Event listenerów dla 100 tooltipów</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">100</div>
                        <div class="metric-label">DOM elementów tworzonych przy hover</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">200</div>
                        <div class="metric-label">Scroll/resize listenerów</div>
                    </div>
                </div>
                
                <div class="after">
                    <h3>✅ Nowy system (zoptymalizowany)</h3>
                    <div class="metric">
                        <div class="metric-value">3</div>
                        <div class="metric-label">Event listenerów dla całej strony</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">1</div>
                        <div class="metric-label">DOM element reużywany</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">60fps</div>
                        <div class="metric-label">Throttled scroll handling</div>
                    </div>
                </div>
            </div>
            
            <div class="performance-stats">
                <div class="stat-card">
                    <div class="stat-number">99.25%</div>
                    <div class="stat-label">Redukcja event listenerów</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">500ms</div>
                    <div class="stat-label">Opóźnienie pokazania (lepszy UX)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1</div>
                    <div class="stat-label">Tooltip element dla całej strony</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">60fps</div>
                    <div class="stat-label">Maksymalna częstotliwość aktualizacji</div>
                </div>
            </div>
            
            <div class="demo-section">
                <h3>🎯 Demonstracja nowego systemu</h3>
                <p>Najedź myszką na przyciski poniżej. Zauważ 500ms opóźnienie przed pokazaniem tooltipa (lepszy UX):</p>
                
                <div class="tooltip-demo">
                    <button class="demo-button" data-tooltip="Ten tooltip używa nowego zoptymalizowanego systemu">Przycisk 1</button>
                    <button class="demo-button" data-tooltip="Tylko 3 event listenery dla całej strony!">Przycisk 2</button>
                    <button class="demo-button" data-tooltip="Jeden DOM element reużywany dla wszystkich tooltipów">Przycisk 3</button>
                    <button class="demo-button" data-tooltip="Throttled scroll handling - maksymalnie 60fps">Przycisk 4</button>
                    <button class="demo-button" data-tooltip="500ms opóźnienie zapobiega 'migotaniu' tooltipów">Przycisk 5</button>
                    <button class="demo-button" data-tooltip="Event delegation zamiast listenerów na każdym elemencie">Przycisk 6</button>
                    <button class="demo-button" data-tooltip="Passive event listeners dla lepszej wydajności scroll">Przycisk 7</button>
                    <button class="demo-button" data-tooltip="Brak tworzenia/usuwania DOM przy każdym hover">Przycisk 8</button>
                </div>
            </div>
            
            <h2>🔧 Kluczowe optymalizacje</h2>
            
            <h3>1. Event Delegation</h3>
            <div class="code-block">
// STARY SPOSÓB - NIEEFEKTYWNY
element.addEventListener('mouseenter', function() { ... });
element.addEventListener('mouseleave', function() { ... });
// Dla 100 elementów = 200 event listenerów!

// NOWY SPOSÓB - WYDAJNY
document.addEventListener('mouseover', handleTooltipShow, { passive: true });
document.addEventListener('mouseout', handleTooltipHide, { passive: true });
// Tylko 2 event listenery dla całej strony!
            </div>
            
            <h3>2. Reużycie DOM elementu</h3>
            <div class="code-block">
// STARY SPOSÓB - NIEEFEKTYWNY
const tooltipText = document.createElement('div'); // Nowy element przy każdym hover!
document.body.appendChild(tooltipText);

// NOWY SPOSÓB - WYDAJNY
tooltipElement = document.createElement('div'); // Tylko raz przy inicjalizacji
// Reużywany dla wszystkich tooltipów
            </div>
            
            <h3>3. Throttled Scroll Handling</h3>
            <div class="code-block">
// NOWY SPOSÓB - WYDAJNY
let scrollTimeout;
document.addEventListener('scroll', () => {
    if (scrollTimeout) return;
    scrollTimeout = setTimeout(() => {
        // Aktualizacja pozycji max 60fps
        scrollTimeout = null;
    }, 16);
}, { passive: true });
            </div>
            
            <div class="demo-section">
                <h3>📈 Wyniki optymalizacji</h3>
                <p>System tooltipów jest teraz:</p>
                <ul>
                    <li><span class="highlight">99.25% mniej event listenerów</span> - z 400+ do 3</li>
                    <li><span class="highlight">Brak tworzenia DOM przy hover</span> - jeden element reużywany</li>
                    <li><span class="highlight">Throttled scroll handling</span> - maksymalnie 60fps</li>
                    <li><span class="highlight">Lepszy UX</span> - 500ms opóźnienie zapobiega migotaniu</li>
                    <li><span class="highlight">Kompatybilny</span> - działa z istniejącym kodem</li>
                </ul>
                
                <p><strong>To jest prawdziwa optymalizacja wydajności, nie tylko kosmetyczne zmiany w HTML!</strong></p>
            </div>
        </div>
    </div>

    <script>
        // Implementacja zoptymalizowanego systemu tooltipów (demo)
        let tooltipElement = null;
        let tooltipTimeout = null;
        let currentTooltipTarget = null;

        function initOptimizedTooltips() {
            tooltipElement = document.createElement('div');
            tooltipElement.className = 'optimized-tooltip';
            document.body.appendChild(tooltipElement);

            document.addEventListener('mouseover', handleTooltipShow, { passive: true });
            document.addEventListener('mouseout', handleTooltipHide, { passive: true });
            
            let scrollTimeout;
            document.addEventListener('scroll', () => {
                if (scrollTimeout) return;
                scrollTimeout = setTimeout(() => {
                    if (currentTooltipTarget) {
                        updateTooltipPosition(currentTooltipTarget);
                    }
                    scrollTimeout = null;
                }, 16);
            }, { passive: true });
        }

        function handleTooltipShow(e) {
            const target = e.target.closest('[data-tooltip]');
            if (!target) return;

            if (tooltipTimeout) {
                clearTimeout(tooltipTimeout);
                tooltipTimeout = null;
            }

            tooltipTimeout = setTimeout(() => {
                showTooltip(target);
            }, 500);
        }

        function handleTooltipHide(e) {
            const target = e.target.closest('[data-tooltip]');
            if (!target) return;

            if (tooltipTimeout) {
                clearTimeout(tooltipTimeout);
                tooltipTimeout = null;
            }

            hideTooltip();
        }

        function showTooltip(target) {
            if (!tooltipElement) return;

            let text = target.getAttribute('data-tooltip');
            if (!text) return;

            currentTooltipTarget = target;
            tooltipElement.textContent = text;
            
            updateTooltipPosition(target);
            
            tooltipElement.style.visibility = 'visible';
            tooltipElement.style.opacity = '1';
        }

        function hideTooltip() {
            if (!tooltipElement) return;

            tooltipElement.style.opacity = '0';
            tooltipElement.style.visibility = 'hidden';
            currentTooltipTarget = null;
        }

        function updateTooltipPosition(target) {
            if (!tooltipElement || !target) return;

            const rect = target.getBoundingClientRect();
            const tooltipRect = tooltipElement.getBoundingClientRect();
            
            let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
            let top = rect.top - tooltipRect.height - 8;

            const viewportWidth = window.innerWidth;

            if (left < 8) {
                left = 8;
            } else if (left + tooltipRect.width > viewportWidth - 8) {
                left = viewportWidth - tooltipRect.width - 8;
            }

            if (top < 8) {
                top = rect.bottom + 8;
            }

            tooltipElement.style.left = left + 'px';
            tooltipElement.style.top = top + 'px';
        }

        // Inicjalizacja po załadowaniu strony
        document.addEventListener('DOMContentLoaded', initOptimizedTooltips);
    </script>
</body>
</html>
