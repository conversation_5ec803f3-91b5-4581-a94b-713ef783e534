<!DOCTYPE html>
<html lang="pl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Panel Administracyjny' ?> - <?= APP_NAME ?></title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&family=Inter:wght@400;500;600;700;900&family=Noto+Sans:wght@400;500;700;900" />

    <!-- Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', 'Noto Sans', sans-serif;
        }

        /* Prevent horizontal scrollbar */
        html,
        body {
            overflow-x: hidden;
        }

        /* Ensure content doesn't overflow horizontally */
        .main-content {
            overflow-x: hidden;
            width: 100%;
        }

        /* Status colors */
        .status-working {
            @apply bg-green-100 text-green-800 border-green-200;
        }

        .status-ready {
            @apply bg-blue-100 text-blue-800 border-blue-200;
        }

        .status-finished {
            @apply bg-gray-100 text-gray-800 border-gray-200;
        }

        .status-no-appointments {
            @apply bg-slate-100 text-slate-800 border-slate-200;
        }

        /* Patient status colors */
        .patient-waiting {
            @apply bg-blue-50 border-blue-200;
        }

        .patient-current {
            @apply bg-green-50 border-green-200;
        }

        .patient-completed {
            @apply bg-gray-50 border-gray-200;
        }

        .patient-delayed {
            @apply bg-red-50 border-red-200;
        }

        /* Custom scrollbar */
        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Line clamp utility */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Compact queue view */
        .queue-compact {
            max-height: 400px;
        }

        /* Animation for current patient */
        @keyframes pulse-green {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        .animate-pulse-green {
            animation: pulse-green 2s infinite;
        }
    </style>

    <!-- Tooltip Styles -->
    <style>
        /* Tooltip container */
        .tooltip {
            position: relative;
            display: inline-block;
        }

        /* Tooltip text */
        .tooltiptext {
            visibility: hidden;
            width: max-content;
            max-width: 300px;
            background-color: #1f2937;
            color: white;
            text-align: center;
            border-radius: 6px;
            padding: 8px 12px;
            position: fixed;
            z-index: 99999;
            opacity: 0;
            transition: opacity 0.3s, visibility 0.3s;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            pointer-events: none;
            line-height: 1.4;
            white-space: normal;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        /* Tooltip arrow */
        .tooltiptext::after {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #1f2937;
        }

        /* Show the tooltip when hovering over the tooltip container */
        .tooltip:hover #dynamic-tooltip,
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        /* Tooltip positioning variations */
        .tooltip.top .tooltiptext {
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
        }

        .tooltip.top .tooltiptext::after {
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #1f2937 transparent transparent transparent;
        }

        .tooltip.bottom .tooltiptext {
            top: 125%;
            bottom: auto;
        }

        .tooltip.bottom .tooltiptext::after {
            top: auto;
            bottom: 100%;
            border-color: transparent transparent #1f2937 transparent;
        }

        .tooltip.left .tooltiptext {
            top: 50%;
            right: 125%;
            left: auto;
            bottom: auto;
            transform: translateY(-50%);
        }

        .tooltip.left .tooltiptext::after {
            top: 50%;
            left: 100%;
            right: auto;
            margin-left: 0;
            margin-top: -5px;
            border-color: transparent transparent transparent #1f2937;
        }

        .tooltip.right .tooltiptext {
            top: 50%;
            left: 125%;
            right: auto;
            bottom: auto;
            transform: translateY(-50%);
        }

        .tooltip.right .tooltiptext::after {
            top: 50%;
            right: 100%;
            left: auto;
            margin-left: 0;
            margin-top: -5px;
            border-color: transparent #1f2937 transparent transparent;
        }

        /* Tooltip for disabled elements */
        .tooltip.disabled .tooltiptext {
            background-color: #6b7280;
        }

        .tooltip.disabled .tooltiptext::after {
            border-color: #6b7280 transparent transparent transparent;
        }

        .tooltip.disabled.bottom .tooltiptext::after {
            border-color: transparent transparent #6b7280 transparent;
        }

        .tooltip.disabled.left .tooltiptext::after {
            border-color: transparent transparent transparent #6b7280;
        }

        .tooltip.disabled.right .tooltiptext::after {
            border-color: transparent #6b7280 transparent transparent;
        }

        /* Special tooltip styles for different contexts */
        .tooltip.success .tooltiptext {
            background-color: #059669;
        }

        .tooltip.success .tooltiptext::after {
            border-color: #059669 transparent transparent transparent;
        }

        .tooltip.success.bottom .tooltiptext::after {
            border-color: transparent transparent #059669 transparent;
        }

        .tooltip.success.left .tooltiptext::after {
            border-color: transparent transparent transparent #059669;
        }

        .tooltip.success.right .tooltiptext::after {
            border-color: transparent #059669 transparent transparent;
        }

        .tooltip.warning .tooltiptext {
            background-color: #d97706;
        }

        .tooltip.warning .tooltiptext::after {
            border-color: #d97706 transparent transparent transparent;
        }

        .tooltip.warning.bottom .tooltiptext::after {
            border-color: transparent transparent #d97706 transparent;
        }

        .tooltip.warning.left .tooltiptext::after {
            border-color: transparent transparent transparent #d97706;
        }

        .tooltip.warning.right .tooltiptext::after {
            border-color: transparent #d97706 transparent transparent;
        }

        .tooltip.error .tooltiptext {
            background-color: #dc2626;
        }

        .tooltip.error .tooltiptext::after {
            border-color: #dc2626 transparent transparent transparent;
        }

        .tooltip.error.bottom .tooltiptext::after {
            border-color: transparent transparent #dc2626 transparent;
        }

        .tooltip.error.left .tooltiptext::after {
            border-color: transparent transparent transparent #dc2626;
        }

        .tooltip.error.right .tooltiptext::after {
            border-color: transparent #dc2626 transparent transparent;
        }

        /* Small tooltip variation */
        .tooltip.small .tooltiptext {
            font-size: 12px;
            padding: 6px 10px;
        }

        /* Large tooltip variation */
        .tooltip.large .tooltiptext {
            font-size: 16px;
            padding: 10px 14px;
            max-width: 300px;
        }
    </style>
</head>

<body class="bg-slate-50 text-slate-900">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-white border-b border-slate-200 shadow-sm sticky top-0 z-20">
            <div class="w-full px-6">
                <div class="flex items-center justify-between h-14">
                    <!-- Logo and Title -->
                    <div class="flex items-center gap-4">
                        <div class="w-10 h-10 rounded-lg flex items-center justify-center" data-tooltip="System zarządzania kolejkami">
                            <img src="/admin/assets/logo-web.webp" alt="<?= APP_NAME ?> Logo" class="w-full h-full object-contain rounded-lg">
                        </div>
                        <div>
                            <h1 class="text-lg font-bold text-slate-900" data-tooltip="System zarządzania kolejkami"><?= APP_NAME ?></h1>
                            <p class="text-xs text-slate-600">System zarządzania kolejkami</p>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <nav class="hidden md:flex items-center space-x-4">
                        <a href="/admin/pulpit" class="px-3 py-2 text-sm font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100 rounded-lg transition-colors" title="Przejdź do pulpitu głównego">
                            <span class="material-icons-outlined text-sm mr-1">dashboard</span>
                            Pulpit
                        </a>
                        <a href="/admin/kolejki" class="px-3 py-2 text-sm font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100 rounded-lg transition-colors" title="Zarządzaj kolejkami pacjentów">
                            <span class="material-icons-outlined text-sm mr-1">queue</span>
                            Kolejki
                        </a>
                        <a href="/admin/lekarze" class="px-3 py-2 text-sm font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100 rounded-lg transition-colors" title="Zarządzaj lekarzami">
                            <span class="material-icons-outlined text-sm mr-1">people</span>
                            Lekarze
                        </a>
                        <a href="/admin/wyswietlacze" class="px-3 py-2 text-sm font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100 rounded-lg transition-colors" title="Zarządzaj wyświetlaczami">
                            <span class="material-icons-outlined text-sm mr-1">tv</span>
                            Wyświetlacze
                        </a>
                        <a href="/admin/video" class="px-3 py-2 text-sm font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100 rounded-lg transition-colors" title="Zarządzaj video">
                            <span class="material-icons-outlined text-sm mr-1">video_library</span>
                            Video
                        </a>
                        <a href="/admin/gabinety" class="px-3 py-2 text-sm font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100 rounded-lg transition-colors" title="Zarządzaj gabinetami">
                            <span class="material-icons-outlined text-sm mr-1">meeting_room</span>
                            Gabinety
                        </a>
                        
                        <!-- Przycisk Pomoc -->
                        <button id="help-toggle" onclick="toggleHelpVisibility()" class="px-3 py-2 text-sm font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100 rounded-lg transition-colors" title="Pokaż/Ukryj instrukcje obsługi">
                            <span class="material-icons-outlined text-sm mr-1">help_outline</span>
                            Pomoc
                        </button>

                    </nav>

                    <!-- User menu and controls -->
                    <div class="flex items-center gap-4">
                        <!-- Current Time -->
                        <div class="text-right hidden sm:block">
                            <div class="text-sm font-semibold text-slate-900" id="current-time"><?= date('H:i:s') ?></div>
                            <div class="text-xs text-slate-600"><?= formatDate(date('Y-m-d')) ?></div>
                        </div>

                        <!-- User info -->
                        <div class="flex items-center gap-2">
                            <?php if (isset($user) && $user): ?>
                                <div class="text-right hidden sm:block">
                                    <div class="text-sm font-medium text-slate-900"><?= htmlspecialchars($user['username'] ?? '') ?></div> <!-- UWAGA: Kolumna company_name została usunięta -->
                                    <div class="text-xs text-slate-600">&nbsp;</div>
                                </div>
                            <?php endif; ?>

                            <!-- Settings -->
                            <a href="/admin/ustawienia" class="p-2 rounded-lg hover:bg-slate-100 transition-colors" title="Ustawienia">
                                <span class="material-icons-outlined text-slate-600">settings</span>
                            </a>

                            <!-- Logout -->
                            <a href="/admin/logout" class="p-2 rounded-lg hover:bg-slate-100 transition-colors" title="Wyloguj">
                                <span class="material-icons-outlined text-slate-600">logout</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 w-full">
            <?= $content ?>
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-slate-200 py-4">
            <div class="w-full px-6">
                <div class="flex items-center justify-between text-sm text-slate-600">
                    <div>
                        <?= APP_NAME ?> v<?= APP_VERSION ?> &copy; <?= date('Y') ?>
                    </div>
                    <div class="flex items-center gap-4">
                        <span>Ostatnia aktualizacja: <span id="last-update"><?= date('H:i:s') ?></span></span>
                        <button onclick="location.reload()" class="text-indigo-600 hover:text-indigo-800 transition-colors" title="Odśwież stronę">
                            <span class="material-icons-outlined text-sm">refresh</span>
                        </button>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('pl-PL');
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }

            const updateElement = document.getElementById('last-update');
            if (updateElement) {
                updateElement.textContent = timeString;
            }
        }

        setInterval(updateTime, 1000);
        updateTime();

        // Smart auto refresh - only for queue management page
        let lastGlobalUpdate = null;
        
        function checkForChangesAndReload() {
            // Sprawdź czy jesteśmy na stronie zarządzania kolejkami
            const currentPath = window.location.pathname;
            const isQueuePage = currentPath.includes('/admin/kolejki') || currentPath.includes('/admin/queue');

            if (!isQueuePage || document.visibilityState !== 'visible') {
                return;
            }

            // Sprawdź czy monitoring jest zablokowany (np. podczas importu)
            if (typeof window.cacheMonitoringBlocked !== 'undefined' && window.cacheMonitoringBlocked) {
                console.log('Layout cache monitoring zablokowany - pomijanie sprawdzania zmian');
                return;
            }

            // Sprawdź zmiany w cache (używamy endpoint changes)
            fetch('/api/v2/changes/appointments')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        const currentGlobalUpdate = data.data.globalLastUpdate;
                        
                        // Jeśli to pierwsze sprawdzenie, zapisz timestamp
                        if (lastGlobalUpdate === null) {
                            lastGlobalUpdate = currentGlobalUpdate;
                            return;
                        }
                        
                        // Sprawdź czy globalny timestamp się zmienił
                        if (currentGlobalUpdate > lastGlobalUpdate) {
                            console.log('Wykryto zmiany w systemie, odświeżam panel...');
                            console.log('Stary timestamp:', lastGlobalUpdate, 'Nowy timestamp:', currentGlobalUpdate);
                            location.reload();
                        }
                        
                        lastGlobalUpdate = currentGlobalUpdate;
                    }
                })
                .catch(error => {
                    console.error('Błąd sprawdzania zmian:', error);
                });
        }
        
        // Sprawdzaj zmiany co 3 sekundy
        setInterval(checkForChangesAndReload, 3000);

        // Scroll to current appointment on page load
        function scrollToCurrentAppointment() {
            const currentAppointment = document.querySelector('.patient-current');
            if (currentAppointment) {
                currentAppointment.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
                // Highlight current appointment briefly
                currentAppointment.style.boxShadow = '0 0 10px rgba(34, 197, 94, 0.5)';
                setTimeout(() => {
                    currentAppointment.style.boxShadow = '';
                }, 2000);
            }
        }

        // Run on page load
        document.addEventListener('DOMContentLoaded', scrollToCurrentAppointment);

        // Also run after a short delay to ensure all content is loaded
        setTimeout(scrollToCurrentAppointment, 500);

        // AJAX helper functions
        function callPatient(appointmentId) {
            fetch('/admin/queue/call-patient', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'appointment_id=' + appointmentId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Błąd: ' + (data.message || 'Nie udało się wezwać pacjenta'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Wystąpił błąd podczas wzywania pacjenta');
                });
        }

        function completeAppointment(appointmentId) {
            if (confirm('Czy na pewno chcesz zakończyć tę wizytę?')) {
                fetch('/admin/queue/complete-appointment', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'appointment_id=' + appointmentId
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert('Błąd: ' + (data.message || 'Nie udało się zakończyć wizyty'));
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Wystąpił błąd podczas kończenia wizyty');
                    });
            }
        }

        // Date navigation
        function changeDate(direction) {
            const currentDate = new Date('<?= $selectedDate ?? date('Y-m-d') ?>');
            currentDate.setDate(currentDate.getDate() + direction);
            const newDate = currentDate.toISOString().split('T')[0];
            window.location.href = '?date=' + newDate;
        }

        function selectDate() {
            const dateInput = document.getElementById('date-picker');
            if (dateInput.value) {
                window.location.href = '?date=' + dateInput.value;
            }
        }

        // Filter functions
        function toggleFilter() {
            const withAppointments = document.getElementById('filter-with-appointments').checked;
            const withoutAppointments = document.getElementById('filter-without-appointments').checked;
            const working = document.getElementById('filter-working').checked;

            const doctorCards = document.querySelectorAll('.doctor-card');

            doctorCards.forEach(card => {
                const hasAppointments = card.dataset.hasAppointments === 'true';
                const status = card.dataset.status;

                let show = false;

                if (hasAppointments && withAppointments) {
                    show = true;
                } else if (!hasAppointments && withoutAppointments) {
                    show = true;
                }

                if (working && status === 'working') {
                    show = true;
                }

                // If no filters are checked, show all
                if (!withAppointments && !withoutAppointments && !working) {
                    show = true;
                }

                card.style.display = show ? 'block' : 'none';
            });
        }

        // ZOPTYMALIZOWANY SYSTEM TOOLTIPÓW
        // Używa event delegation i pojedynczy tooltip element dla całej strony

        let tooltipElement = null;
        let tooltipTimeout = null;
        let currentTooltipTarget = null;

        // Inicjalizacja zoptymalizowanego systemu tooltipów
        function initOptimizedTooltips() {
            // Utwórz jeden tooltip element dla całej strony
            tooltipElement = document.createElement('div');
            tooltipElement.className = 'optimized-tooltip';
            tooltipElement.style.cssText = `
                position: fixed;
                z-index: 99999;
                background: #1f2937;
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.2s ease;
                max-width: 300px;
                word-wrap: break-word;
                line-height: 1.4;
                visibility: hidden;
            `;
            document.body.appendChild(tooltipElement);

            // Event delegation - jeden listener dla całego dokumentu
            document.addEventListener('mouseover', handleTooltipShow, { passive: true });
            document.addEventListener('mouseout', handleTooltipHide, { passive: true });

            // Optymalizacja: throttled scroll listener
            let scrollTimeout;
            document.addEventListener('scroll', () => {
                if (scrollTimeout) return;
                scrollTimeout = setTimeout(() => {
                    if (currentTooltipTarget) {
                        updateTooltipPosition(currentTooltipTarget);
                    }
                    scrollTimeout = null;
                }, 16); // ~60fps
            }, { passive: true });
        }

        function handleTooltipShow(e) {
            const target = e.target.closest('[title], [data-tooltip]');
            if (!target) return;

            // Anuluj poprzedni timeout
            if (tooltipTimeout) {
                clearTimeout(tooltipTimeout);
                tooltipTimeout = null;
            }

            // Opóźnienie 500ms przed pokazaniem (UX improvement)
            tooltipTimeout = setTimeout(() => {
                showTooltip(target);
            }, 500);
        }

        function handleTooltipHide(e) {
            const target = e.target.closest('[title], [data-tooltip]');
            if (!target) return;

            // Anuluj timeout pokazania
            if (tooltipTimeout) {
                clearTimeout(tooltipTimeout);
                tooltipTimeout = null;
            }

            hideTooltip();
        }

        function showTooltip(target) {
            if (!tooltipElement) return;

            // Pobierz tekst tooltipa
            let text = target.getAttribute('data-tooltip') || target.getAttribute('title');
            if (!text) return;

            // Usuń title aby nie pokazywał się natywny tooltip
            if (target.hasAttribute('title')) {
                target.setAttribute('data-original-title', text);
                target.removeAttribute('title');
            }

            currentTooltipTarget = target;
            tooltipElement.textContent = text;

            updateTooltipPosition(target);

            tooltipElement.style.visibility = 'visible';
            tooltipElement.style.opacity = '1';
        }

        function hideTooltip() {
            if (!tooltipElement) return;

            tooltipElement.style.opacity = '0';
            tooltipElement.style.visibility = 'hidden';

            // Przywróć title jeśli był
            if (currentTooltipTarget && currentTooltipTarget.hasAttribute('data-original-title')) {
                const originalTitle = currentTooltipTarget.getAttribute('data-original-title');
                currentTooltipTarget.setAttribute('title', originalTitle);
            }

            currentTooltipTarget = null;
        }

        function updateTooltipPosition(target) {
            if (!tooltipElement || !target) return;

            const rect = target.getBoundingClientRect();
            const tooltipRect = tooltipElement.getBoundingClientRect();

            let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
            let top = rect.top - tooltipRect.height - 8;

            // Sprawdź czy tooltip mieści się w viewport
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            // Korekta pozioma
            if (left < 8) {
                left = 8;
            } else if (left + tooltipRect.width > viewportWidth - 8) {
                left = viewportWidth - tooltipRect.width - 8;
            }

            // Korekta pionowa - jeśli nie ma miejsca u góry, pokaż na dole
            if (top < 8) {
                top = rect.bottom + 8;
            }

            tooltipElement.style.left = left + 'px';
            tooltipElement.style.top = top + 'px';
        }

        // Funkcja pomocnicza do konwersji starych tooltipów na nowy system
        function convertLegacyTooltips() {
            // Znajdź wszystkie elementy z title i zamień na data-tooltip
            const elementsWithTitle = document.querySelectorAll('[title]');
            elementsWithTitle.forEach(element => {
                const title = element.getAttribute('title');
                if (title) {
                    element.setAttribute('data-tooltip', title);
                    element.removeAttribute('title');
                }
            });
        }

        // Funkcja do programowego ustawiania tooltipa (kompatybilność wsteczna)
        function setTooltip(element, text) {
            element.setAttribute('data-tooltip', text);
        }

        // Stara funkcja createTooltip - teraz tylko ustawia atrybut (kompatybilność wsteczna)
        function createTooltip(element, text) {
            setTooltip(element, text);
        }

        // Inicjalizacja systemu tooltipów
        function initTooltips() {
            initOptimizedTooltips();
            convertLegacyTooltips();
        }

        // Initialize tooltips when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initTooltips();
        });

        // Re-initialize tooltips after dynamic content loads
        function reinitializeTooltips() {
            convertLegacyTooltips();
        }


        
        // Funkcja do przełączania widoczności instrukcji
        function toggleHelpVisibility() {
            // Znajdź wszystkie kontenery instrukcji na stronie
            const helpContainers = document.querySelectorAll('.bg-blue-50.border.border-blue-200, .bg-amber-50.border.border-amber-200');
            const helpToggle = document.getElementById('help-toggle');
            const helpIcon = helpToggle.querySelector('.material-icons-outlined');
            
            // Sprawdź aktualny stan widoczności
            let isVisible = true;
            if (helpContainers.length > 0) {
                isVisible = helpContainers[0].style.display !== 'none';
            }
            
            // Przełącz widoczność wszystkich kontenerów instrukcji
            helpContainers.forEach(container => {
                container.style.display = isVisible ? 'none' : 'block';
            });
            
            // Zaktualizuj ikonę i tekst przycisku
            if (isVisible) {
                helpIcon.textContent = 'help';
                helpToggle.title = 'Pokaż instrukcje obsługi';
                // Zapisz stan w localStorage
                localStorage.setItem('help_visible', 'false');
            } else {
                helpIcon.textContent = 'help_outline';
                helpToggle.title = 'Ukryj instrukcje obsługi';
                // Zapisz stan w localStorage
                localStorage.setItem('help_visible', 'true');
            }
        }
        
        // Przywróć stan widoczności instrukcji z localStorage przy ładowaniu strony
        document.addEventListener('DOMContentLoaded', function() {
            const helpVisible = localStorage.getItem('help_visible');
            const helpToggle = document.getElementById('help-toggle');
            const helpIcon = helpToggle.querySelector('.material-icons-outlined');
            
            if (helpVisible === 'false') {
                // Ukryj wszystkie kontenery instrukcji
                const helpContainers = document.querySelectorAll('.bg-blue-50.border.border-blue-200, .bg-amber-50.border.border-amber-200');
                helpContainers.forEach(container => {
                    container.style.display = 'none';
                });
                
                // Zaktualizuj ikonę przycisku
                helpIcon.textContent = 'help';
                helpToggle.title = 'Pokaż instrukcje obsługi';
            }
        });
    </script>
</body>

</html>