/**
 * DrEryk Import Module
 * Obsługuje import danych z systemu drEryk
 */

// Globalne zmienne dla modułu drEryk
let drErykFileContent = null;
let drErykConvertedData = null;
let drErykConverter = null;

/**
 * Inicjalizacja modułu importu drEryk
 */
window.initializeDrErykImport = function() {
    console.log('=== ROZPOCZĘCIE INICJALIZACJI MODALU DRERYK ===');

    const fileInput = document.getElementById('drErykFileInput');
    const fileDropArea = document.getElementById('drErykFileDropArea');
    const uploadBtn = document.getElementById('modalUploadBtn');
    const applySettingsBtn = document.getElementById('applySettingsBtn');

    console.log('Inicjalizacja modalu drEryk:');
    console.log('- fileInput:', fileInput ? 'znaleziony' : 'nie znaleziony', fileInput);
    console.log('- fileDropArea:', fileDropArea ? 'znaleziony' : 'nie znaleziony', fileDropArea);
    console.log('- uploadBtn:', uploadBtn ? 'znaleziony' : 'nie znaleziony', uploadBtn);
    console.log('- applySettingsBtn:', applySettingsBtn ? 'znaleziony' : 'nie znaleziony', applySettingsBtn);

    if (!fileInput || !fileDropArea || !uploadBtn || !applySettingsBtn) {
        console.error('Niektóre elementy modalu drEryk nie zostały znalezione');
        return;
    }

    // Wczytaj zapisane ustawienia z localStorage
    loadDrErykSettingsFromStorage();

    // Dodaj event listenery do pól ustawień dla automatycznego zapisu
    setupDrErykSettingsAutoSave();

    // Obsługa przeciągania plików
    if (fileDropArea) {
        fileDropArea.addEventListener('click', () => {
            console.log('Kliknięto na obszar wyboru pliku');
            fileInput.click();
        });

        fileDropArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileDropArea.classList.add('border-orange-400', 'bg-orange-50');
        });

        fileDropArea.addEventListener('dragleave', () => {
            fileDropArea.classList.remove('border-orange-400', 'bg-orange-50');
        });

        fileDropArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileDropArea.classList.remove('border-orange-400', 'bg-orange-50');
            console.log('Upuszczono plik:', e.dataTransfer.files);
            if (e.dataTransfer.files.length > 0) {
                handleDrErykFileSelect(e.dataTransfer.files[0]);
            }
        });
    }

    // Obsługa wyboru pliku
    if (fileInput) {
        fileInput.addEventListener('change', (e) => {
            console.log('Wybrano plik:', e.target.files);
            if (e.target.files.length > 0) {
                handleDrErykFileSelect(e.target.files[0]);
            }
        });
    }

    // Przyciski
    applySettingsBtn.addEventListener('click', applyDrErykSettings);
    uploadBtn.addEventListener('click', uploadDrErykData);
};

/**
 * Obsługa wyboru pliku
 */
window.handleDrErykFileSelect = async function(file) {
    if (!file.name.endsWith('.csv')) {
        showDrErykAlert('Proszę wybrać plik CSV', 'danger');
        return;
    }

    try {
        const fileContent = await readDrErykFileAsText(file);
        window.drErykFileContent = fileContent;
        showDrErykAlert(`Plik "${file.name}" został wczytany pomyślnie`, 'success');

        // Przejdź do kroku 2
        document.getElementById('importStep1').classList.add('hidden');
        document.getElementById('importStep2').classList.remove('hidden');

        // Automatycznie przeanalizuj plik z domyślnymi ustawieniami
        processDrErykDataWithSettings();

        showDrErykAlert('Dane zostały pomyślnie przeanalizowane. Możesz dostosować ustawienia i zastosować zmiany.', 'success');
    } catch (error) {
        showDrErykAlert('Błąd podczas wczytywania pliku: ' + error.message, 'danger');
        console.error('File reading error:', error);
    }
};

/**
 * Czytanie pliku jako tekst z kodowaniem Windows-1250
 */
window.readDrErykFileAsText = function(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            try {
                const arrayBuffer = e.target.result;
                const decoder = new TextDecoder('windows-1250');
                const text = decoder.decode(arrayBuffer);
                resolve(text);
            } catch (error) {
                reject(new Error('Błąd dekodowania pliku: ' + error.message));
            }
        };
        
        reader.onerror = function() {
            reject(new Error('Błąd odczytu pliku'));
        };
        
        reader.readAsArrayBuffer(file);
    });
};

/**
 * Przetwarzanie danych z ustawieniami
 */
window.processDrErykDataWithSettings = function() {
    const settings = {
        outsideScheduleStartTime: document.getElementById('outsideScheduleStartTime').value,
        outsideScheduleDuration: parseInt(document.getElementById('outsideScheduleDuration').value),
        repeatRpStartTime: document.getElementById('repeatRpStartTime').value,
        repeatRpDuration: parseInt(document.getElementById('repeatRpDuration').value),
        removeOutsideSchedule: document.getElementById('removeOutsideSchedule').checked,
        randomizeOutsideSchedule: document.getElementById('randomizeOutsideSchedule').checked
    };
    
    console.log('Przetwarzanie danych z ustawieniami:', settings);
    
    if (!window.drErykConverter) {
        window.drErykConverter = new DrErykConverter();
    }
    
    const convertedData = window.drErykConverter.convertDataFromString(window.drErykFileContent, settings);
    window.drErykConvertedData = convertedData;
    
    console.log('Przetworzone dane:', convertedData);
    
    displayModalSummary(convertedData);
};

/**
 * Zastosowanie ustawień
 */
window.applyDrErykSettings = function() {
    if (!window.drErykFileContent) {
        showDrErykAlert('Brak danych pliku do ponownego przetworzenia', 'warning');
        return;
    }

    console.log('Zastosuj zmiany - rozpoczęto');
    
    // Zapisz aktualne ustawienia do localStorage
    saveDrErykSettingsToStorage();
    
    processDrErykDataWithSettings();

    displayModalSummary(window.drErykConvertedData);

    showDrErykAlert('Ustawienia zostały zastosowane', 'success');
    console.log('Zastosuj zmiany - zakończono');
};

/**
 * Zapisuje ustawienia do localStorage
 */
window.saveDrErykSettingsToStorage = function() {
    const settings = {
        outsideScheduleStartTime: document.getElementById('outsideScheduleStartTime').value,
        outsideScheduleDuration: parseInt(document.getElementById('outsideScheduleDuration').value),
        repeatRpStartTime: document.getElementById('repeatRpStartTime').value,
        repeatRpDuration: parseInt(document.getElementById('repeatRpDuration').value),
        removeOutsideSchedule: document.getElementById('removeOutsideSchedule').checked,
        randomizeOutsideSchedule: document.getElementById('randomizeOutsideSchedule').checked
    };

    try {
        localStorage.setItem('drErykImportSettings', JSON.stringify(settings));
        console.log('Ustawienia zapisane w localStorage:', settings);
    } catch (error) {
        console.error('Błąd podczas zapisywania ustawień do localStorage:', error);
    }
};

/**
 * Wczytuje ustawienia z localStorage
 */
window.loadDrErykSettingsFromStorage = function() {
    try {
        const savedSettings = localStorage.getItem('drErykImportSettings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            console.log('Wczytano ustawienia z localStorage:', settings);

            // Ustaw wartości pól formularza
            const outsideScheduleStartTime = document.getElementById('outsideScheduleStartTime');
            const outsideScheduleDuration = document.getElementById('outsideScheduleDuration');
            const repeatRpStartTime = document.getElementById('repeatRpStartTime');
            const repeatRpDuration = document.getElementById('repeatRpDuration');
            const removeOutsideSchedule = document.getElementById('removeOutsideSchedule');
            const randomizeOutsideSchedule = document.getElementById('randomizeOutsideSchedule');

            if (outsideScheduleStartTime && settings.outsideScheduleStartTime) {
                outsideScheduleStartTime.value = settings.outsideScheduleStartTime;
            }
            if (outsideScheduleDuration && (settings.outsideScheduleDuration !== undefined && settings.outsideScheduleDuration !== null)) {
                outsideScheduleDuration.value = settings.outsideScheduleDuration;
            }
            if (repeatRpStartTime && settings.repeatRpStartTime) {
                repeatRpStartTime.value = settings.repeatRpStartTime;
            }
            if (repeatRpDuration && (settings.repeatRpDuration !== undefined && settings.repeatRpDuration !== null)) {
                repeatRpDuration.value = settings.repeatRpDuration;
            }
            if (removeOutsideSchedule && typeof settings.removeOutsideSchedule === 'boolean') {
                removeOutsideSchedule.checked = settings.removeOutsideSchedule;
            }
            if (randomizeOutsideSchedule && typeof settings.randomizeOutsideSchedule === 'boolean') {
                randomizeOutsideSchedule.checked = settings.randomizeOutsideSchedule;
            }

            console.log('Ustawienia zostały wczytane i zastosowane');
        } else {
            console.log('Brak zapisanych ustawień w localStorage');
        }
    } catch (error) {
        console.error('Błąd podczas wczytywania ustawień z localStorage:', error);
    }
};

/**
 * Konfiguruje automatyczny zapis ustawień przy zmianie wartości pól
 */
window.setupDrErykSettingsAutoSave = function() {
    const settingsFields = [
        'outsideScheduleStartTime',
        'outsideScheduleDuration',
        'repeatRpStartTime',
        'repeatRpDuration',
        'removeOutsideSchedule',
        'randomizeOutsideSchedule'
    ];

    settingsFields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element) {
            // Dla pól typu checkbox używamy 'change', dla reszty 'input' i 'change'
            const eventType = element.type === 'checkbox' ? 'change' : 'input';

            element.addEventListener(eventType, () => {
                console.log(`Zmieniono ustawienie: ${fieldId}`);

                // Obsługa logiki między checkboxami
                handleCheckboxLogic(fieldId);

                saveDrErykSettingsToStorage();
            });

            // Dodaj też event 'change' dla pól time i number dla pewności
            if (element.type !== 'checkbox') {
                element.addEventListener('change', () => {
                    console.log(`Zmieniono ustawienie (change): ${fieldId}`);
                    saveDrErykSettingsToStorage();
                });
            }
        }
    });

    // Ustaw początkowy stan checkboxów
    handleCheckboxLogic('removeOutsideSchedule');

    console.log('Skonfigurowano automatyczny zapis ustawień');
};

/**
 * Obsługuje logikę między checkboxami
 */
window.handleCheckboxLogic = function(changedFieldId) {
    const removeOutsideSchedule = document.getElementById('removeOutsideSchedule');
    const randomizeOutsideSchedule = document.getElementById('randomizeOutsideSchedule');

    if (!removeOutsideSchedule || !randomizeOutsideSchedule) {
        return;
    }

    // Jeśli zaznaczono "Usuń wizyty poza kolejką", wyłącz "Pomieszaj losowo"
    if (changedFieldId === 'removeOutsideSchedule' || changedFieldId === 'randomizeOutsideSchedule') {
        if (removeOutsideSchedule.checked) {
            randomizeOutsideSchedule.disabled = true;
            randomizeOutsideSchedule.checked = false;

            // Dodaj wizualną wskazówkę
            const randomizeLabel = randomizeOutsideSchedule.closest('label');
            if (randomizeLabel) {
                randomizeLabel.classList.add('opacity-50');
                randomizeLabel.title = 'Opcja niedostępna gdy wizyty poza kolejką są usuwane';
            }
        } else {
            randomizeOutsideSchedule.disabled = false;

            // Usuń wizualną wskazówkę
            const randomizeLabel = randomizeOutsideSchedule.closest('label');
            if (randomizeLabel) {
                randomizeLabel.classList.remove('opacity-50');
                randomizeLabel.title = '';
            }
        }
    }
};

/**
 * Wyświetlanie podsumowania w modalu
 */
window.displayModalSummary = function(convertedData) {
    const totalAppointments = convertedData.doctors.reduce((sum, doctor) => sum + doctor.appointments.length, 0);
    const totalDoctors = convertedData.doctors.length;
    const outsideSchedule = convertedData.outsideScheduleCount || convertedData.doctors.reduce((sum, doctor) => {
        return sum + doctor.appointments.filter(apt =>
            apt.was_outside_schedule || apt.original_appointment_time === 'poza kol.' || apt.original_appointment_time === 'Powtórka Rp.'
        ).length;
    }, 0);
    const skippedCount = convertedData.skippedData ? convertedData.skippedData.length : 0;
    
    document.getElementById('modalTotalAppointments').textContent = totalAppointments;
    document.getElementById('modalTotalDoctors').textContent = totalDoctors;
    document.getElementById('modalOutsideSchedule').textContent = outsideSchedule;
    document.getElementById('modalSkippedAppointments').textContent = skippedCount;
    
    // Informacje o pominiętych wizytach
    if (skippedCount > 0) {
        document.getElementById('modalSkippedInfo').classList.remove('hidden');
        document.getElementById('modalSkippedCount').textContent = skippedCount;
        
        const tableBody = document.getElementById('modalSkippedTableBody');
        tableBody.innerHTML = convertedData.skippedData.map(item => `
            <tr class="border-b border-amber-100">
                <td class="py-1 px-2">${item.lp}</td>
                <td class="py-1 px-2">${item.pacjent || '-'}</td>
                <td class="py-1 px-2">${item.lekarz}</td>
                <td class="py-1 px-2">${item.skipReason}</td>
            </tr>
        `).join('');
    } else {
        document.getElementById('modalSkippedInfo').classList.add('hidden');
    }
    
    // Podgląd danych
    const previewContainer = document.getElementById('modalDoctorsPreview');
    previewContainer.innerHTML = '';
    
    convertedData.doctors.forEach(doctor => {
        const doctorSection = document.createElement('div');
        doctorSection.className = 'border border-slate-200 rounded-lg overflow-hidden';
        
        doctorSection.innerHTML = `
            <div class="bg-slate-50 px-4 py-2 border-b border-slate-200">
                <h5 class="font-medium text-slate-900">Lekarz: ${doctor.doctorName} (${doctor.appointments.length} wizyt)</h5>
            </div>
            <div class="p-4">
                <div class="overflow-x-auto">
                    <table class="min-w-full text-sm">
                        <thead>
                            <tr class="border-b border-slate-200">
                                <th class="text-left py-1 px-2">Godzina</th>
                                <th class="text-left py-1 px-2">Pacjent</th>
                                <th class="text-left py-1 px-2">Telefon</th>
                                <th class="text-left py-1 px-2">Czas trwania</th>
                                <th class="text-left py-1 px-2">ID wizyty</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${doctor.appointments.map(appointment => {
                                const isOutsideSchedule = appointment.was_outside_schedule ||
                                                      appointment.original_appointment_time === 'poza kol.' ||
                                                      appointment.original_appointment_time === 'Powtórka Rp.';
                                const rowClass = isOutsideSchedule ? 'bg-slate-100' : 'border-b border-slate-100';
                                
                                let displayTime = appointment.appointment_time;
                                let timeInfo = '';
                                if (isOutsideSchedule && appointment.original_appointment_time !== appointment.appointment_time) {
                                    timeInfo = ` (oryg. ${appointment.original_appointment_time})`;
                                }
                                
                                return `
                                    <tr class="${rowClass}">
                                        <td class="py-1 px-2 font-medium">${displayTime}${timeInfo}</td>
                                        <td class="py-1 px-2">${appointment.patient_name}</td>
                                        <td class="py-1 px-2">${appointment.phone_number || 'brak'}</td>
                                        <td class="py-1 px-2">${appointment.appointment_duration || 'brak'} min</td>
                                        <td class="py-1 px-2">${appointment.external_id || 'brak'}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        
        previewContainer.appendChild(doctorSection);
    });
    
    // Włącz przycisk importu
    const uploadBtn = document.getElementById('modalUploadBtn');
    if (uploadBtn) uploadBtn.disabled = false;
};

/**
 * Upload danych do API
 */
window.uploadDrErykData = function() {
    if (!window.drErykConvertedData) {
        showDrErykAlert('Brak danych do importu', 'warning');
        return;
    }

    // Zablokuj sprawdzanie zmian w tle podczas importu
    try {
        if (typeof window.blockCacheMonitoring === 'function') {
            window.blockCacheMonitoring();
            console.log('Cache monitoring zablokowany przed importem');
        } else if (typeof blockCacheMonitoring === 'function') {
            blockCacheMonitoring();
            console.log('Cache monitoring zablokowany przed importem (lokalna funkcja)');
        } else {
            console.warn('Funkcja blockCacheMonitoring nie jest dostępna');
        }
    } catch (error) {
        console.error('Błąd podczas blokowania cache monitoring:', error);
    }

    // Pokaż komunikat o trwającym imporcie
    showDrErykAlert('Trwa import danych... Proszę czekać.', 'info');

    const uploadBtn = document.getElementById('modalUploadBtn');
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<span class="material-icons-outlined text-sm animate-spin">refresh</span> Importowanie...';

    let apiData;
    try {
        if (typeof window.convertDrErykToApiFormat === 'function') {
            apiData = window.convertDrErykToApiFormat(window.drErykConvertedData);
        } else if (typeof convertDrErykToApiFormat === 'function') {
            apiData = convertDrErykToApiFormat(window.drErykConvertedData);
        } else {
            throw new Error('Funkcja convertDrErykToApiFormat nie jest dostępna');
        }
    } catch (error) {
        console.error('Błąd podczas konwersji danych:', error);
        showDrErykAlert('Błąd podczas konwersji danych: ' + error.message, 'danger');
        
        // Odblokuj monitoring w przypadku błędu
        try {
            if (typeof window.unblockCacheMonitoring === 'function') {
                window.unblockCacheMonitoring();
            } else if (typeof unblockCacheMonitoring === 'function') {
                unblockCacheMonitoring();
            }
        } catch (e) {
            console.error('Błąd podczas odblokowywania cache monitoring:', e);
        }
        
        // Przywróć przycisk po błędzie
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<span class="material-icons-outlined text-sm">upload</span> Importuj dane';
        return;
    }

    fetch('/api/v2/import', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                // Przekaz dodatkowe informacje o błędzie, jeśli są dostępne
                const error = new Error(data.message || `Błąd HTTP: ${response.status}`);
                error.data = data.data || null;
                error.status = response.status;
                throw error;
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            const importedCount = data.data?.imported || 0;
            const totalCount = data.data?.total || 0;
            const errors = data.data?.errors || [];

            let message = `Import zakończony pomyślnie!\n\nZaimportowano: ${importedCount}/${totalCount} wizyt`;

            if (errors.length > 0) {
                message += `\n\nOstrzeżenia (${errors.length}):\n${errors.slice(0, 3).join('\n')}`;
                if (errors.length > 3) {
                    message += `\n...i ${errors.length - 3} innych`;
                }
            }

            // Przywróć przycisk po sukcesie
            uploadBtn.disabled = false;
            uploadBtn.innerHTML = '<span class="material-icons-outlined text-sm">upload</span> Importuj dane';

            // Znajdź przyciski Anuluj i Importuj i ukryj je
            const cancelBtn = document.querySelector('button[onclick="closeDrErykImportModal()"]');
            const uploadBtnElement = document.getElementById('modalUploadBtn');

            if (cancelBtn) cancelBtn.style.display = 'none';
            if (uploadBtnElement) uploadBtnElement.style.display = 'none';

            // Pokaż komunikat sukcesu
            showDrErykAlert(message, 'success');

            // Dodaj przycisk ZAKOŃCZ
            const finishButtonContainer = document.createElement('div');
            finishButtonContainer.className = 'text-center mt-4';
            finishButtonContainer.innerHTML = `
                <button id="finishImportBtn" class="px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-lg font-medium">
                    <span class="material-icons-outlined text-lg mr-2">check_circle</span>
                    ZAKOŃCZ
                </button>
            `;

            const modalFooter = document.querySelector('#drErykImportModal .flex.justify-between.pt-4');
            if (modalFooter) {
                modalFooter.appendChild(finishButtonContainer);

                // Dodaj event listener do przycisku ZAKOŃCZ
                const finishBtn = document.getElementById('finishImportBtn');
                if (finishBtn) {
                    finishBtn.addEventListener('click', () => {
                        // Odblokuj monitoring przed przeładowaniem
                        try {
                            if (typeof window.unblockCacheMonitoring === 'function') {
                                window.unblockCacheMonitoring();
                            } else if (typeof unblockCacheMonitoring === 'function') {
                                unblockCacheMonitoring();
                            }
                        } catch (error) {
                            console.error('Błąd podczas odblokowywania cache monitoring:', error);
                        }

                        window.location.reload();
                    });
                }
            }

        } else {
            throw new Error(data.message || 'Nieznany błąd podczas importu');
        }
    })
    .catch(error => {
        console.error('Import error:', error);
        
        // Sprawdź czy błąd jest związany z niezmapowanymi lekarzami
        const isUnmappedDoctorsError = (
            (error.message && error.message.includes('niezmapowanych lekarzy')) ||
            (error.data && error.data.unmapped_count > 0)
        );
        
        if (isUnmappedDoctorsError) {
            // Przygotuj komunikat z liczbą niezmapowanych lekarzy
            let message = error.message;
            if (error.data && error.data.unmapped_count) {
                message = `Znaleziono ${error.data.unmapped_count} niezmapowanych lekarzy. Musisz zmapować ich przed kontynuowaniem importu.`;
            }
            
            // Wyświetl alert z przyciskiem do mapowania lekarzy
            showDrErykAlertWithButton(
                message,
                'warning', // Użyj warning zamiast danger dla lepszego kontrastu
                'Przejdź do mapowania lekarzy',
                () => {
                    window.location.href = '/admin/lekarze/map-import';
                }
            );
        } else {
            showDrErykAlert(`Błąd importu: ${error.message}`, 'danger');
        }

        // Odblokuj monitoring w przypadku błędu
        try {
            if (typeof window.unblockCacheMonitoring === 'function') {
                window.unblockCacheMonitoring();
            } else if (typeof unblockCacheMonitoring === 'function') {
                unblockCacheMonitoring();
            }
        } catch (error) {
            console.error('Błąd podczas odblokowywania cache monitoring:', error);
        }

        // Przywróć przycisk po błędzie
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<span class="material-icons-outlined text-sm">upload</span> Importuj dane';
    });
};

/**
 * Wyświetlanie alertów
 */
window.showDrErykAlert = function(message, type, duration = 5000) {
    const existingAlerts = document.querySelectorAll('#drErykImportModal .dreryk-alert');
    existingAlerts.forEach(alert => alert.remove());

    // Kolory dla różnych typów alertów (Tailwind CSS)
    const alertStyles = {
        'success': 'bg-green-50 border-green-200 text-green-800',
        'danger': 'bg-red-50 border-red-200 text-red-800',
        'warning': 'bg-amber-50 border-amber-200 text-amber-800',
        'info': 'bg-blue-50 border-blue-200 text-blue-800'
    };

    const iconNames = {
        'success': 'check_circle',
        'danger': 'error',
        'warning': 'warning',
        'info': 'info'
    };

    const alertDiv = document.createElement('div');
    alertDiv.className = `dreryk-alert border rounded-lg p-4 mb-4 ${alertStyles[type] || alertStyles['info']}`;
    alertDiv.style.zIndex = '60'; // Upewnij się, że alert jest na wierzchu
    alertDiv.style.position = 'relative'; // Upewnij się, że alert jest pozycjonowany
    alertDiv.innerHTML = `
        <div class="flex items-center">
            <span class="material-icons-outlined mr-3 text-lg">
                ${iconNames[type] || 'info'}
            </span>
            <span class="flex-1">${message.replace(/\n/g, ' ')}</span>
            <button type="button" class="ml-3 text-slate-400 hover:text-slate-600" onclick="this.parentElement.parentElement.remove()">
                <span class="material-icons-outlined text-lg">close</span>
            </button>
        </div>
    `;

    const modalContent = document.querySelector('#drErykImportModal .p-6');
    if (modalContent) {
        // Wstaw alert na samej górze modalu, zaraz pod tytułem
        const modalTitle = document.querySelector('#drErykImportModal .flex.justify-between.items-center.mb-6');
        if (modalTitle) {
            modalTitle.parentNode.insertBefore(alertDiv, modalTitle.nextSibling);
        } else {
            modalContent.insertBefore(alertDiv, modalContent.firstChild);
        }
        
        // Przewiń modal na górę, aby pokazać alert
        const modalContainer = document.querySelector('#drErykImportModal .overflow-y-auto');
        if (modalContainer) {
            modalContainer.scrollTop = 0;
        }
        
        // Upewnij się, że modal jest widoczny
        const modal = document.getElementById('drErykImportModal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    } else {
        // Jeśli modal nie jest dostępny, pokaż alert na stronie
        document.body.insertBefore(alertDiv, document.body.firstChild);
    }

    // Nie usuwaj alertu automatycznie, jeśli duration jest większe niż 10 sekund
    if (duration <= 10000) {
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, duration);
    }
};

/**
 * Wyświetlanie alertów z przyciskiem akcji
 */
window.showDrErykAlertWithButton = function(message, type, buttonText, buttonCallback) {
    const existingAlerts = document.querySelectorAll('#drErykImportModal .dreryk-alert');
    existingAlerts.forEach(alert => alert.remove());

    // Kolory dla różnych typów alertów (Tailwind CSS)
    const alertStyles = {
        'success': 'bg-green-50 border-green-200 text-green-800',
        'danger': 'bg-red-50 border-red-200 text-red-800',
        'warning': 'bg-amber-50 border-amber-200 text-amber-800',
        'info': 'bg-blue-50 border-blue-200 text-blue-800'
    };

    const iconNames = {
        'success': 'check_circle',
        'danger': 'error',
        'warning': 'warning',
        'info': 'info'
    };

    const alertDiv = document.createElement('div');
    alertDiv.className = `dreryk-alert border rounded-lg p-4 mb-4 ${alertStyles[type] || alertStyles['info']}`;
    alertDiv.style.zIndex = '60'; // Upewnij się, że alert jest na wierzchu
    alertDiv.style.position = 'relative'; // Upewnij się, że alert jest pozycjonowany
    alertDiv.innerHTML = `
        <div class="flex items-start">
            <span class="material-icons-outlined mr-3 text-lg mt-0.5">
                ${iconNames[type] || 'info'}
            </span>
            <div class="flex-1">
                <div class="mb-3">${message.replace(/\n/g, ' ')}</div>
                <button type="button" id="alertActionButton" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors text-sm">
                    ${buttonText}
                </button>
            </div>
            <button type="button" class="ml-3 text-slate-400 hover:text-slate-600" onclick="this.parentElement.parentElement.remove()">
                <span class="material-icons-outlined text-lg">close</span>
            </button>
        </div>
    `;

    const modalContent = document.querySelector('#drErykImportModal .p-6');
    if (modalContent) {
        // Wstaw alert na samej górze modalu, zaraz pod tytułem
        const modalTitle = document.querySelector('#drErykImportModal .flex.justify-between.items-center.mb-6');
        if (modalTitle) {
            modalTitle.parentNode.insertBefore(alertDiv, modalTitle.nextSibling);
        } else {
            modalContent.insertBefore(alertDiv, modalContent.firstChild);
        }
        
        // Przewiń modal na górę, aby pokazać alert
        const modalContainer = document.querySelector('#drErykImportModal .overflow-y-auto');
        if (modalContainer) {
            modalContainer.scrollTop = 0;
        }
        
        // Upewnij się, że modal jest widoczny
        const modal = document.getElementById('drErykImportModal');
        if (modal) {
            modal.classList.remove('hidden');
        }

        // Dodaj nasłuchiwanie na przycisk akcji
        const actionButton = alertDiv.querySelector('#alertActionButton');
        if (actionButton && typeof buttonCallback === 'function') {
            actionButton.addEventListener('click', buttonCallback);
        }
    } else {
        // Jeśli modal nie jest dostępny, pokaż alert na stronie
        document.body.insertBefore(alertDiv, document.body.firstChild);
        
        // Dodaj nasłuchiwanie na przycisk akcji
        const actionButton = alertDiv.querySelector('#alertActionButton');
        if (actionButton && typeof buttonCallback === 'function') {
            actionButton.addEventListener('click', buttonCallback);
        }
    }
};

/**
 * Zamykanie modalu
 */
window.closeDrErykImportModal = function() {
    console.log('Zamykanie modalu drEryk');
    const modal = document.getElementById('drErykImportModal');
    if (modal) {
        modal.classList.add('hidden');
        if (typeof resetDrErykImport === 'function') {
            resetDrErykImport();
        }
    }
};

window.showDrErykImportModal = function() {
    console.log('Otwieranie modalu drEryk');
    const modal = document.getElementById('drErykImportModal');
    if (modal) {
        modal.classList.remove('hidden');
    } else {
        console.error('Nie znaleziono modalu drErykImportModal');
    }
};

/**
 * Reset modalu
 */
window.resetDrErykImport = function() {
    document.getElementById('importStep1').classList.remove('hidden');
    document.getElementById('importStep2').classList.add('hidden');
    document.getElementById('drErykFileInput').value = '';

    const uploadBtn = document.getElementById('modalUploadBtn');
    if (uploadBtn) {
        uploadBtn.disabled = true;
        uploadBtn.style.display = ''; // Przywróć widoczność przycisku
    }

    // Przywróć widoczność przycisku Anuluj
    const cancelBtn = document.querySelector('button[onclick="closeDrErykImportModal()"]');
    if (cancelBtn) {
        cancelBtn.style.display = '';
    }

    // Usuń przycisk ZAKOŃCZ jeśli istnieje
    const finishBtn = document.getElementById('finishImportBtn');
    if (finishBtn) {
        finishBtn.parentElement.remove();
    }

    window.drErykFileContent = null;
    window.drErykConvertedData = null;

    const existingAlerts = document.querySelectorAll('#drErykImportModal .dreryk-alert');
    existingAlerts.forEach(alert => alert.remove());
};
