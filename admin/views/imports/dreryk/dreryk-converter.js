/**
 * DrEryk Data Converter
 * Konwertuje dane z formatu drEryk CSV do formatu API
 */

/**
 * Funkcja MD5 hash
 */
function md5(string) {
    function md5cycle(x, k) {
        var a = x[0], b = x[1], c = x[2], d = x[3];
        a = ff(a, b, c, d, k[0], 7, -680876936);
        d = ff(d, a, b, c, k[1], 12, -389564586);
        c = ff(c, d, a, b, k[2], 17, 606105819);
        b = ff(b, c, d, a, k[3], 22, -1044525330);
        a = ff(a, b, c, d, k[4], 7, -176418897);
        d = ff(d, a, b, c, k[5], 12, 1200080426);
        c = ff(c, d, a, b, k[6], 17, -1473231341);
        b = ff(b, c, d, a, k[7], 22, -45705983);
        a = ff(a, b, c, d, k[8], 7, 1770035416);
        d = ff(d, a, b, c, k[9], 12, -1958414417);
        c = ff(c, d, a, b, k[10], 17, -42063);
        b = ff(b, c, d, a, k[11], 22, -1990404162);
        a = ff(a, b, c, d, k[12], 7, 1804603682);
        d = ff(d, a, b, c, k[13], 12, -40341101);
        c = ff(c, d, a, b, k[14], 17, -1502002290);
        b = ff(b, c, d, a, k[15], 22, 1236535329);
        a = gg(a, b, c, d, k[1], 5, -165796510);
        d = gg(d, a, b, c, k[6], 9, -1069501632);
        c = gg(c, d, a, b, k[11], 14, 643717713);
        b = gg(b, c, d, a, k[0], 20, -373897302);
        a = gg(a, b, c, d, k[5], 5, -701558691);
        d = gg(d, a, b, c, k[10], 9, 38016083);
        c = gg(c, d, a, b, k[15], 14, -660478335);
        b = gg(b, c, d, a, k[4], 20, -405537848);
        a = gg(a, b, c, d, k[9], 5, 568446438);
        d = gg(d, a, b, c, k[14], 9, -1019803690);
        c = gg(c, d, a, b, k[3], 14, -187363961);
        b = gg(b, c, d, a, k[8], 20, 1163531501);
        a = gg(a, b, c, d, k[13], 5, -1444681467);
        d = gg(d, a, b, c, k[2], 9, -51403784);
        c = gg(c, d, a, b, k[7], 14, 1735328473);
        b = gg(b, c, d, a, k[12], 20, -1926607734);
        a = hh(a, b, c, d, k[5], 4, -378558);
        d = hh(d, a, b, c, k[8], 11, -2022574463);
        c = hh(c, d, a, b, k[11], 16, 1839030562);
        b = hh(b, c, d, a, k[14], 23, -35309556);
        a = hh(a, b, c, d, k[1], 4, -1530992060);
        d = hh(d, a, b, c, k[4], 11, 1272893353);
        c = hh(c, d, a, b, k[7], 16, -155497632);
        b = hh(b, c, d, a, k[10], 23, -1094730640);
        a = hh(a, b, c, d, k[13], 4, 681279174);
        d = hh(d, a, b, c, k[0], 11, -358537222);
        c = hh(c, d, a, b, k[3], 16, -722521979);
        b = hh(b, c, d, a, k[6], 23, 76029189);
        a = hh(a, b, c, d, k[9], 4, -640364487);
        d = hh(d, a, b, c, k[12], 11, -421815835);
        c = hh(c, d, a, b, k[15], 16, 530742520);
        b = hh(b, c, d, a, k[2], 23, -995338651);
        a = ii(a, b, c, d, k[0], 6, -198630844);
        d = ii(d, a, b, c, k[7], 10, 1126891415);
        c = ii(c, d, a, b, k[14], 15, -1416354905);
        b = ii(b, c, d, a, k[5], 21, -57434055);
        a = ii(a, b, c, d, k[12], 6, 1700485571);
        d = ii(d, a, b, c, k[3], 10, -1894986606);
        c = ii(c, d, a, b, k[10], 15, -1051523);
        b = ii(b, c, d, a, k[1], 21, -2054922799);
        a = ii(a, b, c, d, k[8], 6, 1873313359);
        d = ii(d, a, b, c, k[15], 10, -30611744);
        c = ii(c, d, a, b, k[6], 15, -1560198380);
        b = ii(b, c, d, a, k[13], 21, 1309151649);
        a = ii(a, b, c, d, k[4], 6, -145523070);
        d = ii(d, a, b, c, k[11], 10, -1120210379);
        c = ii(c, d, a, b, k[2], 15, 718787259);
        b = ii(b, c, d, a, k[9], 21, -343485551);
        x[0] = add32(a, x[0]);
        x[1] = add32(b, x[1]);
        x[2] = add32(c, x[2]);
        x[3] = add32(d, x[3]);
    }
    function cmn(q, a, b, x, s, t) {
        a = add32(add32(a, q), add32(x, t));
        return add32((a << s) | (a >>> (32 - s)), b);
    }
    function ff(a, b, c, d, x, s, t) {
        return cmn((b & c) | ((~b) & d), a, b, x, s, t);
    }
    function gg(a, b, c, d, x, s, t) {
        return cmn((b & d) | (c & (~d)), a, b, x, s, t);
    }
    function hh(a, b, c, d, x, s, t) {
        return cmn(b ^ c ^ d, a, b, x, s, t);
    }
    function ii(a, b, c, d, x, s, t) {
        return cmn(c ^ (b | (~d)), a, b, x, s, t);
    }
    function md51(s) {
        var n = s.length,
            state = [1732584193, -271733879, -1732584194, 271733878], i;
        for (i = 64; i <= s.length; i += 64) {
            md5cycle(state, md5blk(s.substring(i - 64, i)));
        }
        s = s.substring(i - 64);
        var tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        for (i = 0; i < s.length; i++)
            tail[i >> 2] |= s.charCodeAt(i) << ((i % 4) << 3);
        tail[i >> 2] |= 0x80 << ((i % 4) << 3);
        if (i > 55) {
            md5cycle(state, tail);
            for (i = 0; i < 16; i++) tail[i] = 0;
        }
        tail[14] = n * 8;
        md5cycle(state, tail);
        return state;
    }
    function md5blk(s) {
        var md5blks = [], i;
        for (i = 0; i < 64; i += 4) {
            md5blks[i >> 2] = s.charCodeAt(i)
                + (s.charCodeAt(i + 1) << 8)
                + (s.charCodeAt(i + 2) << 16)
                + (s.charCodeAt(i + 3) << 24);
        }
        return md5blks;
    }
    var hex_chr = '0123456789abcdef'.split('');
    function rhex(n) {
        var s = '', j = 0;
        for (; j < 4; j++)
            s += hex_chr[(n >> (j * 8 + 4)) & 0x0F]
                + hex_chr[(n >> (j * 8)) & 0x0F];
        return s;
    }
    function hex(x) {
        for (var i = 0; i < x.length; i++)
            x[i] = rhex(x[i]);
        return x.join('');
    }
    function add32(a, b) {
        return (a + b) & 0xFFFFFFFF;
    }
    return hex(md51(string));
}

/**
 * Konwersja danych drEryk do formatu API
 */
window.convertDrErykToApiFormat = function(drErykData) {
    const appointments = [];

    drErykData.doctors.forEach(doctor => {
        doctor.appointments.forEach(appointment => {
            const doctorId = md5(doctor.doctorName.trim().toUpperCase());
            
            appointments.push({
                doctor_id: doctorId,
                doctor_name: doctor.doctorName,
                patient_name: appointment.patient_name,
                appointment_time: appointment.appointment_time,
                appointment_date: appointment.appointment_date,
                appointment_duration: appointment.appointment_duration,
                phone_number: appointment.phone_number,
                external_id: appointment.external_id
            });
        });
    });
    
    // Grupuj wizyty po datach
    const dayGroups = {};

    appointments.forEach(appointment => {
        const date = appointment.appointment_date;
        if (!dayGroups[date]) {
            dayGroups[date] = {};
        }

        const doctorId = appointment.doctor_id;
        if (!dayGroups[date][doctorId]) {
            dayGroups[date][doctorId] = {
                doctorId: doctorId,
                doctorName: appointment.doctor_name || 'Nieznany lekarz',
                appointments: []
            };
        }

        const patientParts = appointment.patient_name.trim().split(' ');
        const patientFirstName = patientParts[0] || '';
        const patientLastName = patientParts.slice(1).join(' ') || '';

        dayGroups[date][doctorId].appointments.push({
            appointmentId: appointment.external_id,
            patientFirstName: patientFirstName,
            patientLastName: patientLastName,
            appointmentStart: appointment.appointment_time,
            appointmentDuration: appointment.appointment_duration,
            phone_number: appointment.phone_number
        });
    });

    const days = [];
    Object.keys(dayGroups).forEach(date => {
        const doctors = Object.values(dayGroups[date]);
        days.push({
            date: date,
            doctors: doctors
        });
    });

    return {
        source: drErykData.source || "drEryk",
        syncCode: drErykData.syncCode || "drEryk_" + new Date().toISOString().slice(0, 10),
        syncData: {
            days: days
        }
    };
};

/**
 * Klasa konwertera drEryk
 */
window.DrErykConverter = class DrErykConverter {
    convertDataFromString(content, settings = {}) {
        console.log('Rozpoczęcie konwersji danych z ustawieniami:', settings);
        
        this.outsideScheduleCount = 0;
        
        const parsedData = this.parseCSV(content);
        const filteredData = this.filterAndNormalizeData(parsedData, settings);
        const convertedData = this.convertToAPIFormat(filteredData);
        
        convertedData.outsideScheduleCount = this.outsideScheduleCount || 0;
        console.log('Liczba wizyt poza kolejką przekazana do convertedData:', convertedData.outsideScheduleCount);
        console.log('Zakończono konwersję danych');
        return convertedData;
    }
    
    parseCSV(content) {
        const lines = content.split('\n');
        const result = [];
        let currentDoctor = null;
        let currentDate = null;
        let skipped = [];
        let currentColumnMapping = null;

        for (const line of lines) {
            const dateMatch = line.match(/na dzień (\d{4}-\d{2}-\d{2})/);
            if (dateMatch) {
                currentDate = dateMatch[1];
                break;
            }
        }

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            const doctorMatch = line.match(/Lekarz: (.+)\|\|\|\|\|\|\|\|\|\|/);
            if (doctorMatch) {
                currentDoctor = this.cleanDoctorName(doctorMatch[1].trim());
                continue;
            }

            // Wykryj nagłówek kolumn i utwórz mapowanie
            if (line.includes('Lp.') && line.includes('Pacjent') && line.includes('|')) {
                currentColumnMapping = this.detectColumnMapping(line);
                console.log('Wykryto nowe mapowanie kolumn:', currentColumnMapping);
                continue;
            }

            if (line.includes('Lista zarejestrowanych') ||
                line.includes('Sporządził') || line.includes('Data') ||
                line === '' || line.match(/^\d+\s*$/) || line.match(/^\|\|\|\|\|$/)) {
                continue;
            }

            if (currentDoctor && currentColumnMapping && line.includes('|')) {
                const parts = line.split('|').map(p => p.trim());

                if (parts.length >= Math.max(...Object.values(currentColumnMapping)) + 1) {
                    const lp = parts[currentColumnMapping.lp] || '';
                    if (!lp.match(/^\d+([A-Z]*)?$/)) continue;

                    const appointment = {
                        lp: lp.trim(),
                        godzinaPrzyjecia: (parts[currentColumnMapping.godzinaPrzyjecia] || '').trim(),
                        godzinaZaplanowana: (parts[currentColumnMapping.godzinaZaplanowana] || '').trim(),
                        godzinaPrzyjecia2: (parts[currentColumnMapping.godzinaPrzyjecia2] || '').trim(),
                        pacjent: (parts[currentColumnMapping.pacjent] || '').trim(),
                        adres: (parts[currentColumnMapping.adres] || '').trim(),
                        komentarz: (parts[currentColumnMapping.komentarz] || '').trim(),
                        kategoriaWizyty: (parts[currentColumnMapping.kategoriaWizyty] || '').trim(),
                        telefon: (parts[currentColumnMapping.telefon] || '').trim(),
                        nrId: (parts[currentColumnMapping.nrId] || '').trim(),
                        lekarz: currentDoctor.trim(),
                        data: currentDate
                    };

                    result.push(appointment);
                }
            }
        }

        return { data: result, skipped: skipped };
    }

    /**
     * Wykrywa mapowanie kolumn na podstawie nagłówka
     */
    detectColumnMapping(headerLine) {
        const parts = headerLine.split('|').map(p => p.trim().toLowerCase());
        const mapping = {};

        console.log('Analizowanie nagłówka:', headerLine);
        console.log('Części nagłówka:', parts);

        // Znajdź indeksy kolumn na podstawie ich nazw (obsługa polskich znaków i kodowania)
        for (let i = 0; i < parts.length; i++) {
            const part = parts[i];

            if (part.includes('lp')) {
                mapping.lp = i;
            } else if (part.includes('godz.przyj') || part.includes('godz.przyj�cia') || part.includes('godz.przyj�cia')) {
                if (!mapping.godzinaPrzyjecia) {
                    mapping.godzinaPrzyjecia = i;
                } else if (!mapping.godzinaPrzyjecia2) {
                    mapping.godzinaPrzyjecia2 = i;
                }
            } else if (part.includes('godz.zaplanowana') || part.includes('godz.zaplan')) {
                mapping.godzinaZaplanowana = i;
            } else if (part.includes('pacjent')) {
                mapping.pacjent = i;
            } else if (part.includes('adres')) {
                mapping.adres = i;
            } else if (part.includes('komentarz')) {
                mapping.komentarz = i;
            } else if (part.includes('kategoria')) {
                mapping.kategoriaWizyty = i;
            } else if (part.includes('telefon')) {
                mapping.telefon = i;
            } else if (part.includes('nr id') || part.includes('nrid') || part.includes('nr�id')) {
                mapping.nrId = i;
            }
        }

        // Jeśli nie znaleziono mapowania na podstawie nazw, użyj pozycyjnego wykrywania
        if (Object.keys(mapping).length < 5) { // Jeśli znaleziono mniej niż 5 kolumn
            console.log('Nie znaleziono wystarczającego mapowania na podstawie nazw, używam pozycyjnego wykrywania');

            // Znajdź kolumny z niepustymi nazwami (pomijając puste kolumny)
            const nonEmptyColumns = [];
            for (let i = 0; i < parts.length; i++) {
                if (parts[i] && parts[i].trim() !== '') {
                    nonEmptyColumns.push({
                        index: i,
                        name: parts[i].trim()
                    });
                }
            }

            console.log('Kolumny z niepustymi nazwami:', nonEmptyColumns);

            // Mapuj na podstawie pozycji w niepustych kolumnach
            if (nonEmptyColumns.length >= 10) {
                mapping.lp = nonEmptyColumns[0].index;
                mapping.godzinaPrzyjecia = nonEmptyColumns[1].index;
                mapping.godzinaZaplanowana = nonEmptyColumns[2].index;
                mapping.godzinaPrzyjecia2 = nonEmptyColumns[3].index;
                mapping.pacjent = nonEmptyColumns[4].index;
                mapping.adres = nonEmptyColumns[5].index;
                mapping.komentarz = nonEmptyColumns[6].index;
                mapping.kategoriaWizyty = nonEmptyColumns[7].index;
                mapping.telefon = nonEmptyColumns[8].index;
                mapping.nrId = nonEmptyColumns[9].index;
            } else {
                // Fallback - użyj standardowego mapowania ale przesunięte o puste kolumny
                console.log('Używam fallback mapowania');

                // Znajdź pierwszą niepustą kolumnę
                let firstNonEmptyIndex = 0;
                for (let i = 0; i < parts.length; i++) {
                    if (parts[i] && parts[i].trim() !== '' && parts[i].toLowerCase().includes('lp')) {
                        firstNonEmptyIndex = i;
                        break;
                    }
                }

                mapping.lp = firstNonEmptyIndex;
                mapping.godzinaPrzyjecia = firstNonEmptyIndex + 1;
                mapping.godzinaZaplanowana = firstNonEmptyIndex + 2;
                mapping.godzinaPrzyjecia2 = firstNonEmptyIndex + 3;
                mapping.pacjent = firstNonEmptyIndex + 4;
                mapping.adres = firstNonEmptyIndex + 5;
                mapping.komentarz = firstNonEmptyIndex + 6;
                mapping.kategoriaWizyty = firstNonEmptyIndex + 7;
                mapping.telefon = firstNonEmptyIndex + 8;
                mapping.nrId = firstNonEmptyIndex + 9;
            }
        }

        // Ustaw domyślne wartości jeśli nadal nie znaleziono mapowania
        const defaults = {
            lp: 0,
            godzinaPrzyjecia: 1,
            godzinaZaplanowana: 2,
            godzinaPrzyjecia2: 3,
            pacjent: 4,
            adres: 5,
            komentarz: 6,
            kategoriaWizyty: 7,
            telefon: 8,
            nrId: 9
        };

        // Uzupełnij brakujące mapowania domyślnymi wartościami
        Object.keys(defaults).forEach(key => {
            if (mapping[key] === undefined) {
                mapping[key] = defaults[key];
            }
        });

        console.log('Finalne mapowanie kolumn:', mapping);
        return mapping;
    }
    
    cleanDoctorName(doctorName) {
        return doctorName.replace(/[|,]/g, '').replace(/\s+/g, ' ').trim();
    }

    filterAndNormalizeData(parsedData, settings = {}) {
        const result = [];
        const skipped = [...parsedData.skipped];
        const outsideScheduleCounters = {};

        let outsideScheduleCount = 0;
        for (const item of parsedData.data) {
            if (item.godzinaZaplanowana === 'poza kol.' ||
                item.godzinaZaplanowana === 'Powtórka Rp.' ||
                item.godzinaZaplanowana === '') {
                outsideScheduleCount++;
            }
        }

        this.outsideScheduleCount = outsideScheduleCount;
        console.log('Wykryto wizyt poza kolejką:', outsideScheduleCount);

        // Jeśli włączone jest losowe mieszanie, najpierw zbierz wszystkie wizyty poza kolejką
        if (settings.randomizeOutsideSchedule) {
            return this.processWithRandomization(parsedData, settings);
        }

        for (const item of parsedData.data) {
            let skipReason = null;

            if (!item.pacjent || item.pacjent.trim() === '') {
                skipReason = 'Brak nazwy pacjenta';
            }

            if (skipReason) {
                skipped.push({
                    ...item,
                    skipReason: skipReason
                });
                continue;
            }

            let appointmentTime = item.godzinaZaplanowana;
            let duration = 20;
            let skipAppointment = false;

            if (appointmentTime === 'poza kol.' || appointmentTime === 'Powtórka Rp.' || appointmentTime === '') {
                if (appointmentTime === '') {
                    appointmentTime = 'poza kol.';
                }

                const originalAppointmentTime = appointmentTime;

                if (settings.removeOutsideSchedule) {
                    skipAppointment = true;
                } else {
                    if (appointmentTime === 'poza kol.') {
                        // Użyj wartości 0 jeśli została ustawiona, inaczej domyślnie 3
                        const outsideDuration = (settings.outsideScheduleDuration !== undefined && settings.outsideScheduleDuration !== null)
                            ? settings.outsideScheduleDuration
                            : 3;

                        appointmentTime = this.getNextAppointmentTime(
                            settings.outsideScheduleStartTime || '08:00',
                            outsideDuration,
                            outsideScheduleCounters[item.lekarz] || 0
                        );
                        duration = outsideDuration;

                        if (!outsideScheduleCounters[item.lekarz]) {
                            outsideScheduleCounters[item.lekarz] = 0;
                        }
                        outsideScheduleCounters[item.lekarz]++;
                    } else if (appointmentTime === 'Powtórka Rp.') {
                        // Użyj wartości 0 jeśli została ustawiona, inaczej domyślnie 3
                        const repeatDuration = (settings.repeatRpDuration !== undefined && settings.repeatRpDuration !== null)
                            ? settings.repeatRpDuration
                            : 3;

                        appointmentTime = this.getNextAppointmentTime(
                            settings.repeatRpStartTime || '19:00',
                            repeatDuration,
                            outsideScheduleCounters[`repeatRp_${item.lekarz}`] || 0
                        );
                        duration = repeatDuration;

                        if (!outsideScheduleCounters[`repeatRp_${item.lekarz}`]) {
                            outsideScheduleCounters[`repeatRp_${item.lekarz}`] = 0;
                        }
                        outsideScheduleCounters[`repeatRp_${item.lekarz}`]++;
                    }
                }

                item.wasOutsideSchedule = true;
                item.originalAppointmentTime = originalAppointmentTime;
            }

            if (skipAppointment) {
                skipped.push({
                    ...item,
                    skipReason: 'Usunięto na żądanie (wizyta poza kolejką)'
                });
                continue;
            }

            const phone = item.telefon.replace(/[^0-9]/g, '');

            const normalizedItem = {
                ...item,
                appointmentTime: appointmentTime,
                duration: duration,
                phone: phone
            };

            result.push(normalizedItem);
        }

        return { data: result, skipped: skipped };
    }

    convertToAPIFormat(filteredData) {
        const doctorsMap = new Map();

        for (const item of filteredData.data) {
            const doctorName = item.lekarz;

            if (!doctorsMap.has(doctorName)) {
                doctorsMap.set(doctorName, {
                    doctorName: doctorName,
                    appointments: []
                });
            }

            let externalId = item.nrId;
            if (!externalId || externalId.trim() === '') {
                const currentDate = new Date().toISOString().split('T')[0]; // Format YYYY-MM-DD
                const hashData = `${item.pacjent}_${item.data || currentDate}_${item.lekarz}`;
                externalId = md5(hashData);
            }

            const appointment = {
                patient_name: item.pacjent,
                appointment_time: item.appointmentTime,
                appointment_date: item.data || new Date().toISOString().split('T')[0],
                appointment_duration: item.duration,
                phone_number: item.phone,
                external_id: externalId,
                was_outside_schedule: item.wasOutsideSchedule || false,
                original_appointment_time: item.originalAppointmentTime || item.appointmentTime
            };

            doctorsMap.get(doctorName).appointments.push(appointment);
        }

        const doctors = Array.from(doctorsMap.values()).map(doctor => {
            doctor.appointments.sort((a, b) => {
                const isTimeA = /^\d{2}:\d{2}$/.test(a.appointment_time);
                const isTimeB = /^\d{2}:\d{2}$/.test(b.appointment_time);

                if (isTimeA && isTimeB) {
                    const timeA = a.appointment_time.split(':');
                    const timeB = b.appointment_time.split(':');
                    const minutesA = parseInt(timeA[0]) * 60 + parseInt(timeA[1]);
                    const minutesB = parseInt(timeB[0]) * 60 + parseInt(timeB[1]);
                    return minutesA - minutesB;
                }

                if (isTimeA && !isTimeB) return -1;
                if (!isTimeA && isTimeB) return 1;

                return 0;
            });
            return doctor;
        });

        return {
            source: "drEryk",
            syncCode: "drEryk_" + new Date().toISOString().slice(0, 10),
            doctors: doctors,
            skippedData: filteredData.skipped,
            outsideScheduleCount: this.outsideScheduleCount || 0
        };
    }

    getNextAppointmentTime(startTime, duration, counter) {
        // Jeśli czas trwania wynosi 0, wszystkie wizyty mają tę samą godzinę startową
        if (duration === 0) {
            return startTime;
        }
        
        const [startHour, startMinute] = startTime.split(':').map(Number);
        const totalMinutes = startHour * 60 + startMinute + (counter * duration);
        const hour = Math.floor(totalMinutes / 60);
        const minute = totalMinutes % 60;
        return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    }

    /**
     * Przetwarza dane z losowym mieszaniem wizyt poza kolejką
     */
    processWithRandomization(parsedData, settings = {}) {
        const result = [];
        const skipped = [...parsedData.skipped];

        // Rozdziel wizyty na normalne i poza kolejką
        const normalAppointments = [];
        const outsideScheduleAppointments = [];
        const repeatRpAppointments = [];

        for (const item of parsedData.data) {
            let skipReason = null;

            if (!item.pacjent || item.pacjent.trim() === '') {
                skipReason = 'Brak nazwy pacjenta';
            }

            if (skipReason) {
                skipped.push({
                    ...item,
                    skipReason: skipReason
                });
                continue;
            }

            const appointmentTime = item.godzinaZaplanowana;

            if (appointmentTime === 'poza kol.' || appointmentTime === '') {
                if (settings.removeOutsideSchedule) {
                    skipped.push({
                        ...item,
                        skipReason: 'Usunięto na żądanie (wizyta poza kolejką)'
                    });
                    continue;
                }
                outsideScheduleAppointments.push(item);
            } else if (appointmentTime === 'Powtórka Rp.') {
                if (settings.removeOutsideSchedule) {
                    skipped.push({
                        ...item,
                        skipReason: 'Usunięto na żądanie (wizyta poza kolejką)'
                    });
                    continue;
                }
                repeatRpAppointments.push(item);
            } else {
                // Normalna wizyta z ustaloną godziną
                const phone = item.telefon.replace(/[^0-9]/g, '');
                const normalizedItem = {
                    ...item,
                    appointmentTime: appointmentTime,
                    duration: 20, // Domyślny czas dla normalnych wizyt
                    phone: phone
                };
                normalAppointments.push(normalizedItem);
            }
        }

        // Pomieszaj wizyty poza kolejką losowo
        this.shuffleArray(outsideScheduleAppointments);
        this.shuffleArray(repeatRpAppointments);

        // Przetwórz wizyty poza kolejką z losową kolejnością
        this.processOutsideScheduleAppointments(outsideScheduleAppointments, 'poza kol.', settings, result);
        this.processOutsideScheduleAppointments(repeatRpAppointments, 'Powtórka Rp.', settings, result);

        // Dodaj normalne wizyty
        result.push(...normalAppointments);

        return { data: result, skipped: skipped };
    }

    /**
     * Miesza tablicę w miejscu (algorytm Fisher-Yates)
     */
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    /**
     * Przetwarza wizyty poza kolejką (już pomieszane lub w oryginalnej kolejności)
     */
    processOutsideScheduleAppointments(appointments, originalType, settings, result) {
        const outsideScheduleCounters = {};

        appointments.forEach(item => {
            let appointmentTime, duration;
            const phone = item.telefon.replace(/[^0-9]/g, '');

            if (originalType === 'poza kol.') {
                const outsideDuration = (settings.outsideScheduleDuration !== undefined && settings.outsideScheduleDuration !== null)
                    ? settings.outsideScheduleDuration
                    : 3;

                appointmentTime = this.getNextAppointmentTime(
                    settings.outsideScheduleStartTime || '08:00',
                    outsideDuration,
                    outsideScheduleCounters[item.lekarz] || 0
                );
                duration = outsideDuration;

                if (!outsideScheduleCounters[item.lekarz]) {
                    outsideScheduleCounters[item.lekarz] = 0;
                }
                outsideScheduleCounters[item.lekarz]++;
            } else if (originalType === 'Powtórka Rp.') {
                const repeatDuration = (settings.repeatRpDuration !== undefined && settings.repeatRpDuration !== null)
                    ? settings.repeatRpDuration
                    : 3;

                appointmentTime = this.getNextAppointmentTime(
                    settings.repeatRpStartTime || '19:00',
                    repeatDuration,
                    outsideScheduleCounters[`repeatRp_${item.lekarz}`] || 0
                );
                duration = repeatDuration;

                if (!outsideScheduleCounters[`repeatRp_${item.lekarz}`]) {
                    outsideScheduleCounters[`repeatRp_${item.lekarz}`] = 0;
                }
                outsideScheduleCounters[`repeatRp_${item.lekarz}`]++;
            }

            const normalizedItem = {
                ...item,
                appointmentTime: appointmentTime,
                duration: duration,
                phone: phone,
                wasOutsideSchedule: true,
                originalAppointmentTime: originalType
            };

            result.push(normalizedItem);
        });
    }
};
