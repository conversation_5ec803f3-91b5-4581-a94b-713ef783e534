<!-- Modal importu z drEryk -->
<div id="drErykImportModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold text-slate-900">Import danych z drEryk</h2>
                    <button onclick="closeDrErykImportModal()" class="text-slate-400 hover:text-slate-600">
                        <span class="material-icons-outlined">close</span>
                    </button>
                </div>

                <!-- Krok 1: Wybór pliku -->
                <div id="importStep1" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">
                            Krok 1: W<PERSON><PERSON>rz plik CSV z drEryk
                        </label>
                        <div class="file-drop-area border-2 border-dashed border-slate-300 rounded-lg p-8 text-center hover:border-slate-400 transition-colors cursor-pointer" id="drErykFileDropArea">
                            <input type="file" id="drErykFileInput" accept=".csv" style="display: none;">
                            <div class="file-message">
                                <span class="material-icons-outlined text-4xl text-slate-400 mb-4">cloud_upload</span>
                                <p class="text-slate-700 font-medium">Przeciągnij i upuść plik CSV tutaj lub kliknij, aby wybrać plik</p>
                                <p class="text-slate-500 text-sm mt-2">Obsługiwane formaty: CSV</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Krok 2: Podgląd i import -->
                <div id="importStep2" class="hidden space-y-4">
                    <div>
                        <h3 class="text-lg font-medium text-slate-900">Podgląd danych do importu</h3>
                    </div>

                    <!-- Statystyki -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="bg-blue-50 rounded-lg p-4 flex items-center justify-center">
                            <div class="text-2xl font-bold text-blue-600 mr-2" id="modalTotalAppointments">0</div>
                            <div class="text-sm text-blue-600">Wizyt</div>
                        </div>
                        <div class="bg-green-50 rounded-lg p-4 flex items-center justify-center">
                            <div class="text-2xl font-bold text-green-600 mr-2" id="modalTotalDoctors">0</div>
                            <div class="text-sm text-green-600">Lekarzy</div>
                        </div>
                        <div class="bg-orange-50 rounded-lg p-4 flex items-center justify-center">
                            <div class="text-2xl font-bold text-orange-600 mr-2" id="modalOutsideSchedule">0</div>
                            <div class="text-sm text-orange-600">Poza kolejką</div>
                        </div>
                        <div class="bg-red-50 rounded-lg p-4 flex items-center justify-center">
                            <div class="text-2xl font-bold text-red-600 mr-2" id="modalSkippedAppointments">0</div>
                            <div class="text-sm text-red-600">Pominięto</div>
                        </div>
                    </div>

                    <!-- Ustawienia importu -->
                    <div class="bg-slate-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-medium text-slate-900">Ustawienia importu</h3>
                            <div class="text-xs text-slate-500 flex items-center">
                                <span class="material-icons-outlined text-sm mr-1">save</span>
                                Automatyczny zapis
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">
                                    Godzina rozpoczęcia wizyt "poza kolejką"
                                </label>
                                <input type="time" id="outsideScheduleStartTime" value="08:00"
                                       class="w-full px-3 py-2 border border-slate-300 rounded-md text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">
                                    Czas trwania wizyt "poza kolejką" (minuty)
                                </label>
                                <input type="number" id="outsideScheduleDuration" value="0" min="0" max="60"
                                       class="w-full px-3 py-2 border border-slate-300 rounded-md text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">
                                    Godzina rozpoczęcia "Powtórka Rp."
                                </label>
                                <input type="time" id="repeatRpStartTime" value="19:00"
                                       class="w-full px-3 py-2 border border-slate-300 rounded-md text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">
                                    Czas trwania "Powtórka Rp." (minuty)
                                </label>
                                <input type="number" id="repeatRpDuration" value="0" min="0" max="60"
                                       class="w-full px-3 py-2 border border-slate-300 rounded-md text-sm">
                            </div>
                        </div>
                        <div class="mt-4 space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" id="removeOutsideSchedule" class="mr-2">
                                <span class="text-sm text-slate-700">Usuń wizyty poza kolejką (nie importuj ich)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="randomizeOutsideSchedule" class="mr-2">
                                <span class="text-sm text-slate-700">Pomieszaj wizyty poza kolejką losowo</span>
                            </label>
                        </div>

                        <div class="mt-4">
                            <button id="applySettingsBtn" class="px-4 py-2 bg-slate-600 text-white rounded-md hover:bg-slate-700 text-sm">
                                <span class="material-icons-outlined text-sm mr-1">settings</span>
                                Zastosuj zmiany
                            </button>
                        </div>
                    </div>

                    <!-- Informacje o pominiętych wizytach -->
                    <div id="modalSkippedInfo" class="hidden bg-amber-50 border border-amber-200 rounded-lg p-4">
                        <h4 class="font-medium text-amber-800 mb-2">
                            Pominięto <span id="modalSkippedCount">0</span> wizyt
                        </h4>
                        <div class="max-h-32 overflow-y-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-amber-200">
                                        <th class="text-left py-1 px-2">Lp.</th>
                                        <th class="text-left py-1 px-2">Pacjent</th>
                                        <th class="text-left py-1 px-2">Lekarz</th>
                                        <th class="text-left py-1 px-2">Powód</th>
                                    </tr>
                                </thead>
                                <tbody id="modalSkippedTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Przyciski -->
                    <div class="flex justify-between pt-4 border-t pb-4 border-b">
                        <button onclick="closeDrErykImportModal()" class="px-4 py-2 border border-slate-300 text-slate-700 rounded-md hover:bg-slate-50">
                            Anuluj
                        </button>
                        <button id="modalUploadBtn" disabled class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-slate-300 disabled:cursor-not-allowed">
                            <span class="material-icons-outlined text-sm mr-1">upload</span>
                            Importuj dane
                        </button>
                    </div>

                    <!-- Podgląd danych -->
                    <div class="space-y-4 max-h-96 overflow-y-auto" id="modalDoctorsPreview">
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>
