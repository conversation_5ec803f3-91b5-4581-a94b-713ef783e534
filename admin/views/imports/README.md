# System Importu Danych Medycznych

## Przegląd

System importu został przeprojektowany w modularną architekturę, która umożliwia łatwe dodawanie nowych źródeł importu danych medycznych. Obecnie obsługuje import z systemu drEryk, z możliwością rozszerzenia o inne systemy.

## Architektura

### Frontend

#### Import Manager (`import-manager.js`)
Główny manager odpowiedzialny za:
- Zarządzanie dostępnymi systemami importu
- Dynamiczne ładowanie skryptów i modali
- Tworzenie przycisków importu
- Inicjalizację poszczególnych importerów

#### Struktura katalogów
```
admin/views/imports/
├── import-manager.js          # Główny manager importu
├── dreryk/                    # Import z drEryk
│   ├── dreryk-import-modal.html
│   ├── dreryk-import.js       # Logika UI importu drEryk
│   └── dreryk-converter.js    # Konwerter danych drEryk
└── README.md                  # Ta dokumentacja
```

### Backend

#### Kontrolery importu
- `ImportController.php` - Główny kontroler importu (obsługuje drEryk i inne systemy)
- `IgabinetImportController.php` - Specjalny kontroler dla iGabinet

#### Endpointy API
- `POST /api/v2/import` - Główny endpoint importu (drEryk i inne systemy)
- `POST /api/v2/import/igabinet` - Specjalny endpoint dla iGabinet

## Dodawanie nowego systemu importu

### 1. Frontend

#### Krok 1: Stwórz katalog dla nowego systemu
```bash
mkdir admin/views/imports/[nazwa_systemu]
```

#### Krok 2: Stwórz pliki dla nowego systemu
- `[nazwa_systemu]-import-modal.html` - Modal importu
- `[nazwa_systemu]-import.js` - Logika UI
- `[nazwa_systemu]-converter.js` - Konwerter danych

#### Krok 3: Dodaj system do Import Manager
```javascript
// W import-manager.js
this.availableImports['nazwa_systemu'] = {
    name: 'Nazwa Systemu',
    description: 'Import danych z systemu XYZ',
    modalId: 'nazwaSystemuImportModal',
    initFunction: 'initializeNazwaSystemuImport',
    fileTypes: ['.xml', '.csv'],
    scripts: [
        '/admin/views/imports/nazwa_systemu/nazwa_systemu-converter.js',
        '/admin/views/imports/nazwa_systemu/nazwa_systemu-import.js'
    ],
    modal: '/admin/views/imports/nazwa_systemu/nazwa_systemu-import-modal.html'
};
```

### 2. Backend

#### Krok 1: Decyzja o endpoincie
**Opcja A: Użyj standardowego endpointu** (zalecane dla większości systemów)
- Dane będą przetwarzane przez `ImportController.php`
- Endpoint: `POST /api/v2/import`
- Format danych: `syncData.days` (jak drEryk)

**Opcja B: Stwórz dedykowany endpoint** (tylko dla systemów wymagających specjalnej logiki)
```php
// api/v2/controllers/NazwaSystemuImportController.php
class NazwaSystemuImportController extends ApiController {
    public function importData() {
        // Specjalna logika importu
    }
}
```

#### Krok 2: Dodaj routing (tylko dla opcji B)
```php
// W api/v2/index.php
$router->post('/import/nazwa_systemu', [NazwaSystemuImportController::class, 'importData']);
```

## Przykład: System drEryk

### Frontend

#### Modal (`dreryk-import-modal.html`)
Zawiera HTML strukturę modalu z:
- Obszarem wyboru pliku (drag & drop)
- Ustawieniami importu
- Podglądem danych
- Przyciskami akcji

#### Logika UI (`dreryk-import.js`)
Funkcje globalne:
- `window.initializeDrErykImport()` - Inicjalizacja
- `window.handleDrErykFileSelect()` - Obsługa wyboru pliku
- `window.uploadDrErykData()` - Upload danych
- `window.showDrErykAlert()` - Wyświetlanie alertów

#### Konwerter (`dreryk-converter.js`)
- `window.DrErykConverter` - Klasa konwertera
- `window.convertDrErykToApiFormat()` - Konwersja do API
- Funkcje pomocnicze (MD5, parsowanie CSV)

### Backend

#### Kontroler (`ImportController.php`)
- `importData()` - Główna metoda importu
- `importStructuredData()` - Przetwarzanie danych w formacie syncData.days
- Obsługuje automatyczne mapowanie lekarzy
- Obsługuje transakcje i rollback

#### Endpoint
`POST /api/v2/import`

Format danych:
```json
{
    "source": "drEryk",
    "syncCode": "drEryk_2025-10-14",
    "syncData": {
        "days": [
            {
                "date": "2025-10-14",
                "doctors": [
                    {
                        "doctorId": "md5_hash",
                        "doctorName": "Dr. Jan Kowalski",
                        "appointments": [...]
                    }
                ]
            }
        ]
    }
}
```

## Proces importu drEryk

1. **Wybór pliku** - Użytkownik wybiera plik CSV
2. **Automatyczna analiza** - System automatycznie analizuje plik
3. **Konwersja danych** - CSV → struktura API
4. **Podgląd** - Wyświetlenie podsumowania i podglądu
5. **Import** - Wysłanie danych do API
6. **Zamknięcie** - Automatyczne zamknięcie modalu po sukcesie

## Konfiguracja

### Ustawienia importu drEryk
- **Czas rozpoczęcia "poza kolejką"** - Domyślnie 08:00
- **Czas trwania "poza kolejką"** - Domyślnie 0 minut (wszystkie wizyty na tę samą godzinę)
- **Czas rozpoczęcia "Powtórka Rp."** - Domyślnie 19:00
- **Czas trwania "Powtórka Rp."** - Domyślnie 0 minut (wszystkie wizyty na tę samą godzinę)
- **Usuwanie wizyt poza kolejką** - Opcjonalne (nie importuj ich wcale)
- **Losowe mieszanie wizyt poza kolejką** - Opcjonalne (randomizuje kolejność przypisywania godzin)

### Zakończenie importu
Po udanym imporcie:
- Automatyczny timer został usunięty
- Pojawia się przycisk **ZAKOŃCZ**
- Kliknięcie przycisku przeładowuje stronę
- Przycisk ma zielony kolor i ikonę check_circle

### Obsługiwane formaty
- **drEryk**: CSV z kodowaniem Windows-1250

## Logowanie

### Logi synchronizacji
Wszystkie importy są logowane w:
```
api/v2/data_sync_log/import_sync_[system]_[syncCode]_[timestamp].json
```

### Logi błędów
Błędy są zapisywane w logach PHP i zwracane w odpowiedzi API.

## Bezpieczeństwo

- Walidacja źródła danych
- Sprawdzanie uprawnień importu
- Transakcje bazodanowe
- Logowanie wszystkich operacji
- Czyszczenie danych wejściowych

## Wydajność

- Lazy loading skryptów i modali
- Cache'owanie załadowanych komponentów
- Transakcje bazodanowe dla spójności
- Automatyczne czyszczenie cache po imporcie

## Rozwiązywanie problemów

### Częste problemy

1. **Skrypty nie ładują się**
   - Sprawdź ścieżki w konfiguracji Import Manager
   - Sprawdź uprawnienia plików

2. **Modal nie otwiera się**
   - Sprawdź czy HTML modalu jest poprawny
   - Sprawdź ID modalu w konfiguracji

3. **Funkcje inicjalizacji nie działają**
   - Sprawdź czy funkcje są globalne (`window.functionName`)
   - Sprawdź kolejność ładowania skryptów

4. **Błędy importu**
   - Sprawdź logi w `api/v2/data_sync_log/`
   - Sprawdź format danych wejściowych
   - Sprawdź uprawnienia bazy danych

### Debug

Włącz debug w konsoli przeglądarki:
```javascript
// Sprawdź załadowane systemy
console.log(window.importManager.getAvailableSystems());

// Sprawdź załadowane skrypty
console.log(window.importManager.loadedScripts);

// Sprawdź dostępne funkcje drEryk
console.log(Object.keys(window).filter(key => key.includes('drEryk')));
```

## Migracja ze starego systemu

Stary system importu drEryk został zastąpiony nowym modularnym systemem. Główne zmiany:

1. **Pliki usunięte:**
   - `admin/views/drerik_import_modal.html`
   - `admin/views/drerik_converter.js`

2. **Nowe pliki:**
   - `admin/views/imports/import-manager.js`
   - `admin/views/imports/dreryk/dreryk-import-modal.html`
   - `admin/views/imports/dreryk/dreryk-import.js`
   - `admin/views/imports/dreryk/dreryk-converter.js`

3. **Zmiany w queue_management.php:**
   - Usunięto stary kod inicjalizacji
   - Dodano Import Manager
   - Zmieniono przyciski importu na dynamiczne

Wszystkie funkcjonalności zostały zachowane, ale kod jest teraz bardziej modularny i łatwiejszy do rozszerzania.
