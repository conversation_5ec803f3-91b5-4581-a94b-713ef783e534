/**
 * Import Manager
 * Zarządza importami z różnych systemów medycznych
 */

class ImportManager {
    constructor() {
        this.availableImports = {
            'dreryk': {
                name: 'dr<PERSON><PERSON><PERSON>',
                description: 'Import danych z systemu drEryk',
                modalId: 'drErykImportModal',
                initFunction: 'initializeDrErykImport',
                fileTypes: ['.csv'],
                scripts: [
                    '/admin/views/imports/dreryk/dreryk-converter.js',
                    '/admin/views/imports/dreryk/dreryk-import.js'
                ],
                modal: '/admin/views/imports/dreryk/dreryk-import-modal.html'
            },
            'mmedica': {
                name: 'mMedica',
                description: 'Import danych z systemu mMedica',
                modalId: 'mmedicaImportModal',
                initFunction: 'initializeMmedicaImport',
                fileTypes: ['.csv'],
                scripts: [
                    '/admin/views/imports/mmedica/mmedica-converter.js',
                    '/admin/views/imports/mmedica/mmedica-import.js'
                ],
                modal: '/admin/views/imports/mmedica/mmedica-import-modal.html'
            }
            // Tutaj można dodać inne systemy importu:
            // 'medisoft': {
            //     name: 'MediSoft',
            //     description: 'Import danych z systemu MediSoft',
            //     modalId: 'medisoftImportModal',
            //     initFunction: 'initializeMedisoftImport',
            //     fileTypes: ['.xml', '.csv'],
            //     scripts: [
            //         '/admin/views/imports/medisoft/medisoft-converter.js',
            //         '/admin/views/imports/medisoft/medisoft-import.js'
            //     ],
            //     modal: '/admin/views/imports/medisoft/medisoft-import-modal.html'
            // }
        };
        
        this.loadedScripts = new Set();
        this.loadedModals = new Set();
    }

    /**
     * Inicjalizacja managera importu
     */
    init() {
        console.log('Inicjalizacja Import Manager');
        this.createImportButtons();
    }

    /**
     * Tworzenie przycisków importu dla dostępnych systemów
     */
    createImportButtons() {
        const importContainer = document.getElementById('importButtonsContainer');
        if (!importContainer) {
            console.warn('Nie znaleziono kontenera dla przycisków importu');
            return;
        }

        // Pobierz aktualne źródło importu z ukrytego pola danych
        const importSource = document.getElementById('currentImportSource');
        const currentSource = importSource ? importSource.value : null;
        
        // Jeśli mamy źródło, stwórz tylko przycisk dla tego źródła
        if (currentSource && this.availableImports[currentSource]) {
            const system = this.availableImports[currentSource];
            const button = this.createImportButton(currentSource, system);
            importContainer.appendChild(button);
            console.log(`Utworzono przycisk importu dla źródła: ${currentSource}`);
        }
    }

    /**
     * Tworzenie pojedynczego przycisku importu
     */
    createImportButton(systemKey, system) {
        const button = document.createElement('button');
        button.className = 'px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mr-2 mb-2';
        button.innerHTML = `
            <span class="material-icons-outlined text-sm mr-1">upload</span>
            Import z ${system.name}
        `;
        
        button.addEventListener('click', () => {
            this.openImport(systemKey);
        });

        return button;
    }

    /**
     * Otwieranie importu dla określonego systemu
     */
    async openImport(systemKey) {
        const system = this.availableImports[systemKey];
        if (!system) {
            console.error(`Nieznany system importu: ${systemKey}`);
            return;
        }

        try {
            // Załaduj skrypty jeśli nie są załadowane
            await this.loadScripts(system.scripts);
            
            // Załaduj modal jeśli nie jest załadowany
            await this.loadModal(system.modal, system.modalId);
            
            // Otwórz modal
            this.showModal(system.modalId);
            
            // Inicjalizuj import
            await this.initializeImport(system.initFunction);
            
        } catch (error) {
            console.error(`Błąd podczas otwierania importu ${system.name}:`, error);
            this.showError(`Błąd podczas ładowania importu ${system.name}: ${error.message}`);
        }
    }

    /**
     * Ładowanie skryptów
     */
    async loadScripts(scripts) {
        const loadPromises = scripts.map(scriptPath => {
            if (this.loadedScripts.has(scriptPath)) {
                return Promise.resolve();
            }

            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = scriptPath;
                script.onload = () => {
                    this.loadedScripts.add(scriptPath);
                    console.log(`Załadowano skrypt: ${scriptPath}`);
                    resolve();
                };
                script.onerror = () => {
                    reject(new Error(`Nie można załadować skryptu: ${scriptPath}`));
                };
                document.head.appendChild(script);
            });
        });

        await Promise.all(loadPromises);
    }

    /**
     * Ładowanie modalu
     */
    async loadModal(modalPath, modalId) {
        if (this.loadedModals.has(modalId)) {
            return;
        }

        try {
            const response = await fetch(modalPath);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const modalHtml = await response.text();
            
            // Dodaj modal do DOM
            const modalContainer = document.createElement('div');
            modalContainer.innerHTML = modalHtml;
            document.body.appendChild(modalContainer);
            
            this.loadedModals.add(modalId);
            console.log(`Załadowano modal: ${modalPath}`);
            
        } catch (error) {
            throw new Error(`Nie można załadować modalu: ${modalPath} - ${error.message}`);
        }
    }

    /**
     * Pokazanie modalu
     */
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            console.log(`Otwarto modal: ${modalId}`);
        } else {
            throw new Error(`Nie znaleziono modalu: ${modalId}`);
        }
    }

    /**
     * Inicjalizacja importu
     */
    async initializeImport(initFunctionName) {
        // Poczekaj chwilę na załadowanie skryptów
        await new Promise(resolve => setTimeout(resolve, 100));
        
        if (typeof window[initFunctionName] === 'function') {
            console.log(`Inicjalizacja funkcji: ${initFunctionName}`);
            window[initFunctionName]();
        } else {
            throw new Error(`Funkcja inicjalizacji nie jest dostępna: ${initFunctionName}`);
        }
    }

    /**
     * Pokazanie błędu
     */
    showError(message) {
        // Można zastąpić bardziej zaawansowanym systemem powiadomień
        alert(message);
        console.error(message);
    }

    /**
     * Dodanie nowego systemu importu
     */
    addImportSystem(key, config) {
        this.availableImports[key] = config;
        console.log(`Dodano nowy system importu: ${key}`);
        
        // Odśwież przyciski jeśli manager jest już zainicjalizowany
        const importContainer = document.getElementById('importButtonsContainer');
        if (importContainer) {
            const button = this.createImportButton(key, config);
            importContainer.appendChild(button);
        }
    }

    /**
     * Usunięcie systemu importu
     */
    removeImportSystem(key) {
        if (this.availableImports[key]) {
            delete this.availableImports[key];
            console.log(`Usunięto system importu: ${key}`);
            
            // Odśwież przyciski
            this.createImportButtons();
        }
    }

    /**
     * Pobranie listy dostępnych systemów
     */
    getAvailableSystems() {
        return Object.keys(this.availableImports).map(key => ({
            key: key,
            name: this.availableImports[key].name,
            description: this.availableImports[key].description,
            fileTypes: this.availableImports[key].fileTypes
        }));
    }
}

// Globalna instancja managera
window.importManager = new ImportManager();

// Inicjalizacja po załadowaniu DOM
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.importManager.init();
    }, 100);
});
