<!-- Modal importu z mMedica -->
<div id="mmedicaImportModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold text-slate-900">Import danych z mMedica</h2>
                    <button onclick="closeMmedicaImportModal()" class="text-slate-400 hover:text-slate-600">
                        <span class="material-icons-outlined">close</span>
                    </button>
                </div>

                <!-- Krok 1: Wybór pliku -->
                <div id="mmedicaImportStep1" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-700 mb-2">
                            Krok 1: <PERSON><PERSON><PERSON>rz plik CSV z mMedica
                        </label>
                        <div class="file-drop-area border-2 border-dashed border-slate-300 rounded-lg p-8 text-center hover:border-slate-400 transition-colors cursor-pointer" id="mmedicaFileDropArea">
                            <input type="file" id="mmedicaFileInput" accept=".csv" style="display: none;">
                            <div class="file-message">
                                <span class="material-icons-outlined text-4xl text-slate-400 mb-4">cloud_upload</span>
                                <p class="text-slate-700 font-medium">Przeciągnij i upuść plik CSV tutaj lub kliknij, aby wybrać plik</p>
                                <p class="text-slate-500 text-sm mt-2">Obsługiwane formaty: CSV</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Krok 2: Podgląd i import -->
                <div id="mmedicaImportStep2" class="hidden space-y-4">
                    <div>
                        <h3 class="text-lg font-medium text-slate-900">Podgląd danych do importu</h3>
                    </div>

                    <!-- Statystyki -->
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <div class="bg-blue-50 rounded-lg p-4 flex items-center justify-center">
                            <div class="text-2xl font-bold text-blue-600 mr-2" id="mmedicaModalTotalAppointments">0</div>
                            <div class="text-sm text-blue-600">Wizyt</div>
                        </div>
                        <div class="bg-green-50 rounded-lg p-4 flex items-center justify-center">
                            <div class="text-2xl font-bold text-green-600 mr-2" id="mmedicaModalTotalDoctors">0</div>
                            <div class="text-sm text-green-600">Lekarzy</div>
                        </div>
                        <div class="bg-red-50 rounded-lg p-4 flex items-center justify-center">
                            <div class="text-2xl font-bold text-red-600 mr-2" id="mmedicaModalSkippedAppointments">0</div>
                            <div class="text-sm text-red-600">Pominięto</div>
                        </div>
                    </div>

                    <!-- Informacje o pominiętych wizytach -->
                    <div id="mmedicaModalSkippedInfo" class="hidden bg-amber-50 border border-amber-200 rounded-lg p-4">
                        <h4 class="font-medium text-amber-800 mb-2">
                            Pominięto <span id="mmedicaModalSkippedCount">0</span> wizyt
                        </h4>
                        <div class="max-h-32 overflow-y-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-amber-200">
                                        <th class="text-left py-1 px-2">Pacjent</th>
                                        <th class="text-left py-1 px-2">Lekarz</th>
                                        <th class="text-left py-1 px-2">Powód</th>
                                    </tr>
                                </thead>
                                <tbody id="mmedicaModalSkippedTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Przyciski -->
                    <div class="flex justify-between pt-4 border-t pb-4 border-b">
                        <button onclick="closeMmedicaImportModal()" class="px-4 py-2 border border-slate-300 text-slate-700 rounded-md hover:bg-slate-50">
                            Anuluj
                        </button>
                        <button id="mmedicaModalUploadBtn" disabled class="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-slate-300 disabled:cursor-not-allowed">
                            <span class="material-icons-outlined text-sm mr-1">upload</span>
                            Importuj dane
                        </button>
                    </div>

                    <!-- Podgląd danych -->
                    <div class="space-y-4 max-h-96 overflow-y-auto" id="mmedicaModalDoctorsPreview">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>