/**
 * mMedica Import Module
 * Obsługuje import danych z systemu mMedica
 */

// Globalne zmienne dla modułu mMedica
let mmedicaFileContent = null;
let mmedicaConvertedData = null;
let mmedicaConverter = null;

/**
 * Inicjalizacja modułu importu mMedica
 */
window.initializeMmedicaImport = function() {
    console.log('=== ROZPOCZĘCIE INICJALIZACJI MODALU MMEDICA ===');

    const fileInput = document.getElementById('mmedicaFileInput');
    const fileDropArea = document.getElementById('mmedicaFileDropArea');
    const uploadBtn = document.getElementById('mmedicaModalUploadBtn');

    console.log('Inicjalizacja modalu mMedica:');
    console.log('- fileInput:', fileInput ? 'znaleziony' : 'nie znaleziony', fileInput);
    console.log('- fileDropArea:', fileDropArea ? 'znaleziony' : 'nie znaleziony', fileDropArea);
    console.log('- uploadBtn:', uploadBtn ? 'znaleziony' : 'nie znaleziony', uploadBtn);

    if (!fileInput || !fileDropArea || !uploadBtn) {
        console.error('Niektóre elementy modalu mMedica nie zostały znalezione');
        return;
    }

    // Obsługa przeciągania plików
    if (fileDropArea) {
        fileDropArea.addEventListener('click', () => {
            console.log('Kliknięto na obszar wyboru pliku');
            fileInput.click();
        });

        fileDropArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileDropArea.classList.add('border-purple-400', 'bg-purple-50');
        });

        fileDropArea.addEventListener('dragleave', () => {
            fileDropArea.classList.remove('border-purple-400', 'bg-purple-50');
        });

        fileDropArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileDropArea.classList.remove('border-purple-400', 'bg-purple-50');
            console.log('Upuszczono plik:', e.dataTransfer.files);
            if (e.dataTransfer.files.length > 0) {
                handleMmedicaFileSelect(e.dataTransfer.files[0]);
            }
        });
    }

    // Obsługa wyboru pliku
    if (fileInput) {
        fileInput.addEventListener('change', (e) => {
            console.log('Wybrano plik:', e.target.files);
            if (e.target.files.length > 0) {
                handleMmedicaFileSelect(e.target.files[0]);
            }
        });
    }

    // Przycisk importu
    uploadBtn.addEventListener('click', uploadMmedicaData);
};

/**
 * Obsługa wyboru pliku
 */
window.handleMmedicaFileSelect = async function(file) {
    if (!file.name.endsWith('.csv')) {
        showMmedicaAlert('Proszę wybrać plik CSV', 'danger');
        return;
    }

    try {
        const fileContent = await readMmedicaFileAsText(file);
        window.mmedicaFileContent = fileContent;
        showMmedicaAlert(`Plik "${file.name}" został wczytany pomyślnie`, 'success');

        // Przejdź do kroku 2
        document.getElementById('mmedicaImportStep1').classList.add('hidden');
        document.getElementById('mmedicaImportStep2').classList.remove('hidden');

        // Automatycznie przeanalizuj plik
        processMmedicaData();

        showMmedicaAlert('Dane zostały pomyślnie przeanalizowane.', 'success');
    } catch (error) {
        showMmedicaAlert('Błąd podczas wczytywania pliku: ' + error.message, 'danger');
        console.error('File reading error:', error);
    }
};

/**
 * Czytanie pliku jako tekst z kodowaniem UTF-8
 */
window.readMmedicaFileAsText = function(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            try {
                const text = e.target.result;
                resolve(text);
            } catch (error) {
                reject(new Error('Błąd dekodowania pliku: ' + error.message));
            }
        };
        
        reader.onerror = function() {
            reject(new Error('Błąd odczytu pliku'));
        };
        
        reader.readAsText(file, 'UTF-8');
    });
};

/**
 * Przetwarzanie danych
 */
window.processMmedicaData = function() {
    console.log('Przetwarzanie danych z mMedica');
    
    if (!window.mmedicaConverter) {
        window.mmedicaConverter = new MmedicaConverter();
    }
    
    const convertedData = window.mmedicaConverter.convertDataFromString(window.mmedicaFileContent);
    window.mmedicaConvertedData = convertedData;
    
    console.log('Przetworzone dane:', convertedData);
    
    displayMmedicaModalSummary(convertedData);
};

/**
 * Wyświetlanie podsumowania w modalu
 */
window.displayMmedicaModalSummary = function(convertedData) {
    const totalAppointments = convertedData.doctors.reduce((sum, doctor) => sum + doctor.appointments.length, 0);
    const totalDoctors = convertedData.doctors.length;
    const skippedCount = convertedData.skippedData ? convertedData.skippedData.length : 0;
    
    document.getElementById('mmedicaModalTotalAppointments').textContent = totalAppointments;
    document.getElementById('mmedicaModalTotalDoctors').textContent = totalDoctors;
    document.getElementById('mmedicaModalSkippedAppointments').textContent = skippedCount;
    
    // Informacje o pominiętych wizytach
    if (skippedCount > 0) {
        document.getElementById('mmedicaModalSkippedInfo').classList.remove('hidden');
        document.getElementById('mmedicaModalSkippedCount').textContent = skippedCount;
        
        const tableBody = document.getElementById('mmedicaModalSkippedTableBody');
        tableBody.innerHTML = convertedData.skippedData.map(item => `
            <tr class="border-b border-purple-100">
                <td class="py-1 px-2">${item.pacjent || '-'}</td>
                <td class="py-1 px-2">${item.personel || '-'}</td>
                <td class="py-1 px-2">${item.skipReason}</td>
            </tr>
        `).join('');
    } else {
        document.getElementById('mmedicaModalSkippedInfo').classList.add('hidden');
    }
    
    // Podgląd danych
    const previewContainer = document.getElementById('mmedicaModalDoctorsPreview');
    previewContainer.innerHTML = '';
    
    convertedData.doctors.forEach(doctor => {
        const doctorSection = document.createElement('div');
        doctorSection.className = 'border border-slate-200 rounded-lg overflow-hidden';
        
        doctorSection.innerHTML = `
            <div class="bg-purple-50 px-4 py-2 border-b border-slate-200">
                <h5 class="font-medium text-slate-900">Lekarz: ${doctor.doctorName} (${doctor.appointments.length} wizyt)</h5>
            </div>
            <div class="p-4">
                <div class="overflow-x-auto">
                    <table class="min-w-full text-sm">
                        <thead>
                            <tr class="border-b border-slate-200">
                                <th class="text-left py-1 px-2">Godzina</th>
                                <th class="text-left py-1 px-2">Pacjent</th>
                                <th class="text-left py-1 px-2">Telefon</th>
                                <th class="text-left py-1 px-2">Czas trwania</th>
                                <th class="text-left py-1 px-2">ID wizyty</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${doctor.appointments.map(appointment => `
                                <tr class="border-b border-slate-100">
                                    <td class="py-1 px-2 font-medium">${appointment.appointment_time || 'brak'}</td>
                                    <td class="py-1 px-2">${appointment.patient_name}</td>
                                    <td class="py-1 px-2">${appointment.phone_number || 'brak'}</td>
                                    <td class="py-1 px-2">${appointment.appointment_duration || 'brak'} min</td>
                                    <td class="py-1 px-2">${appointment.external_id || 'brak'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
        
        previewContainer.appendChild(doctorSection);
    });
    
    // Włącz przycisk importu
    const uploadBtn = document.getElementById('mmedicaModalUploadBtn');
    if (uploadBtn) uploadBtn.disabled = false;
};

/**
 * Upload danych do API
 */
window.uploadMmedicaData = function() {
    if (!window.mmedicaConvertedData) {
        showMmedicaAlert('Brak danych do importu', 'warning');
        return;
    }

    // Zablokuj sprawdzanie zmian w tle podczas importu
    try {
        if (typeof window.blockCacheMonitoring === 'function') {
            window.blockCacheMonitoring();
            console.log('Cache monitoring zablokowany przed importem');
        } else if (typeof blockCacheMonitoring === 'function') {
            blockCacheMonitoring();
            console.log('Cache monitoring zablokowany przed importem (lokalna funkcja)');
        } else {
            console.warn('Funkcja blockCacheMonitoring nie jest dostępna');
        }
    } catch (error) {
        console.error('Błąd podczas blokowania cache monitoring:', error);
    }

    // Pokaż komunikat o trwającym imporcie
    showMmedicaAlert('Trwa import danych... Proszę czekać.', 'info');

    const uploadBtn = document.getElementById('mmedicaModalUploadBtn');
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<span class="material-icons-outlined text-sm animate-spin">refresh</span> Importowanie...';

    let apiData;
    try {
        if (typeof window.convertMmedicaToApiFormat === 'function') {
            apiData = window.convertMmedicaToApiFormat(window.mmedicaConvertedData);
        } else if (typeof convertMmedicaToApiFormat === 'function') {
            apiData = convertMmedicaToApiFormat(window.mmedicaConvertedData);
        } else {
            throw new Error('Funkcja convertMmedicaToApiFormat nie jest dostępna');
        }
    } catch (error) {
        console.error('Błąd podczas konwersji danych:', error);
        showMmedicaAlert('Błąd podczas konwersji danych: ' + error.message, 'danger');
        
        // Odblokuj monitoring w przypadku błędu
        try {
            if (typeof window.unblockCacheMonitoring === 'function') {
                window.unblockCacheMonitoring();
            } else if (typeof unblockCacheMonitoring === 'function') {
                unblockCacheMonitoring();
            }
        } catch (e) {
            console.error('Błąd podczas odblokowywania cache monitoring:', e);
        }
        
        // Przywróć przycisk po błędzie
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<span class="material-icons-outlined text-sm">upload</span> Importuj dane';
        return;
    }

    fetch('/api/v2/import', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(data => {
                // Przekaz dodatkowe informacje o błędzie, jeśli są dostępne
                const error = new Error(data.message || `Błąd HTTP: ${response.status}`);
                error.data = data.data || null;
                error.status = response.status;
                throw error;
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            const importedCount = data.data?.imported || 0;
            const totalCount = data.data?.total || 0;
            const errors = data.data?.errors || [];

            let message = `Import zakończony pomyślnie!\n\nZaimportowano: ${importedCount}/${totalCount} wizyt`;

            if (errors.length > 0) {
                message += `\n\nOstrzeżenia (${errors.length}):\n${errors.slice(0, 3).join('\n')}`;
                if (errors.length > 3) {
                    message += `\n...i ${errors.length - 3} innych`;
                }
            }

            // Przywróć przycisk po sukcesie
            uploadBtn.disabled = false;
            uploadBtn.innerHTML = '<span class="material-icons-outlined text-sm">upload</span> Importuj dane';

            // Znajdź przyciski Anuluj i Importuj i ukryj je
            const cancelBtn = document.querySelector('button[onclick="closeMmedicaImportModal()"]');
            const uploadBtnElement = document.getElementById('mmedicaModalUploadBtn');

            if (cancelBtn) cancelBtn.style.display = 'none';
            if (uploadBtnElement) uploadBtnElement.style.display = 'none';

            // Pokaż komunikat sukcesu
            showMmedicaAlert(message, 'success');

            // Dodaj przycisk ZAKOŃCZ
            const finishButtonContainer = document.createElement('div');
            finishButtonContainer.className = 'text-center mt-4';
            finishButtonContainer.innerHTML = `
                <button id="mmedicaFinishImportBtn" class="px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-lg font-medium">
                    <span class="material-icons-outlined text-lg mr-2">check_circle</span>
                    ZAKOŃCZ
                </button>
            `;

            const modalFooter = document.querySelector('#mmedicaImportModal .flex.justify-between.pt-4');
            if (modalFooter) {
                modalFooter.appendChild(finishButtonContainer);

                // Dodaj event listener do przycisku ZAKOŃCZ
                const finishBtn = document.getElementById('mmedicaFinishImportBtn');
                if (finishBtn) {
                    finishBtn.addEventListener('click', () => {
                        // Odblokuj monitoring przed przeładowaniem
                        try {
                            if (typeof window.unblockCacheMonitoring === 'function') {
                                window.unblockCacheMonitoring();
                            } else if (typeof unblockCacheMonitoring === 'function') {
                                unblockCacheMonitoring();
                            }
                        } catch (error) {
                            console.error('Błąd podczas odblokowywania cache monitoring:', error);
                        }

                        window.location.reload();
                    });
                }
            }

        } else {
            throw new Error(data.message || 'Nieznany błąd podczas importu');
        }
    })
    .catch(error => {
        console.error('Import error:', error);
        
        // Sprawdź czy błąd jest związany z niezmapowanymi lekarzami
        const isUnmappedDoctorsError = (
            (error.message && error.message.includes('niezmapowanych lekarzy')) ||
            (error.data && error.data.unmapped_count > 0)
        );
        
        if (isUnmappedDoctorsError) {
            // Przygotuj komunikat z liczbą niezmapowanych lekarzy
            let message = error.message;
            if (error.data && error.data.unmapped_count) {
                message = `Znaleziono ${error.data.unmapped_count} niezmapowanych lekarzy. Musisz zmapować ich przed kontynuowaniem importu.`;
            }
            
            // Wyświetl alert z przyciskiem do mapowania lekarzy
            showMmedicaAlertWithButton(
                message,
                'warning', // Użyj warning zamiast danger dla lepszego kontrastu
                'Przejdź do mapowania lekarzy',
                () => {
                    window.location.href = '/admin/lekarze/map-import';
                }
            );
        } else {
            showMmedicaAlert(`Błąd importu: ${error.message}`, 'danger');
        }

        // Odblokuj monitoring w przypadku błędu
        try {
            if (typeof window.unblockCacheMonitoring === 'function') {
                window.unblockCacheMonitoring();
            } else if (typeof unblockCacheMonitoring === 'function') {
                unblockCacheMonitoring();
            }
        } catch (error) {
            console.error('Błąd podczas odblokowywania cache monitoring:', error);
        }

        // Przywróć przycisk po błędzie
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<span class="material-icons-outlined text-sm">upload</span> Importuj dane';
    });
};

/**
 * Wyświetlanie alertów
 */
window.showMmedicaAlert = function(message, type, duration = 5000) {
    const existingAlerts = document.querySelectorAll('#mmedicaImportModal .mmedica-alert');
    existingAlerts.forEach(alert => alert.remove());

    // Kolory dla różnych typów alertów (Tailwind CSS)
    const alertStyles = {
        'success': 'bg-green-50 border-green-200 text-green-800',
        'danger': 'bg-red-50 border-red-200 text-red-800',
        'warning': 'bg-amber-50 border-amber-200 text-amber-800',
        'info': 'bg-blue-50 border-blue-200 text-blue-800'
    };

    const iconNames = {
        'success': 'check_circle',
        'danger': 'error',
        'warning': 'warning',
        'info': 'info'
    };

    const alertDiv = document.createElement('div');
    alertDiv.className = `mmedica-alert border rounded-lg p-4 mb-4 ${alertStyles[type] || alertStyles['info']}`;
    alertDiv.style.zIndex = '60'; // Upewnij się, że alert jest na wierzchu
    alertDiv.style.position = 'relative'; // Upewnij się, że alert jest pozycjonowany
    alertDiv.innerHTML = `
        <div class="flex items-center">
            <span class="material-icons-outlined mr-3 text-lg">
                ${iconNames[type] || 'info'}
            </span>
            <span class="flex-1">${message.replace(/\n/g, ' ')}</span>
            <button type="button" class="ml-3 text-slate-400 hover:text-slate-600" onclick="this.parentElement.parentElement.remove()">
                <span class="material-icons-outlined text-lg">close</span>
            </button>
        </div>
    `;

    const modalContent = document.querySelector('#mmedicaImportModal .p-6');
    if (modalContent) {
        // Wstaw alert na samej górze modalu, zaraz pod tytułem
        const modalTitle = document.querySelector('#mmedicaImportModal .flex.justify-between.items-center.mb-6');
        if (modalTitle) {
            modalTitle.parentNode.insertBefore(alertDiv, modalTitle.nextSibling);
        } else {
            modalContent.insertBefore(alertDiv, modalContent.firstChild);
        }
        
        // Przewiń modal na górę, aby pokazać alert
        const modalContainer = document.querySelector('#mmedicaImportModal .overflow-y-auto');
        if (modalContainer) {
            modalContainer.scrollTop = 0;
        }
        
        // Upewnij się, że modal jest widoczny
        const modal = document.getElementById('mmedicaImportModal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    } else {
        // Jeśli modal nie jest dostępny, pokaż alert na stronie
        document.body.insertBefore(alertDiv, document.body.firstChild);
    }

    // Nie usuwaj alertu automatycznie, jeśli duration jest większe niż 10 sekund
    if (duration <= 10000) {
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, duration);
    }
};

/**
 * Wyświetlanie alertów z przyciskiem akcji
 */
window.showMmedicaAlertWithButton = function(message, type, buttonText, buttonCallback) {
    const existingAlerts = document.querySelectorAll('#mmedicaImportModal .mmedica-alert');
    existingAlerts.forEach(alert => alert.remove());

    // Kolory dla różnych typów alertów (Tailwind CSS)
    const alertStyles = {
        'success': 'bg-green-50 border-green-200 text-green-800',
        'danger': 'bg-red-50 border-red-200 text-red-800',
        'warning': 'bg-amber-50 border-amber-200 text-amber-800',
        'info': 'bg-blue-50 border-blue-200 text-blue-800'
    };

    const iconNames = {
        'success': 'check_circle',
        'danger': 'error',
        'warning': 'warning',
        'info': 'info'
    };

    const alertDiv = document.createElement('div');
    alertDiv.className = `mmedica-alert border rounded-lg p-4 mb-4 ${alertStyles[type] || alertStyles['info']}`;
    alertDiv.style.zIndex = '60'; // Upewnij się, że alert jest na wierzchu
    alertDiv.style.position = 'relative'; // Upewnij się, że alert jest pozycjonowany
    alertDiv.innerHTML = `
        <div class="flex items-start">
            <span class="material-icons-outlined mr-3 text-lg mt-0.5">
                ${iconNames[type] || 'info'}
            </span>
            <div class="flex-1">
                <div class="mb-3">${message.replace(/\n/g, ' ')}</div>
                <button type="button" id="mmedicaAlertActionButton" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors text-sm">
                    ${buttonText}
                </button>
            </div>
            <button type="button" class="ml-3 text-slate-400 hover:text-slate-600" onclick="this.parentElement.parentElement.remove()">
                <span class="material-icons-outlined text-lg">close</span>
            </button>
        </div>
    `;

    const modalContent = document.querySelector('#mmedicaImportModal .p-6');
    if (modalContent) {
        // Wstaw alert na samej górze modalu, zaraz pod tytułem
        const modalTitle = document.querySelector('#mmedicaImportModal .flex.justify-between.items-center.mb-6');
        if (modalTitle) {
            modalTitle.parentNode.insertBefore(alertDiv, modalTitle.nextSibling);
        } else {
            modalContent.insertBefore(alertDiv, modalContent.firstChild);
        }
        
        // Przewiń modal na górę, aby pokazać alert
        const modalContainer = document.querySelector('#mmedicaImportModal .overflow-y-auto');
        if (modalContainer) {
            modalContainer.scrollTop = 0;
        }
        
        // Upewnij się, że modal jest widoczny
        const modal = document.getElementById('mmedicaImportModal');
        if (modal) {
            modal.classList.remove('hidden');
        }

        // Dodaj nasłuchiwanie na przycisk akcji
        const actionButton = alertDiv.querySelector('#mmedicaAlertActionButton');
        if (actionButton && typeof buttonCallback === 'function') {
            actionButton.addEventListener('click', buttonCallback);
        }
    } else {
        // Jeśli modal nie jest dostępny, pokaż alert na stronie
        document.body.insertBefore(alertDiv, document.body.firstChild);
        
        // Dodaj nasłuchiwanie na przycisk akcji
        const actionButton = alertDiv.querySelector('#mmedicaAlertActionButton');
        if (actionButton && typeof buttonCallback === 'function') {
            actionButton.addEventListener('click', buttonCallback);
        }
    }
};

/**
 * Zamykanie modalu
 */
window.closeMmedicaImportModal = function() {
    console.log('Zamykanie modalu mMedica');
    const modal = document.getElementById('mmedicaImportModal');
    if (modal) {
        modal.classList.add('hidden');
        if (typeof resetMmedicaImport === 'function') {
            resetMmedicaImport();
        }
    }
};

window.showMmedicaImportModal = function() {
    console.log('Otwieranie modalu mMedica');
    const modal = document.getElementById('mmedicaImportModal');
    if (modal) {
        modal.classList.remove('hidden');
    } else {
        console.error('Nie znaleziono modalu mmedicaImportModal');
    }
};

/**
 * Reset modalu
 */
window.resetMmedicaImport = function() {
    document.getElementById('mmedicaImportStep1').classList.remove('hidden');
    document.getElementById('mmedicaImportStep2').classList.add('hidden');
    document.getElementById('mmedicaFileInput').value = '';

    const uploadBtn = document.getElementById('mmedicaModalUploadBtn');
    if (uploadBtn) {
        uploadBtn.disabled = true;
        uploadBtn.style.display = ''; // Przywróć widoczność przycisku
    }

    // Przywróć widoczność przycisku Anuluj
    const cancelBtn = document.querySelector('button[onclick="closeMmedicaImportModal()"]');
    if (cancelBtn) {
        cancelBtn.style.display = '';
    }

    // Usuń przycisk ZAKOŃCZ jeśli istnieje
    const finishBtn = document.getElementById('mmedicaFinishImportBtn');
    if (finishBtn) {
        finishBtn.parentElement.remove();
    }

    window.mmedicaFileContent = null;
    window.mmedicaConvertedData = null;

    const existingAlerts = document.querySelectorAll('#mmedicaImportModal .mmedica-alert');
    existingAlerts.forEach(alert => alert.remove());
};