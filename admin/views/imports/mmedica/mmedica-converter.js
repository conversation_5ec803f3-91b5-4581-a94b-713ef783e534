/**
 * mMedica Data Converter
 * Konwertuje dane z formatu mMedica CSV do formatu API
 */

/**
 * Funkcja MD5 hash
 */
function md5(string) {
    function md5cycle(x, k) {
        var a = x[0], b = x[1], c = x[2], d = x[3];
        a = ff(a, b, c, d, k[0], 7, -680876936);
        d = ff(d, a, b, c, k[1], 12, -389564586);
        c = ff(c, d, a, b, k[2], 17, 606105819);
        b = ff(b, c, d, a, k[3], 22, -1044525330);
        a = ff(a, b, c, d, k[4], 7, -176418897);
        d = ff(d, a, b, c, k[5], 12, 1200080426);
        c = ff(c, d, a, b, k[6], 17, -1473231341);
        b = ff(b, c, d, a, k[7], 22, -45705983);
        a = ff(a, b, c, d, k[8], 7, 1770035416);
        d = ff(d, a, b, c, k[9], 12, -1958414417);
        c = ff(c, d, a, b, k[10], 17, -42063);
        b = ff(b, c, d, a, k[11], 22, -1990404162);
        a = ff(a, b, c, d, k[12], 7, 1804603682);
        d = ff(d, a, b, c, k[13], 12, -40341101);
        c = ff(c, d, a, b, k[14], 17, -1502002290);
        b = ff(b, c, d, a, k[15], 22, 1236535329);
        a = gg(a, b, c, d, k[1], 5, -165796510);
        d = gg(d, a, b, c, k[6], 9, -1069501632);
        c = gg(c, d, a, b, k[11], 14, 643717713);
        b = gg(b, c, d, a, k[0], 20, -373897302);
        a = gg(a, b, c, d, k[5], 5, -701558691);
        d = gg(d, a, b, c, k[10], 9, 38016083);
        c = gg(c, d, a, b, k[15], 14, -660478335);
        b = gg(b, c, d, a, k[4], 20, -405537848);
        a = gg(a, b, c, d, k[9], 5, 568446438);
        d = gg(d, a, b, c, k[14], 9, -1019803690);
        c = gg(c, d, a, b, k[3], 14, -187363961);
        b = gg(b, c, d, a, k[8], 20, 1163531501);
        a = gg(a, b, c, d, k[13], 5, -1444681467);
        d = gg(d, a, b, c, k[2], 9, -51403784);
        c = gg(c, d, a, b, k[7], 14, 1735328473);
        b = gg(b, c, d, a, k[12], 20, -1926607734);
        a = hh(a, b, c, d, k[5], 4, -378558);
        d = hh(d, a, b, c, k[8], 11, -2022574463);
        c = hh(c, d, a, b, k[11], 16, 1839030562);
        b = hh(b, c, d, a, k[14], 23, -35309556);
        a = hh(a, b, c, d, k[1], 4, -1530992060);
        d = hh(d, a, b, c, k[4], 11, 1272893353);
        c = hh(c, d, a, b, k[7], 16, -155497632);
        b = hh(b, c, d, a, k[10], 23, -1094730640);
        a = hh(a, b, c, d, k[13], 4, 681279174);
        d = hh(d, a, b, c, k[0], 11, -358537222);
        c = hh(c, d, a, b, k[3], 16, -722521979);
        b = hh(b, c, d, a, k[6], 23, 76029189);
        a = hh(a, b, c, d, k[9], 4, -640364487);
        d = hh(d, a, b, c, k[12], 11, -421815835);
        c = hh(c, d, a, b, k[15], 16, 530742520);
        b = hh(b, c, d, a, k[2], 23, -995338651);
        a = ii(a, b, c, d, k[0], 6, -198630844);
        d = ii(d, a, b, c, k[7], 10, 1126891415);
        c = ii(c, d, a, b, k[14], 15, -1416354905);
        b = ii(b, c, d, a, k[5], 21, -57434055);
        a = ii(a, b, c, d, k[12], 6, 1700485571);
        d = ii(d, a, b, c, k[3], 10, -1894986606);
        c = ii(c, d, a, b, k[10], 15, -1051523);
        b = ii(b, c, d, a, k[1], 21, -2054922799);
        a = ii(a, b, c, d, k[8], 6, 1873313359);
        d = ii(d, a, b, c, k[15], 10, -30611744);
        c = ii(c, d, a, b, k[6], 15, -1560198380);
        b = ii(b, c, d, a, k[13], 21, 1309151649);
        a = ii(a, b, c, d, k[4], 6, -145523070);
        d = ii(d, a, b, c, k[11], 10, -1120210379);
        c = ii(c, d, a, b, k[2], 15, 718787259);
        b = ii(b, c, d, a, k[9], 21, -343485551);
        x[0] = add32(a, x[0]);
        x[1] = add32(b, x[1]);
        x[2] = add32(c, x[2]);
        x[3] = add32(d, x[3]);
    }
    function cmn(q, a, b, x, s, t) {
        a = add32(add32(a, q), add32(x, t));
        return add32((a << s) | (a >>> (32 - s)), b);
    }
    function ff(a, b, c, d, x, s, t) {
        return cmn((b & c) | ((~b) & d), a, b, x, s, t);
    }
    function gg(a, b, c, d, x, s, t) {
        return cmn((b & d) | (c & (~d)), a, b, x, s, t);
    }
    function hh(a, b, c, d, x, s, t) {
        return cmn(b ^ c ^ d, a, b, x, s, t);
    }
    function ii(a, b, c, d, x, s, t) {
        return cmn(c ^ (b | (~d)), a, b, x, s, t);
    }
    function md51(s) {
        var n = s.length,
            state = [1732584193, -271733879, -1732584194, 271733878], i;
        for (i = 64; i <= s.length; i += 64) {
            md5cycle(state, md5blk(s.substring(i - 64, i)));
        }
        s = s.substring(i - 64);
        var tail = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        for (i = 0; i < s.length; i++)
            tail[i >> 2] |= s.charCodeAt(i) << ((i % 4) << 3);
        tail[i >> 2] |= 0x80 << ((i % 4) << 3);
        if (i > 55) {
            md5cycle(state, tail);
            for (i = 0; i < 16; i++) tail[i] = 0;
        }
        tail[14] = n * 8;
        md5cycle(state, tail);
        return state;
    }
    function md5blk(s) {
        var md5blks = [], i;
        for (i = 0; i < 64; i += 4) {
            md5blks[i >> 2] = s.charCodeAt(i)
                + (s.charCodeAt(i + 1) << 8)
                + (s.charCodeAt(i + 2) << 16)
                + (s.charCodeAt(i + 3) << 24);
        }
        return md5blks;
    }
    var hex_chr = '0123456789abcdef'.split('');
    function rhex(n) {
        var s = '', j = 0;
        for (; j < 4; j++)
            s += hex_chr[(n >> (j * 8 + 4)) & 0x0F]
                + hex_chr[(n >> (j * 8)) & 0x0F];
        return s;
    }
    function hex(x) {
        for (var i = 0; i < x.length; i++)
            x[i] = rhex(x[i]);
        return x.join('');
    }
    function add32(a, b) {
        return (a + b) & 0xFFFFFFFF;
    }
    return hex(md51(string));
}

/**
 * Konwersja danych mMedica do formatu API
 */
window.convertMmedicaToApiFormat = function(mmedicaData) {
    const appointments = [];

    mmedicaData.doctors.forEach(doctor => {
        doctor.appointments.forEach(appointment => {
            const doctorId = md5(doctor.doctorName.trim().toUpperCase());
            
            appointments.push({
                doctor_id: doctorId,
                doctor_name: doctor.doctorName,
                patient_name: appointment.patient_name,
                appointment_time: appointment.appointment_time,
                appointment_date: appointment.appointment_date,
                appointment_duration: appointment.appointment_duration,
                phone_number: appointment.phone_number,
                external_id: appointment.external_id
            });
        });
    });
    
    // Grupuj wizyty po datach
    const dayGroups = {};

    appointments.forEach(appointment => {
        const date = appointment.appointment_date;
        if (!dayGroups[date]) {
            dayGroups[date] = {};
        }

        const doctorId = appointment.doctor_id;
        if (!dayGroups[date][doctorId]) {
            dayGroups[date][doctorId] = {
                doctorId: doctorId,
                doctorName: appointment.doctor_name || 'Nieznany lekarz',
                appointments: []
            };
        }

        const patientParts = appointment.patient_name.trim().split(' ');
        const patientFirstName = patientParts[0] || '';
        const patientLastName = patientParts.slice(1).join(' ') || '';

        dayGroups[date][doctorId].appointments.push({
            appointmentId: appointment.external_id,
            patientFirstName: patientFirstName,
            patientLastName: patientLastName,
            appointmentStart: appointment.appointment_time,
            appointmentDuration: appointment.appointment_duration,
            phone_number: appointment.phone_number
        });
    });

    const days = [];
    Object.keys(dayGroups).forEach(date => {
        const doctors = Object.values(dayGroups[date]);
        days.push({
            date: date,
            doctors: doctors
        });
    });

    return {
        source: mmedicaData.source || "mMedica",
        syncCode: mmedicaData.syncCode || "mMedica_" + new Date().toISOString().slice(0, 10),
        syncData: {
            days: days
        }
    };
};

/**
 * Klasa konwertera mMedica
 */
window.MmedicaConverter = class MmedicaConverter {
    convertDataFromString(content, settings = {}) {
        console.log('Rozpoczęcie konwersji danych z ustawieniami:', settings);
        
        const parsedData = this.parseCSV(content);
        const filteredData = this.filterAndNormalizeData(parsedData, settings);
        const convertedData = this.convertToAPIFormat(filteredData);
        
        console.log('Zakończono konwersję danych');
        return convertedData;
    }
    
    parseCSV(content) {
        const lines = content.split('\n');
        const result = [];
        let skipped = [];
        
        // Znajdź nagłówek i stwórz mapowanie kolumn
        let headerLine = null;
        let columnIndex = {};
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (line.includes('Data') && line.includes('Pacjent') && line.includes('Personel')) {
                headerLine = line;
                columnIndex = this.createColumnMapping(line);
                break;
            }
        }
        
        if (!headerLine) {
            throw new Error('Nie znaleziono nagłówka CSV z wymaganymi kolumnami');
        }
        
        console.log('Wykryto mapowanie kolumn:', columnIndex);
        
        // Przetwarzaj linie danych
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // Pomiń puste linie i nagłówek
            if (!line || line === headerLine || line.includes('Data')) {
                continue;
            }
            
            // Podziel linię po przecinkach (uwzględnij przecinki w cudzysłowach)
            const parts = this.parseCSVLine(line);
            
            if (parts.length < Math.max(...Object.values(columnIndex)) + 1) {
                continue; // Pomiń linie z niewystarczającą liczbą kolumn
            }
            
            const appointment = {
                data: parts[columnIndex.data] || '',
                godzinaRozpoczecia: parts[columnIndex.godzinaRozpoczecia] || '',
                godzinaZakonczenia: parts[columnIndex.godzinaZakonczenia] || '',
                dlugosc: parts[columnIndex.dlugosc] || '',
                pacjent: parts[columnIndex.pacjent] || '',
                pesel: parts[columnIndex.pesel] || '',
                kontakt: parts[columnIndex.kontakt] || '',
                uwagi: parts[columnIndex.uwagi] || '',
                status: parts[columnIndex.status] || '',
                personel: parts[columnIndex.personel] || '',
                miejsce: parts[columnIndex.miejsce] || '',
                typWizyty: parts[columnIndex.typWizyty] || '',
                trybPrzyjecia: parts[columnIndex.trybPrzyjecia] || ''
            };
            
            result.push(appointment);
        }
        
        return { data: result, skipped: skipped };
    }
    
    createColumnMapping(headerLine) {
        const parts = this.parseCSVLine(headerLine);
        const mapping = {};
        
        // Mapuj kolumny na podstawie nazw
        for (let i = 0; i < parts.length; i++) {
            const part = parts[i].toLowerCase().trim();
            
            if (part.includes('data')) {
                mapping.data = i;
            } else if (part.includes('od godz.') || part.includes('od godz')) {
                mapping.godzinaRozpoczecia = i;
            } else if (part.includes('do godz.') || part.includes('do godz')) {
                mapping.godzinaZakonczenia = i;
            } else if (part.includes('długość') || part.includes('dlugo')) {
                mapping.dlugosc = i;
            } else if (part.includes('pacjent')) {
                mapping.pacjent = i;
            } else if (part.includes('pesel')) {
                mapping.pesel = i;
            } else if (part.includes('kontakt')) {
                mapping.kontakt = i;
            } else if (part.includes('uwagi')) {
                mapping.uwagi = i;
            } else if (part.includes('status')) {
                mapping.status = i;
            } else if (part.includes('personel')) {
                mapping.personel = i;
            } else if (part.includes('miejsce')) {
                mapping.miejsce = i;
            } else if (part.includes('wizyta')) {
                mapping.typWizyty = i;
            } else if (part.includes('tryb przyj')) {
                mapping.trybPrzyjecia = i;
            }
        }
        
        // Ustaw domyślne wartości jeśli nie znaleziono mapowania
        const defaults = {
            data: 0,
            godzinaRozpoczecia: 1,
            godzinaZakonczenia: 2,
            dlugosc: 3,
            pacjent: 4,
            pesel: 5,
            kontakt: 6,
            uwagi: 7,
            status: 8,
            personel: 14,
            miejsce: 15,
            typWizyty: 17,
            trybPrzyjecia: 18
        };
        
        // Uzupełnij brakujące mapowania domyślnymi wartościami
        Object.keys(defaults).forEach(key => {
            if (mapping[key] === undefined) {
                mapping[key] = defaults[key];
            }
        });
        
        return mapping;
    }
    
    parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"') {
                inQuotes = !inQuotes;
            } else if (char === ',' && !inQuotes) {
                result.push(current.trim());
                current = '';
            } else {
                current += char;
            }
        }
        
        result.push(current.trim());
        return result;
    }
    
    filterAndNormalizeData(parsedData, settings = {}) {
        const result = [];
        const skipped = [...parsedData.skipped];
        
        for (const item of parsedData.data) {
            let skipReason = null;
            
            // Sprawdź wymagane pola
            if (!item.pacjent || item.pacjent.trim() === '') {
                skipReason = 'Brak nazwy pacjenta';
            }
            
            // Sprawdź nazwisko lekarza - użyj kolumny Personel
            let doctorName = item.personel ? item.personel.trim() : '';
            
            if (!doctorName) {
                skipReason = 'Brak nazwy lekarza';
            }
            
            if (!item.data || item.data.trim() === '') {
                skipReason = 'Brak daty wizyty';
            }
            
            if (skipReason) {
                skipped.push({
                    ...item,
                    skipReason: skipReason
                });
                continue;
            }
            
            // Pobierz i wyczyść dane kontaktowe
            let phone = item.kontakt ? item.kontakt.replace(/[^0-9]/g, '') : '';
            
            // Ustaw domyślny czas trwania jeśli nie podany
            let duration = parseInt(item.dlugosc) || 15;
            
            // Użyj godziny rozpoczęcia jako czasu wizyty
            let appointmentTime = item.godzinaRozpoczecia || '';
            
            // Jeśli brak godziny rozpoczęcia, użyj godziny zakończenia
            if (!appointmentTime && item.godzinaZakonczenia) {
                appointmentTime = item.godzinaZakonczenia;
            }
            
            const normalizedItem = {
                ...item,
                appointmentTime: appointmentTime,
                duration: duration,
                phone: phone,
                doctorName: doctorName,
                patientName: item.pacjent.trim(),
                appointmentDate: item.data.trim()
            };
            
            result.push(normalizedItem);
        }
        
        return { data: result, skipped: skipped };
    }
    
    convertToAPIFormat(filteredData) {
        const doctorsMap = new Map();
        
        for (const item of filteredData.data) {
            const doctorName = item.doctorName;
            
            if (!doctorsMap.has(doctorName)) {
                doctorsMap.set(doctorName, {
                    doctorName: doctorName,
                    appointments: []
                });
            }
            
            // Generuj ID wizyty jeśli nie ma
            let externalId = item.pesel || null;
            if (!externalId || externalId.trim() === '') {
                const hashData = `${item.patientName}_${item.appointmentDate}_${item.doctorName}`;
                externalId = md5(hashData);
            }
            
            const appointment = {
                patient_name: item.patientName,
                appointment_time: item.appointmentTime,
                appointment_date: item.appointmentDate,
                appointment_duration: item.duration,
                phone_number: item.phone,
                external_id: externalId
            };
            
            doctorsMap.get(doctorName).appointments.push(appointment);
        }
        
        const doctors = Array.from(doctorsMap.values()).map(doctor => {
            // Sortuj wizyty po czasie
            doctor.appointments.sort((a, b) => {
                const timeA = a.appointment_time || '';
                const timeB = b.appointment_time || '';
                return timeA.localeCompare(timeB);
            });
            return doctor;
        });
        
        return {
            source: "mMedica",
            syncCode: "mMedica_" + new Date().toISOString().slice(0, 10),
            doctors: doctors,
            skippedData: filteredData.skipped
        };
    }
};