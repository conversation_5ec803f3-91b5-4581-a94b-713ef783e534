<!-- Ustawienia systemu -->
<div class="min-h-full w-full">
    <!-- Header -->
    <div class="bg-white border-b border-slate-200 px-6 py-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-slate-900">Ustawienia systemu</h1>
                <p class="text-slate-600 mt-1">Zarządzaj konfiguracją systemu kolejkowego KtoOstatni</p>
            </div>
            <div class="flex items-center space-x-3">
                <button onclick="location.reload()" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center" title="Odśwież stronę i załaduj najnowsze ustawienia">
                    <span class="material-icons-outlined mr-2">refresh</span>
                    O<PERSON><PERSON><PERSON><PERSON><PERSON>
                </button>
            </div>
        </div>
    </div>

    <!-- Komunikaty -->
    <?php if (isset($success)): ?>
        <div class="mx-6 mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                <span class="text-green-800"><?= htmlspecialchars($success) ?></span>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="mx-6 mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-red-600 mr-2">error</span>
                <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
            </div>
        </div>
    <?php endif; ?>

    <!-- Główna zawartość - 2 kolumny -->
    <div class="flex flex-col lg:flex-row gap-6 p-6">
        <!-- Lewa kolumna - Ustawienia główne -->
        <div class="flex-1 space-y-6">
            <!-- Ustawienia ogólne -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
                <div class="bg-blue-50 border-b border-blue-200 px-6 py-4">
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-blue-600 mr-3">settings</span>
                        <h2 class="text-lg font-semibold text-blue-900">Ustawienia ogólne</h2>
                    </div>
                    <p class="text-blue-700 text-sm mt-1">Podstawowe konfiguracje systemu</p>
                </div>
                
                <div class="p-6">
                    <form method="POST" class="space-y-4">
                        <input type="hidden" name="action" value="update_general">

                        <div class="space-y-4">
                            <div>
                                <label for="default_appointment_duration" class="block text-sm font-medium text-slate-700 mb-2">
                                    Domyślny czas wizyty (minuty)
                                </label>
                                <div class="flex items-center space-x-3">
                                    <input type="number"
                                           id="default_appointment_duration"
                                           name="default_appointment_duration"
                                           value="<?= $settings['default_appointment_duration'] ?>"
                                           min="5" max="120"
                                           class="flex-1 px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           title="Domyślny czas trwania wizyty w minutach (5-120)">
                                    <span class="text-sm text-slate-500">minut</span>
                                </div>
                                <p class="mt-1 text-xs text-slate-500">
                                    Domyślny czas trwania wizyty, który będzie stosowany przy tworzeniu nowych harmonogramów.
                                </p>
                            </div>
                        </div>
                        
                        <div class="pt-4 border-t border-slate-200">
                            <button type="submit"
                                    class="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center justify-center"
                                    title="Zapisz ustawienia ogólne systemu">
                                <span class="material-icons-outlined mr-2">save</span>
                                Zapisz ustawienia ogólne
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Ustawienia statusów -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
                <div class="bg-amber-50 border-b border-amber-200 px-6 py-4">
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-amber-600 mr-3">toggle_on</span>
                        <h2 class="text-lg font-semibold text-amber-900">Ustawienia statusów</h2>
                    </div>
                    <p class="text-amber-700 text-sm mt-1">Zarządzaj funkcjonalnościami systemu</p>
                </div>
                
                <div class="p-6">
                    <form method="POST" class="space-y-4">
                        <input type="hidden" name="action" value="update_status_settings">

                        <div class="space-y-4">
                            <div class="flex items-start p-3 border border-slate-200 rounded-lg">
                                <input type="checkbox"
                                    id="enable_appointment_confirmation"
                                    name="enable_appointment_confirmation"
                                    <?= $settings['enable_appointment_confirmation'] ? 'checked' : '' ?>
                                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded mt-1"
                                    title="Włącz możliwość oznaczania wizyt jako potwierdzonych">
                                <div class="ml-3 flex-1">
                                    <label for="enable_appointment_confirmation" class="block text-sm font-medium text-slate-700">
                                        Status potwierdzenia wizyty
                                    </label>
                                    <p class="text-xs text-slate-500 mt-1">
                                        Włącz możliwość oznaczania wizyt jako potwierdzonych. Pozwala na śledzenie, które wizyty zostały potwierdzone przez pacjentów.
                                    </p>
                                </div>
                            </div>

                            <div class="flex items-start p-3 border border-slate-200 rounded-lg">
                                <input type="checkbox"
                                    id="enable_attendance_confirmation"
                                    name="enable_attendance_confirmation"
                                    <?= $settings['enable_attendance_confirmation'] ? 'checked' : '' ?>
                                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded mt-1"
                                    title="Włącz możliwość oznaczania pacjentów jako obecnych w poczekalni">
                                <div class="ml-3 flex-1">
                                    <label for="enable_attendance_confirmation" class="block text-sm font-medium text-slate-700">
                                        Status obecności pacjenta
                                    </label>
                                    <p class="text-xs text-slate-500 mt-1">
                                        Włącz możliwość oznaczania pacjentów jako obecnych w poczekalni. Ułatwia zarządzanie kolejką i planowanie wizyt.
                                    </p>
                                </div>
                            </div>

                            <div class="flex items-start p-3 border border-slate-200 rounded-lg">
                                <input type="checkbox"
                                    id="enable_sms_sending"
                                    name="enable_sms_sending"
                                    <?= $settings['enable_sms_sending'] ? 'checked' : '' ?>
                                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded mt-1"
                                    title="Włącz możliwość oznaczania wysyłki SMS do pacjentów">
                                <div class="ml-3 flex-1">
                                    <label for="enable_sms_sending" class="block text-sm font-medium text-slate-700">
                                        Status wysyłki SMS
                                    </label>
                                    <p class="text-xs text-slate-500 mt-1">
                                        Włącz możliwość oznaczania, czy informacja SMS została wysłana do pacjenta dotycząca jego wizyty.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="pt-4 border-t border-slate-200">
                            <button type="submit"
                                class="w-full px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors flex items-center justify-center"
                                title="Zapisz ustawienia statusów wizyt">
                                <span class="material-icons-outlined mr-2">save</span>
                                Zapisz ustawienia statusów
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Ustawienia importu -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
                <div class="bg-blue-50 border-b border-blue-200 px-6 py-4">
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-blue-600 mr-3">import_export</span>
                        <h2 class="text-lg font-semibold text-blue-900">Ustawienia importu</h2>
                    </div>
                    <p class="text-blue-700 text-sm mt-1">Integracja z systemami zewnętrznymi</p>
                </div>
                
                <div class="p-6">
                    <form method="POST" class="space-y-4">
                        <input type="hidden" name="action" value="update_import_settings">

                        <div class="space-y-4">
                            <div class="flex items-start p-3 border border-slate-200 rounded-lg">
                                <input type="checkbox"
                                    id="import_enabled"
                                    name="import_enabled"
                                    <?= $settings['import_enabled'] ? 'checked' : '' ?>
                                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded mt-1"
                                    title="Włącz automatyzację importu danych z systemów zewnętrznych">
                                <div class="ml-3 flex-1">
                                    <label for="import_enabled" class="block text-sm font-medium text-slate-700">
                                        Import danych z systemów zewnętrznych
                                    </label>
                                    <p class="text-xs text-slate-500 mt-1">
                                        Włącz automatyczny import harmonogramów i danych pacjentów z zewnętrznych systemów medycznych.
                                    </p>
                                </div>
                            </div>
                            
                            <div>
                                <label for="import_source" class="block text-sm font-medium text-slate-700 mb-2">
                                    Źródło importu
                                </label>
                                <select id="import_source"
                                        name="import_source"
                                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                        title="Wybierz system zewnętrzny do importu danych">
                                    <?php foreach ($settings['import_sources'] as $key => $label): ?>
                                        <option value="<?= htmlspecialchars($key) ?>"
                                                <?= $settings['import_source'] === $key ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($label) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <p class="mt-1 text-xs text-slate-500">
                                    Wybierz system, z którego będą importowane dane. Import zostanie odrzucony jeśli lekarze nie zostaną prawidłowo powiązani.
                                </p>
                            </div>
                        </div>

                        <div class="pt-4 border-t border-slate-200">
                            <button type="submit"
                                class="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center justify-center"
                                title="Zapisz ustawienia importu danych">
                                <span class="material-icons-outlined mr-2">save</span>
                                Zapisz ustawienia importu
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Zarządzanie użytkownikami -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
                <div class="bg-amber-50 border-b border-amber-200 px-6 py-4">
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-amber-600 mr-3">people</span>
                        <h2 class="text-lg font-semibold text-amber-900">Zarządzanie użytkownikami</h2>
                    </div>
                    <p class="text-amber-700 text-sm mt-1">Dodawaj i zarządzaj kontami użytkowników systemu</p>
                </div>
                
                <div class="p-6">
                    <!-- Lista użytkowników -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-md font-medium text-slate-900">Lista użytkowników</h3>
                            <button onclick="showAddUserModal()" class="px-3 py-1.5 bg-amber-600 text-white text-sm rounded-lg hover:bg-amber-700 transition-colors flex items-center" title="Dodaj nowego użytkownika systemu">
                                <span class="material-icons-outlined mr-1 text-sm">add</span>
                                Dodaj użytkownika
                            </button>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm">
                                <thead>
                                    <tr class="border-b border-slate-200">
                                        <th class="text-left py-2 px-2 font-medium text-slate-700">Nazwa użytkownika</th>
                                        <th class="text-left py-2 px-2 font-medium text-slate-700">Email</th>
                                        <th class="text-left py-2 px-2 font-medium text-slate-700">Ostatnia aktywność</th>
                                        <th class="text-left py-2 px-2 font-medium text-slate-700">Utworzono</th>
                                        <th class="text-center py-2 px-2 font-medium text-slate-700">Akcje</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($users)): ?>
                                        <tr>
                                            <td colspan="5" class="text-center py-4 text-slate-500">
                                                Brak użytkowników w systemie
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($users as $userItem): ?>
                                            <tr class="border-b border-slate-100 hover:bg-slate-50">
                                                <td class="py-3 px-2">
                                                    <div class="font-medium text-slate-900"><?= htmlspecialchars($userItem['username']) ?></div>
                                                </td>
                                                <td class="py-3 px-2">
                                                    <div class="text-slate-700"><?= htmlspecialchars($userItem['email']) ?></div>
                                                </td>
                                                <td class="py-3 px-2">
                                                    <div class="text-slate-600 text-xs">
                                                        <?php
                                                        $userModel = new User();
                                                        echo $userModel->formatLastActivity($userItem['last_activity']);
                                                        ?>
                                                    </div>
                                                </td>
                                                <td class="py-3 px-2">
                                                    <div class="text-slate-600 text-xs"><?= formatDate($userItem['created_at']) ?></div>
                                                </td>
                                                <td class="py-3 px-2">
                                                    <div class="flex items-center justify-center space-x-1">
                                                        <button onclick="showEditUserModal(<?= $userItem['id'] ?>, '<?= htmlspecialchars($userItem['username']) ?>', '<?= htmlspecialchars($userItem['email']) ?>')"
                                                                class="p-1.5 text-blue-600 hover:bg-blue-50 rounded transition-colors"
                                                                title="Edytuj dane użytkownika">
                                                            <span class="material-icons-outlined text-sm">edit</span>
                                                        </button>
                                                        <?php if (count($users) > 1): ?>
                                                        <form method="POST" style="display: inline;"
                                                              onsubmit="return confirm('Czy na pewno chcesz usunąć tego użytkownika?')">
                                                            <input type="hidden" name="action" value="delete_user">
                                                            <input type="hidden" name="user_id" value="<?= $userItem['id'] ?>">
                                                            <button type="submit"
                                                                    class="p-1.5 text-red-600 hover:bg-red-50 rounded transition-colors"
                                                                    title="Usuń użytkownika z systemu">
                                                                <span class="material-icons-outlined text-sm">delete</span>
                                                            </button>
                                                        </form>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Prawa kolumna - Informacje i akcje -->
        <div class="w-full lg:w-96 space-y-6">
            <!-- Informacje o systemie -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
                <div class="bg-blue-50 border-b border-blue-200 px-6 py-4">
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-blue-600 mr-3">info</span>
                        <h2 class="text-lg font-semibold text-blue-900">Informacje o systemie</h2>
                    </div>
                    <p class="text-blue-700 text-sm mt-1">Podsumowanie działania systemu</p>
                </div>
                
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm text-slate-600">Wersja systemu</span>
                            <span class="text-sm font-medium text-slate-900 bg-slate-100 px-2 py-1 rounded"><?= $settings['system_version'] ?></span>
                        </div>

                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm text-slate-600">Rozmiar bazy</span>
                            <span class="text-sm font-medium text-slate-900"><?= $settings['database_size'] ?></span>
                        </div>

                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm text-slate-600">Liczba wizyt</span>
                            <span class="text-sm font-medium text-slate-900"><?= number_format($settings['total_appointments']) ?></span>
                        </div>

                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm text-slate-600">Aktywni lekarze</span>
                            <span class="text-sm font-medium text-slate-900"><?= $settings['total_doctors'] ?></span>
                        </div>

                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm text-slate-600">Aktywne wyświetlacze</span>
                            <span class="text-sm font-medium text-slate-900 bg-green-100 text-green-800 px-2 py-1 rounded"><?= $settings['active_displays'] ?></span>
                        </div>

                        <div class="flex justify-between items-center py-2 border-b border-slate-100">
                            <span class="text-sm text-slate-600">Wyświetlenia video</span>
                            <span class="text-sm font-medium text-slate-900 bg-blue-100 text-blue-800 px-2 py-1 rounded"><?= number_format($settings['total_video_views']) ?></span>
                        </div>

                        <div class="pt-3">
                            <h3 class="text-sm font-medium text-slate-700 mb-2">Statystyki materiałów video</h3>
                            <div class="grid grid-cols-3 gap-2 text-center">
                                <div class="bg-green-50 rounded-lg p-2">
                                    <div class="text-lg font-semibold text-green-700"><?= number_format($settings['video_stats']['approved']) ?></div>
                                    <div class="text-xs text-green-600">Zaakceptowane</div>
                                </div>
                                <div class="bg-amber-50 rounded-lg p-2">
                                    <div class="text-lg font-semibold text-amber-700"><?= number_format($settings['video_stats']['pending']) ?></div>
                                    <div class="text-xs text-amber-600">Oczekujące</div>
                                </div>
                                <div class="bg-red-50 rounded-lg p-2">
                                    <div class="text-lg font-semibold text-red-700"><?= number_format($settings['video_stats']['rejected']) ?></div>
                                    <div class="text-xs text-red-600">Odrzucone</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Modal dodawania użytkownika -->
            <div id="addUserModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
                <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
                    <div class="px-6 py-4 border-b border-slate-200">
                        <h3 class="text-lg font-semibold text-slate-900">Dodaj nowego użytkownika</h3>
                    </div>
                    <form method="POST" class="px-6 py-4">
                        <input type="hidden" name="action" value="create_user">
                        
                        <div class="space-y-4">
                            <div>
                                <label for="new_username" class="block text-sm font-medium text-slate-700 mb-1">
                                    Nazwa użytkownika
                                </label>
                                <input type="text"
                                       id="new_username"
                                       name="username"
                                       required
                                       minlength="3"
                                       maxlength="50"
                                       pattern="[a-zA-Z0-9_]+"
                                       class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                                       title="Wprowadź nazwę użytkownika (litery, cyfry i podkreślniki, min. 3 znaki)">
                                <p class="text-xs text-slate-500 mt-1">Tylko litery, cyfry i podkreślniki (min. 3 znaki)</p>
                            </div>
                            
                            <div>
                                <label for="new_email" class="block text-sm font-medium text-slate-700 mb-1">
                                    Email
                                </label>
                                <input type="email"
                                       id="new_email"
                                       name="email"
                                       required
                                       maxlength="100"
                                       class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                                       title="Wprowadź adres email użytkownika">
                            </div>
                            
                            <div>
                                <label for="new_password" class="block text-sm font-medium text-slate-700 mb-1">
                                    Hasło
                                </label>
                                <input type="password"
                                       id="new_password"
                                       name="password"
                                       required
                                       minlength="8"
                                       class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                                       title="Wprowadź hasło (minimum 8 znaków)">
                                <p class="text-xs text-slate-500 mt-1">Minimum 8 znaków</p>
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-3 mt-6">
                            <button type="button"
                                    onclick="hideAddUserModal()"
                                    class="px-4 py-2 text-slate-700 bg-slate-100 rounded-lg hover:bg-slate-200 transition-colors"
                                    title="Anuluj dodawanie użytkownika">
                                Anuluj
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors"
                                    title="Dodaj nowego użytkownika do systemu">
                                Dodaj użytkownika
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Modal edycji użytkownika -->
            <div id="editUserModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
                <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
                    <div class="px-6 py-4 border-b border-slate-200">
                        <h3 class="text-lg font-semibold text-slate-900">Edytuj użytkownika</h3>
                    </div>
                    <form method="POST" class="px-6 py-4">
                        <input type="hidden" name="action" value="update_user">
                        <input type="hidden" id="edit_user_id" name="user_id">
                        
                        <div class="space-y-4">
                            <div>
                                <label for="edit_username" class="block text-sm font-medium text-slate-700 mb-1">
                                    Nazwa użytkownika
                                </label>
                                <input type="text"
                                       id="edit_username"
                                       name="username"
                                       required
                                       minlength="3"
                                       maxlength="50"
                                       pattern="[a-zA-Z0-9_]+"
                                       class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                                       title="Wprowadź nazwę użytkownika (litery, cyfry i podkreślniki, min. 3 znaki)">
                                <p class="text-xs text-slate-500 mt-1">Tylko litery, cyfry i podkreślniki (min. 3 znaki)</p>
                            </div>
                            
                            <div>
                                <label for="edit_email" class="block text-sm font-medium text-slate-700 mb-1">
                                    Email
                                </label>
                                <input type="email"
                                       id="edit_email"
                                       name="email"
                                       required
                                       maxlength="100"
                                       class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                                       title="Wprowadź adres email użytkownika">
                            </div>
                            
                            <div>
                                <label for="edit_password" class="block text-sm font-medium text-slate-700 mb-1">
                                    Nowe hasło <span class="text-slate-400">(opcjonalnie)</span>
                                </label>
                                <input type="password"
                                       id="edit_password"
                                       name="password"
                                       minlength="8"
                                       placeholder="Pozostaw puste aby nie zmieniać"
                                       class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                                       title="Wprowadź nowe hasło (minimum 8 znaków) lub pozostaw puste">
                                <p class="text-xs text-slate-500 mt-1">Minimum 8 znaków (pozostaw puste aby nie zmieniać)</p>
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-3 mt-6">
                            <button type="button"
                                    onclick="hideEditUserModal()"
                                    class="px-4 py-2 text-slate-700 bg-slate-100 rounded-lg hover:bg-slate-200 transition-colors"
                                    title="Anuluj edycję użytkownika">
                                Anuluj
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors"
                                    title="Zapisz zmiany w danych użytkownika">
                                Zapisz zmiany
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <script>
            // Funkcje do obsługi modalnych okien
            function showAddUserModal() {
                document.getElementById('addUserModal').classList.remove('hidden');
                document.getElementById('addUserModal').classList.add('flex');
                document.getElementById('new_username').focus();
            }
            
            function hideAddUserModal() {
                document.getElementById('addUserModal').classList.add('hidden');
                document.getElementById('addUserModal').classList.remove('flex');
                document.getElementById('new_username').value = '';
                document.getElementById('new_email').value = '';
                document.getElementById('new_password').value = '';
            }
            
            function showEditUserModal(userId, username, email) {
                document.getElementById('edit_user_id').value = userId;
                document.getElementById('edit_username').value = username;
                document.getElementById('edit_email').value = email;
                document.getElementById('edit_password').value = '';
                
                document.getElementById('editUserModal').classList.remove('hidden');
                document.getElementById('editUserModal').classList.add('flex');
                document.getElementById('edit_username').focus();
            }
            
            function hideEditUserModal() {
                document.getElementById('editUserModal').classList.add('hidden');
                document.getElementById('editUserModal').classList.remove('flex');
            }
            
            // Zamknij modale przy kliknięciu poza zawartością
            document.getElementById('addUserModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideAddUserModal();
                }
            });
            
            document.getElementById('editUserModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideEditUserModal();
                }
            });
            
            // Zamknij modale przy klawiszu Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    hideAddUserModal();
                    hideEditUserModal();
                }
            });
            </script>

            <!-- Akcje systemowe -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
                <div class="bg-amber-50 border-b border-amber-200 px-6 py-4">
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-amber-600 mr-3">build</span>
                        <h2 class="text-lg font-semibold text-amber-900">Akcje systemowe</h2>
                    </div>
                    <p class="text-amber-700 text-sm mt-1">Zarządzanie systemem</p>
                </div>
                
                <div class="p-6">
                    <div class="space-y-3">
                        <form method="POST" class="w-full">
                            <input type="hidden" name="action" value="clear_cache">
                            <button type="submit"
                                class="w-full px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors flex items-center justify-center"
                                title="Wyczyść pamięć podręczną systemu">
                                <span class="material-icons-outlined mr-2">refresh</span>
                                Wyczyść cache
                            </button>
                        </form>

                        <a href="/admin/pulpit"
                            class="block w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center justify-center"
                            title="Powrót do pulpitu głównego">
                            <span class="material-icons-outlined mr-2">dashboard</span>
                            Powrót do pulpitu
                        </a>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>