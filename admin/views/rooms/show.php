<!-- Szczegóły gabinetu -->
<div class="px-6 py-4">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <a href="/admin/gabinety" class="text-gray-500 hover:text-gray-700">
                        <span class="material-icons-outlined">meeting_room</span>
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-gray-400">chevron_right</span>
                        <span class="ml-2 text-sm font-medium text-gray-500"><?= htmlspecialchars($room['name']) ?></span>
                    </div>
                </li>
            </ol>
        </nav>
        <div class="flex justify-between items-center mt-2">
            <div>
                <h1 class="text-2xl font-bold text-slate-900"><?= htmlspecialchars($room['name']) ?></h1>
                <p class="text-slate-600 mt-1">Szczegóły gabinetu</p>
            </div>
            <div class="flex space-x-3">
                <a href="/admin/gabinety/edit/<?= $room['id'] ?>" 
                   class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2">
                    <span class="material-icons-outlined">edit</span>
                    Edytuj
                </a>
            </div>
        </div>
    </div>

    <!-- Komunikaty -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                <span class="text-green-800"><?= htmlspecialchars($_SESSION['success']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-red-600 mr-2">error</span>
                <span class="text-red-800"><?= htmlspecialchars($_SESSION['error']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Główne informacje -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Informacje podstawowe -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                <h2 class="text-lg font-semibold text-slate-900 mb-4">Informacje podstawowe</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-sm font-medium text-slate-700 mb-2">Nazwa gabinetu</h3>
                        <p class="text-slate-900"><?= htmlspecialchars($room['name']) ?></p>
                    </div>
                    
                    <div>
                        <h3 class="text-sm font-medium text-slate-700 mb-2">ID gabinetu</h3>
                        <p class="text-slate-900"><?= $room['id'] ?></p>
                    </div>
                    
                    <div class="md:col-span-2">
                        <h3 class="text-sm font-medium text-slate-700 mb-2">Opis</h3>
                        <p class="text-slate-900">
                            <?= !empty($room['description']) ? htmlspecialchars($room['description']) : '<span class="text-slate-500">Brak opisu</span>' ?>
                        </p>
                    </div>
                    
                    <div>
                        <h3 class="text-sm font-medium text-slate-700 mb-2">Data utworzenia</h3>
                        <p class="text-slate-900"><?= formatDate($room['created_at']) ?></p>
                    </div>
                    
                    <?php if (!empty($room['updated_at'])): ?>
                    <div>
                        <h3 class="text-sm font-medium text-slate-700 mb-2">Ostatnia aktualizacja</h3>
                        <p class="text-slate-900"><?= formatDate($room['updated_at']) ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Lekarze w gabinecie -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-slate-900">Lekarze w gabinecie</h2>
                    <span class="px-2 py-1 bg-indigo-100 text-indigo-800 text-xs font-medium rounded-full">
                        <?= count($doctors) ?>
                    </span>
                </div>
                
                <?php if (empty($doctors)): ?>
                    <div class="text-center py-8">
                        <span class="material-icons-outlined text-4xl text-slate-400 mb-2">person_off</span>
                        <h3 class="text-lg font-medium text-slate-900 mb-1">Brak lekarzy</h3>
                        <p class="text-slate-600 mb-4">Brak lekarzy przypisanych do tego gabinetu</p>
                        <a href="/admin/lekarze" class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                            <span class="material-icons-outlined mr-2">person_add</span>
                            Zarządzaj lekarzami
                        </a>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($doctors as $doctor): ?>
                            <div class="flex items-center justify-between p-4 border border-slate-200 rounded-lg hover:bg-slate-50">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center overflow-hidden">
                                        <img src="<?= getDoctorPhotoUrl($doctor['photo_url']) ?>"
                                             alt="<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>"
                                             class="w-12 h-12 rounded-full object-cover"
                                             onerror="this.onerror=null; this.src='<?= DOCTORS_PHOTOS_URL ?>/default-avatar.webp'; this.setAttribute('data-fallback', 'true');">
                                    </div>
                                    <div>
                                        <div class="font-medium text-slate-900">
                                            <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                        </div>
                                        <?php if (!empty($doctor['specialization'])): ?>
                                            <div class="text-sm text-slate-600">
                                                <?= htmlspecialchars($doctor['specialization']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                        Aktywny
                                    </span>
                                    <a href="/admin/lekarze/edit/<?= $doctor['id'] ?>" 
                                       class="text-indigo-600 hover:text-indigo-800" 
                                       title="Edytuj lekarza">
                                        <span class="material-icons-outlined">edit</span>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Panel boczny -->
        <div class="space-y-6">
            <!-- Statystyki -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                <h2 class="text-lg font-semibold text-slate-900 mb-4">Statystyki dzisiaj</h2>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="material-icons-outlined text-slate-400 mr-2">event_available</span>
                            <span class="text-sm text-slate-600">Wizyty dzisiaj</span>
                        </div>
                        <span class="text-lg font-semibold text-slate-900"><?= $stats['appointments_today'] ?></span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                            <span class="text-sm text-slate-600">Zakończone</span>
                        </div>
                        <span class="text-lg font-semibold text-green-600"><?= $stats['completed_today'] ?></span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="material-icons-outlined text-blue-600 mr-2">hourglass_top</span>
                            <span class="text-sm text-slate-600">Oczekujące</span>
                        </div>
                        <span class="text-lg font-semibold text-blue-600"><?= $stats['waiting_today'] ?></span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="material-icons-outlined text-indigo-600 mr-2">people</span>
                            <span class="text-sm text-slate-600">Lekarze</span>
                        </div>
                        <span class="text-lg font-semibold text-indigo-600"><?= $stats['doctors_count'] ?></span>
                    </div>
                </div>
            </div>

            <!-- Akcje -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                <h2 class="text-lg font-semibold text-slate-900 mb-4">Akcje</h2>
                
                <div class="space-y-3">
                    <a href="/admin/gabinety/edit/<?= $room['id'] ?>" 
                       class="block w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors text-center">
                        <span class="material-icons-outlined text-sm mr-1">edit</span>
                        Edytuj gabinet
                    </a>
                    
                    <a href="/admin/lekarze" 
                       class="block w-full px-4 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors text-center">
                        <span class="material-icons-outlined text-sm mr-1">people</span>
                        Zarządzaj lekarzami
                    </a>
                    
                    <button onclick="confirmDelete(<?= $room['id'] ?>, '<?= htmlspecialchars($room['name']) ?>')" 
                            class="w-full px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-center">
                        <span class="material-icons-outlined text-sm mr-1">delete</span>
                        Usuń gabinet
                    </button>
                    
                    <a href="/admin/gabinety" 
                       class="block w-full px-4 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors text-center">
                        <span class="material-icons-outlined text-sm mr-1">list</span>
                        Powrót do listy
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal potwierdzenia usunięcia -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-lg bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <span class="material-icons-outlined text-red-600">warning</span>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900">Usuń gabinet</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Czy na pewno chcesz usunąć gabinet "<span id="roomName"></span>"? 
                    Ta operacja jest nieodwracalna.
                </p>
            </div>
            <div class="flex justify-center gap-4 mt-4">
                <button onclick="closeDeleteModal()" 
                        class="px-4 py-2 bg-gray-300 text-gray-800 rounded-lg hover:bg-gray-400 transition-colors">
                    Anuluj
                </button>
                <a id="confirmDeleteBtn" href="" 
                   class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    Usuń
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(roomId, roomName) {
    document.getElementById('roomName').textContent = roomName;
    document.getElementById('confirmDeleteBtn').href = '/admin/gabinety/delete/' + roomId;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// Zamknij modal klikając poza nim
window.onclick = function(event) {
    const modal = document.getElementById('deleteModal');
    if (event.target == modal) {
        closeDeleteModal();
    }
}

// Automatyczne odświeżanie statystyk co 30 sekund
setInterval(function() {
    fetch('/api/room-stats?room_id=<?= $room['id'] ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.stats) {
                // Można tutaj zaktualizować statystyki na stronie
                // na razie tylko logujemy do konsoli
                console.log('Zaktualizowane statystyki:', data.stats);
            }
        })
        .catch(error => {
            console.error('Błąd pobierania statystyk:', error);
        });
}, 30000);
</script>