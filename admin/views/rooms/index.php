<!-- <PERSON><PERSON> gabinetów -->
<div class="px-6 py-4">
    <!-- Header -->
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-slate-900">Gabinety</h1>
            <p class="text-slate-600 mt-1">Zarządzanie gabinetami medycznymi</p>
        </div>
        <a href="/admin/gabinety/create"
           class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2"
           title="Dodaj nowy gabinet do systemu">
            <span class="material-icons-outlined">add</span>
            Dodaj gabinet
        </a>
    </div>

    <!-- Instrukcja obsługi gabinetów -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg mb-6">
        <div class="p-4 border-b border-blue-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-blue-600 mr-3">info</span>
                    <h2 class="text-lg font-semibold text-blue-900">Instrukcja obsługi gabinetów</h2>
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800 transition-colors" onclick="toggleInstruction('rooms-instruction')">
                    <span id="rooms-instruction-toggle" class="material-icons-outlined">expand_less</span>
                </button>
            </div>
        </div>
        <div id="rooms-instruction" class="p-6">
            <div>
                <div class="space-y-3 text-sm text-blue-800">
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Co to są gabinety?</h3>
                        <p>Gabinety to fizyczne pomieszczenia, w których przyjmują lekarze. Każdy gabinet może mieć przypisanych wielu lekarzy, co pozwala na organizację pracy przychodni.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Jak zarządzać gabinetami?</h3>
                        <ol class="list-decimal list-inside ml-4 mt-1 space-y-1">
                            <li><strong>Dodaj gabinet:</strong> Kliknij przycisk "Dodaj gabinet", aby utworzyć nowe pomieszczenie.</li>
                            <li><strong>Edytuj dane:</strong> Zmień nazwę lub opis gabinetu poprzez ikonę edycji.</li>
                            <li><strong>Przeglądaj szczegóły:</strong> Zobacz przypisanych lekarzy i harmonogram pracy.</li>
                            <li><strong>Usuń gabinet:</strong> Usuń niepotrzebne gabinety (tylko jeśli nie mają przypisanych lekarzy).</li>
                        </ol>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Przypisywanie lekarzy</h3>
                        <p>Każdy gabinet może mieć przypisanych wielu lekarzy. Przez szczegóły gabinetu możesz zarządzać listą lekarzy i ich harmonogramem pracy w danym pomieszczeniu.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Wskazówki</h3>
                        <ul class="list-disc list-inside ml-4 mt-1">
                            <li>Każdy gabinet powinien mieć unikalną nazwę dla łatwej identyfikacji</li>
                            <li>Opis gabinetu może zawierać informacje o lokalizacji lub wyposażeniu</li>
                            <li>Usunięcie gabinetu jest możliwe tylko wtedy, gdy nie ma przypisanych lekarzy</li>
                            <li>Statystyki pokazują liczbę przypisanych lekarzy do każdego gabinetu</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-4 pt-3 border-t border-blue-200">
                    <p class="text-xs text-blue-600">
                        <strong>Uwaga:</strong> Gabinety są wykorzystywane w systemie wizyt i harmonogramów pracy. Upewnij się, że dane są aktualne.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Często zadawane pytania -->
    <div class="bg-amber-50 border border-amber-200 rounded-lg mb-6">
        <div class="p-4 border-b border-amber-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-amber-600 mr-3">help_outline</span>
                    <h2 class="text-lg font-semibold text-amber-900">Często zadawane pytania (FAQ)</h2>
                </div>
                <button type="button" class="text-amber-600 hover:text-amber-800 transition-colors" onclick="toggleInstruction('rooms-faq-section')">
                    <span id="rooms-faq-section-toggle" class="material-icons-outlined">expand_less</span>
                </button>
            </div>
        </div>
        <div id="rooms-faq-section" class="p-6">
            <div>
                <div class="space-y-3 text-sm text-amber-800">
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Czy mogę przypisać jednego lekarza do wielu gabinetów?</h3>
                        <p>Tak, jeden lekarz może być przypisany do wielu gabinetów. Jest to przydatne w przypadku lekarzy specjalistów, którzy przyjmują w różnych lokalizacjach.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Co się stanie po usunięciu gabinetu?</h3>
                        <p>System nie pozwoli usunąć gabinetu, który ma przypisanych lekarzy. Musisz najpierw usunąć powiązania z lekarzami przed usunięciem gabinetu.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Jak zmienić kolejność gabinetów?</h3>
                        <p>Kolejność gabinetów jest automatyczna i alfabetyczna. Aby zmienić kolejność, możesz dodać prefiksy numeryczne do nazw gabinetów.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Czy gabinety wpływają na system wizyt?</h3>
                        <p>Nie, gabinety są wykorzystywane do określania miejsca przyjęcia pacjenta. Lekarz moe wybrać gabinet, w którym chce przyjmować pacjentów. Nazwa gabinetu jest pokazywana na wyświetlaczu w przychodni.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Komunikaty -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                <span class="text-green-800"><?= htmlspecialchars($_SESSION['success']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-red-600 mr-2">error</span>
                <span class="text-red-800"><?= htmlspecialchars($_SESSION['error']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <!-- Lista gabinetów -->
    <div class="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
        <?php if (empty($rooms)): ?>
            <div class="p-8 text-center">
                <span class="material-icons-outlined text-4xl text-slate-400 mb-2">meeting_room</span>
                <h3 class="text-lg font-medium text-slate-900 mb-1">Brak gabinetów</h3>
                <p class="text-slate-600 mb-4">Nie dodano jeszcze żadnych gabinetów</p>
                <a href="/admin/gabinety/create"
                   class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                   title="Dodaj pierwszy gabinet do systemu">
                    <span class="material-icons-outlined mr-2">add</span>
                    Dodaj pierwszy gabinet
                </a>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-slate-50 border-b border-slate-200">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Nazwa gabinetu
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Opis
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Lekarze
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Utworzony
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Akcje
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-slate-200">
                        <?php foreach ($rooms as $room): ?>
                            <tr class="hover:bg-slate-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <span class="material-icons-outlined text-slate-400 mr-3">meeting_room</span>
                                        <div>
                                            <div class="text-sm font-medium text-slate-900">
                                                <?= htmlspecialchars($room['name']) ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-slate-600">
                                        <?= htmlspecialchars($room['description'] ?? 'Brak opisu') ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <span class="material-icons-outlined text-slate-400 mr-1">people</span>
                                        <span class="text-sm text-slate-900"><?= $room['doctors_count'] ?></span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-slate-600">
                                        <?= formatDate($room['created_at']) ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="/admin/gabinety/show/<?= $room['id'] ?>"
                                       class="text-indigo-600 hover:text-indigo-900 mr-3"
                                       title="Zobacz szczegółowe informacje o gabinecie">
                                        <span class="material-icons-outlined">visibility</span>
                                    </a>
                                    <a href="/admin/gabinety/edit/<?= $room['id'] ?>"
                                       class="text-indigo-600 hover:text-indigo-900 mr-3"
                                       title="Edytuj nazwę i opis gabinetu">
                                        <span class="material-icons-outlined">edit</span>
                                    </a>
                                    <button onclick="confirmDelete(<?= $room['id'] ?>, '<?= htmlspecialchars($room['name']) ?>')"
                                            class="text-red-600 hover:text-red-900"
                                            title="Usuń gabinet (możliwe tylko jeśli nie ma przypisanych lekarzy)">
                                        <span class="material-icons-outlined">delete</span>
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal potwierdzenia usunięcia -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-lg bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <span class="material-icons-outlined text-red-600">warning</span>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900">Usuń gabinet</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Czy na pewno chcesz usunąć gabinet "<span id="roomName"></span>"? 
                    Ta operacja jest nieodwracalna.
                </p>
            </div>
            <div class="flex justify-center gap-4 mt-4">
                <button onclick="closeDeleteModal()"
                        class="px-4 py-2 bg-gray-300 text-gray-800 rounded-lg hover:bg-gray-400 transition-colors"
                        title="Anuluj usuwanie gabinetu">
                    Anuluj
                </button>
                <a id="confirmDeleteBtn" href=""
                   class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                   title="Potwierdź usunięcie gabinetu (operacja nieodwracalna)">
                    Usuń
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(roomId, roomName) {
    document.getElementById('roomName').textContent = roomName;
    document.getElementById('confirmDeleteBtn').href = '/admin/gabinety/delete/' + roomId;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// Zamknij modal klikając poza nim
window.onclick = function(event) {
    const modal = document.getElementById('deleteModal');
    if (event.target == modal) {
        closeDeleteModal();
    }
}

// Funkcja do zwijania/rozwijania sekcji instrukcji
function toggleInstruction(sectionId) {
    const section = document.getElementById(sectionId);
    const toggleIcon = document.getElementById(sectionId + '-toggle');
    
    if (section.style.display === 'none') {
        section.style.display = 'block';
        toggleIcon.textContent = 'expand_less';
        // Zapisz stan w localStorage
        localStorage.setItem(sectionId + '_collapsed', 'false');
    } else {
        section.style.display = 'none';
        toggleIcon.textContent = 'expand_more';
        // Zapisz stan w localStorage
        localStorage.setItem(sectionId + '_collapsed', 'true');
    }
}

// Przywróć stan sekcji z localStorage przy ładowaniu strony
document.addEventListener('DOMContentLoaded', function() {
    const sections = ['rooms-instruction', 'rooms-faq-section'];
    
    sections.forEach(function(sectionId) {
        const isCollapsed = localStorage.getItem(sectionId + '_collapsed') === 'true';
        const section = document.getElementById(sectionId);
        const toggleIcon = document.getElementById(sectionId + '-toggle');
        
        if (isCollapsed && section && toggleIcon) {
            section.style.display = 'none';
            toggleIcon.textContent = 'expand_more';
        }
    });
});
</script>