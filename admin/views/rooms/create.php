<!-- Dodawanie gabinetu -->
<div class="px-6 py-4">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <a href="/admin/gabinety" class="text-gray-500 hover:text-gray-700">
                        <span class="material-icons-outlined">meeting_room</span>
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-gray-400">chevron_right</span>
                        <span class="ml-2 text-sm font-medium text-gray-500">Dodaj gabinet</span>
                    </div>
                </li>
            </ol>
        </nav>
        <h1 class="text-2xl font-bold text-slate-900 mt-2">Dodaj nowy gabinet</h1>
        <p class="text-slate-600 mt-1">Wypełnij formularz aby dodać nowy gabinet</p>
    </div>

    <!-- Komunikaty -->
    <?php if (isset($_SESSION['error'])): ?>
        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-red-600 mr-2">error</span>
                <span class="text-red-800"><?= htmlspecialchars($_SESSION['error']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <!-- Formularz -->
    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
        <form method="POST" action="/admin/gabinety/store" class="space-y-6">
            <!-- Nazwa gabinetu -->
            <div>
                <label for="name" class="block text-sm font-medium text-slate-700 mb-2">
                    Nazwa gabinetu <span class="text-red-500">*</span>
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       required
                       maxlength="100"
                       class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                       placeholder="np. Gabinet 1, Gabinet diagnostyczny">
                <p class="mt-1 text-sm text-slate-500">
                    Maksymalnie 100 znaków
                </p>
            </div>

            <!-- Opis -->
            <div>
                <label for="description" class="block text-sm font-medium text-slate-700 mb-2">
                    Opis
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="4"
                          maxlength="500"
                          class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                          placeholder="Opis gabinetu, przeznaczenie, wyposażenie itp."></textarea>
                <p class="mt-1 text-sm text-slate-500">
                    Opcjonalny opis gabinetu (maksymalnie 500 znaków)
                </p>
            </div>

            <!-- Przyciski -->
            <div class="flex justify-end space-x-4 pt-4 border-t border-slate-200">
                <a href="/admin/gabinety" 
                   class="px-4 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors">
                    Anuluj
                </a>
                <button type="submit" 
                        class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2">
                    <span class="material-icons-outlined">save</span>
                    Zapisz gabinet
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Walidacja formularza po stronie klienta
document.querySelector('form').addEventListener('submit', function(e) {
    const nameInput = document.getElementById('name');
    const name = nameInput.value.trim();
    
    if (!name) {
        e.preventDefault();
        alert('Nazwa gabinetu jest wymagana');
        nameInput.focus();
        return false;
    }
    
    if (name.length < 2) {
        e.preventDefault();
        alert('Nazwa gabinetu musi mieć co najmniej 2 znaki');
        nameInput.focus();
        return false;
    }
    
    if (name.length > 100) {
        e.preventDefault();
        alert('Nazwa gabinetu nie może przekraczać 100 znaków');
        nameInput.focus();
        return false;
    }
    
    const description = document.getElementById('description').value;
    if (description.length > 500) {
        e.preventDefault();
        alert('Opis nie może przekraczać 500 znaków');
        document.getElementById('description').focus();
        return false;
    }
});

// Licznik znaków dla opisu
const descriptionTextarea = document.getElementById('description');
const charCount = document.createElement('div');
charCount.className = 'mt-1 text-sm text-slate-500';
charCount.textContent = '0/500 znaków';
descriptionTextarea.parentNode.appendChild(charCount);

descriptionTextarea.addEventListener('input', function() {
    const length = this.value.length;
    charCount.textContent = `${length}/500 znaków`;
    
    if (length > 500) {
        charCount.classList.add('text-red-500');
        charCount.classList.remove('text-slate-500');
    } else {
        charCount.classList.remove('text-red-500');
        charCount.classList.add('text-slate-500');
    }
});
</script>