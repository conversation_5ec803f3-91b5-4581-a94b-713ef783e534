<!-- <PERSON><PERSON><PERSON><PERSON> gabinetu -->
<div class="px-6 py-4">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <a href="/admin/gabinety" class="text-gray-500 hover:text-gray-700">
                        <span class="material-icons-outlined">meeting_room</span>
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-gray-400">chevron_right</span>
                        <a href="/admin/gabinety/show/<?= $room['id'] ?>" class="ml-2 text-sm font-medium text-gray-500 hover:text-gray-700">
                            <?= htmlspecialchars($room['name']) ?>
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-gray-400">chevron_right</span>
                        <span class="ml-2 text-sm font-medium text-gray-500">Edytuj</span>
                    </div>
                </li>
            </ol>
        </nav>
        <h1 class="text-2xl font-bold text-slate-900 mt-2">Edytuj gabinet</h1>
        <p class="text-slate-600 mt-1">Zmień dane gabinetu</p>
    </div>

    <!-- Komunikaty -->
    <?php if (isset($_SESSION['error'])): ?>
        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-red-600 mr-2">error</span>
                <span class="text-red-800"><?= htmlspecialchars($_SESSION['error']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Formularz -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                <form method="POST" action="/admin/gabinety/update/<?= $room['id'] ?>" class="space-y-6">
                    <!-- Nazwa gabinetu -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-slate-700 mb-2">
                            Nazwa gabinetu <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               required
                               maxlength="100"
                               value="<?= htmlspecialchars($room['name']) ?>"
                               class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <p class="mt-1 text-sm text-slate-500">
                            Maksymalnie 100 znaków
                        </p>
                    </div>

                    <!-- Opis -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-slate-700 mb-2">
                            Opis
                        </label>
                        <textarea id="description" 
                                  name="description" 
                                  rows="4"
                                  maxlength="500"
                                  class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"><?= htmlspecialchars($room['description'] ?? '') ?></textarea>
                        <p class="mt-1 text-sm text-slate-500">
                            Opcjonalny opis gabinetu (maksymalnie 500 znaków)
                        </p>
                    </div>

                    <!-- Przyciski -->
                    <div class="flex justify-end space-x-4 pt-4 border-t border-slate-200">
                        <a href="/admin/gabinety/show/<?= $room['id'] ?>" 
                           class="px-4 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors">
                            Anuluj
                        </a>
                        <button type="submit" 
                                class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center gap-2">
                            <span class="material-icons-outlined">save</span>
                            Zapisz zmiany
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Panel boczny -->
        <div class="space-y-6">
            <!-- Informacje o gabinecie -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                <h2 class="text-lg font-semibold text-slate-900 mb-4">Informacje o gabinecie</h2>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-slate-600">ID gabinetu:</span>
                        <span class="text-sm font-medium text-slate-900"><?= $room['id'] ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-sm text-slate-600">Lekarze w gabinecie:</span>
                        <span class="text-sm font-medium text-slate-900"><?= $room['doctors_count'] ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-sm text-slate-600">Utworzony:</span>
                        <span class="text-sm font-medium text-slate-900"><?= formatDate($room['created_at']) ?></span>
                    </div>
                    
                    <?php if (!empty($room['updated_at'])): ?>
                    <div class="flex justify-between">
                        <span class="text-sm text-slate-600">Zaktualizowany:</span>
                        <span class="text-sm font-medium text-slate-900"><?= formatDate($room['updated_at']) ?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Lekarze w gabinecie -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                <h2 class="text-lg font-semibold text-slate-900 mb-4">Lekarze w gabinecie</h2>
                
                <?php if (empty($doctors)): ?>
                    <p class="text-sm text-slate-600">Brak lekarzy przypisanych do tego gabinetu</p>
                <?php else: ?>
                    <div class="space-y-3">
                        <?php foreach ($doctors as $doctor): ?>
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                    <span class="material-icons-outlined text-indigo-600 text-sm">person</span>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-slate-900">
                                        <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                    </div>
                                    <?php if (!empty($doctor['specialization'])): ?>
                                        <div class="text-xs text-slate-600">
                                            <?= htmlspecialchars($doctor['specialization']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <div class="mt-4 pt-4 border-t border-slate-200">
                    <a href="/admin/lekarze" class="text-sm text-indigo-600 hover:text-indigo-800">
                        Zarządzaj lekarzami →
                    </a>
                </div>
            </div>

            <!-- Akcje -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                <h2 class="text-lg font-semibold text-slate-900 mb-4">Akcje</h2>
                
                <div class="space-y-3">
                    <a href="/admin/gabinety/show/<?= $room['id'] ?>" 
                       class="block w-full px-4 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors text-center">
                        <span class="material-icons-outlined text-sm mr-1">visibility</span>
                        Szczegóły gabinetu
                    </a>
                    
                    <a href="/admin/gabinety" 
                       class="block w-full px-4 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition-colors text-center">
                        <span class="material-icons-outlined text-sm mr-1">list</span>
                        Powrót do listy
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Walidacja formularza po stronie klienta
document.querySelector('form').addEventListener('submit', function(e) {
    const nameInput = document.getElementById('name');
    const name = nameInput.value.trim();
    
    if (!name) {
        e.preventDefault();
        alert('Nazwa gabinetu jest wymagana');
        nameInput.focus();
        return false;
    }
    
    if (name.length < 2) {
        e.preventDefault();
        alert('Nazwa gabinetu musi mieć co najmniej 2 znaki');
        nameInput.focus();
        return false;
    }
    
    if (name.length > 100) {
        e.preventDefault();
        alert('Nazwa gabinetu nie może przekraczać 100 znaków');
        nameInput.focus();
        return false;
    }
    
    const description = document.getElementById('description').value;
    if (description.length > 500) {
        e.preventDefault();
        alert('Opis nie może przekraczać 500 znaków');
        document.getElementById('description').focus();
        return false;
    }
});

// Licznik znaków dla opisu
const descriptionTextarea = document.getElementById('description');
const charCount = document.createElement('div');
charCount.className = 'mt-1 text-sm text-slate-500';
charCount.textContent = descriptionTextarea.value.length + '/500 znaków';
descriptionTextarea.parentNode.appendChild(charCount);

descriptionTextarea.addEventListener('input', function() {
    const length = this.value.length;
    charCount.textContent = `${length}/500 znaków`;
    
    if (length > 500) {
        charCount.classList.add('text-red-500');
        charCount.classList.remove('text-slate-500');
    } else {
        charCount.classList.remove('text-red-500');
        charCount.classList.add('text-slate-500');
    }
});
</script>