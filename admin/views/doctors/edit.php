<!-- <PERSON><PERSON><PERSON><PERSON> lekarza -->
<div class="px-6 py-4">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-slate-900">Ed<PERSON><PERSON><PERSON> lekarza</h1>
            <p class="text-slate-600 mt-1"><PERSON><PERSON><PERSON> dane lekarza</p>
        </div>

        <!-- Komunikaty -->
        <?php if (isset($error)): ?>
            <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-red-600 mr-2">error</span>
                    <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                    <span class="text-green-800"><?= htmlspecialchars($success) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <!-- Formularz -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <form method="POST" action="/admin/lekarze/update/<?= $doctor['id'] ?>" enctype="multipart/form-data" class="space-y-6">
                <!-- Zdjęcie -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">
                        Zdjęcie lekarza
                    </label>
                    <div class="flex items-center space-x-4">
                        <img id="photo-preview" src="<?= getDoctorPhotoUrl($doctor['photo_url']) ?>"
                            alt="<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>"
                            class="w-16 h-16 rounded-full object-cover"
                            onerror="this.onerror=null; this.src='<?= DOCTORS_PHOTOS_URL ?>/default-avatar.webp'; this.setAttribute('data-fallback', 'true');">
                        <div class="flex-1">
                            <div class="mb-3">
                                <label for="photo_upload" class="block text-sm font-medium text-slate-700 mb-2">
                                    Prześlij zdjęcie
                                </label>
                                <div class="relative">
                                    <input type="file"
                                        id="photo_upload"
                                        name="photo_upload"
                                        accept="image/*"
                                        onchange="previewPhoto(this)"
                                        class="sr-only">
                                    <button type="button"
                                        onclick="document.getElementById('photo_upload').click()"
                                        class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors inline-flex items-center" title="Wybierz nowe zdjęcie lekarza (JPG, PNG, GIF, WebP, max 5MB)">
                                        <span class="material-icons-outlined text-sm mr-2">cloud_upload</span>
                                        <span id="button-text">Wybierz plik</span>
                                    </button>
                                </div>
                                <div id="file-info" class="mt-2 text-sm text-slate-600 hidden">
                                    <p class="flex items-center">
                                        <span class="material-icons-outlined text-xs mr-1">check_circle</span>
                                        <span id="file-name"></span>
                                        <button type="button" onclick="clearPhoto()" class="ml-2 text-red-600 hover:text-red-800" title="Usuń wybrane zdjęcie i przywróć poprzednie">
                                            <span class="material-icons-outlined text-sm">close</span>
                                        </button>
                                    </p>
                                </div>
                                <p class="text-xs text-slate-500 mt-2">Dozwolone formaty: JPG, PNG, GIF, WebP. Maksymalny rozmiar: 5MB</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Imię -->
                <div>
                    <label for="first_name" class="block text-sm font-medium text-slate-700 mb-2">
                        Imię *
                    </label>
                    <input type="text"
                        id="first_name"
                        name="first_name"
                        value="<?= htmlspecialchars($doctor['first_name']) ?>"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        title="Wprowadź imię lekarza (pole wymagane)"
                        required>
                </div>

                <!-- Nazwisko -->
                <div>
                    <label for="last_name" class="block text-sm font-medium text-slate-700 mb-2">
                        Nazwisko *
                    </label>
                    <input type="text"
                        id="last_name"
                        name="last_name"
                        value="<?= htmlspecialchars($doctor['last_name']) ?>"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        title="Wprowadź nazwisko lekarza (pole wymagane)"
                        required>
                </div>

                <!-- Specjalizacja -->
                <div>
                    <label for="specialization" class="block text-sm font-medium text-slate-700 mb-2">
                        Specjalizacja
                    </label>
                    <input type="text"
                        id="specialization"
                        name="specialization"
                        value="<?= htmlspecialchars($doctor['specialization']) ?>"
                        placeholder="np. Kardiolog, Dermatolog"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        title="Wprowadź specjalizację lekarza">
                </div>

                <!-- Domyślny gabinet -->
                <div>
                    <label for="default_room_id" class="block text-sm font-medium text-slate-700 mb-2">
                        Domyślny gabinet
                    </label>
                    <select id="default_room_id"
                        name="default_room_id"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        title="Wybierz domyślny gabinet, w którym przyjmuje lekarz">
                        <option value="">-- Brak przypisanego gabinetu --</option>
                        <?php foreach ($rooms as $room): ?>
                            <option value="<?= $room['id'] ?>"
                                <?= ($doctor['default_room_id'] == $room['id']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($room['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-xs text-slate-500 mt-2">Wybierz domyślny gabinet, w którym przyjmuje lekarz</p>
                </div>

                <!-- Przypisany wyświetlacz -->
                <div>
                    <label for="display_id" class="block text-sm font-medium text-slate-700 mb-2">
                        Przypisany wyświetlacz
                    </label>
                    <select id="display_id"
                        name="display_id"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        title="Wybierz wyświetlacz, na którym będą wyświetlane informacje o lekarzu">
                        <option value="">-- Wyświetlaj na wszystkich --</option>
                        <?php foreach ($displays as $display): ?>
                            <option value="<?= $display['id'] ?>"
                                <?= ($doctor['display_id'] == $display['id']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($display['display_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-xs text-slate-500 mt-2">Wybierz wyświetlacz, na którym będą wyświetlane informacje o lekarzu</p>
                </div>


                <!-- Dane logowania -->
                <div class="bg-blue-50 rounded-lg p-4">
                    <h3 class="font-medium text-slate-900 mb-3">Dane logowania</h3>
                    <div class="space-y-4">
                        <div>
                            <label for="login" class="block text-sm font-medium text-slate-700 mb-2">
                                Login
                            </label>
                            <input type="text"
                                id="login"
                                name="login"
                                value="<?= htmlspecialchars($doctor['login'] ?? '') ?>"
                                placeholder="np. <EMAIL>"
                                class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                title="Login używany do logowania w aplikacji mobilnej PWA">
                            <p class="text-xs text-slate-500 mt-1">Login używany do logowania w aplikacji mobilnej</p>
                        </div>
                        <div>
                            <span class="text-slate-600">Hasło:</span>
                            <span class="ml-2">••••••••</span>
                            <button type="button"
                                    onclick="showPasswordChangeModal(<?= $doctor['id'] ?>)"
                                    class="ml-2 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                                    title="Zmień hasło logowania do aplikacji PWA">
                                Zmień hasło
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Informacje dodatkowe -->
                <div class="bg-slate-50 rounded-lg p-4">
                    <h3 class="font-medium text-slate-900 mb-2">Informacje</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-slate-600">ID lekarza:</span>
                            <span class="ml-2 font-medium"><?= $doctor['id'] ?></span>
                        </div>
                        <div>
                            <span class="text-slate-600">Status:</span>
                            <span class="ml-2">
                                <?php if ($doctor['active']): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Aktywny
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Nieaktywny
                                    </span>
                                <?php endif; ?>
                            </span>
                        </div>
                        <div>
                            <span class="text-slate-600">Domyślny gabinet:</span>
                            <span class="ml-2">
                                <?php if (!empty($doctor['default_room_name'])): ?>
                                    <span class="font-medium"><?= htmlspecialchars($doctor['default_room_name']) ?></span>
                                <?php else: ?>
                                    <span class="text-slate-400">Nie przypisano</span>
                                <?php endif; ?>
                            </span>
                        </div>
                        <div>
                            <span class="text-slate-600">Przypisany wyświetlacz:</span>
                            <span class="ml-2">
                                <?php if (!empty($doctor['display_name'])): ?>
                                    <span class="font-medium"><?= htmlspecialchars($doctor['display_name']) ?></span>
                                <?php else: ?>
                                    <span class="text-slate-400">Nie przypisano</span>
                                <?php endif; ?>
                            </span>
                        </div>
                        <div>
                            <span class="text-slate-600">Utworzony:</span>
                            <span class="ml-2"><?= formatDateTime($doctor['created_at']) ?></span>
                        </div>
                        <?php if (isset($doctor['updated_at']) && $doctor['updated_at']): ?>
                            <div>
                                <span class="text-slate-600">Zaktualizowany:</span>
                                <span class="ml-2"><?= formatDateTime($doctor['updated_at']) ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Przyciski -->
                <div class="flex items-center justify-between pt-6 border-t border-slate-200">
                    <a href="/admin/lekarze"
                        class="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors" title="Powrót do listy lekarzy bez zapisywania zmian">
                        ← Powrót do listy lekarzy
                    </a>

                    <div class="flex gap-3">
                        <button type="button"
                            onclick="deleteDoctor(<?= $doctor['id'] ?>, '<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>')"
                            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors" title="Usuń lekarza z systemu wraz z wszystkimi danymi (operacja nieodwracalna)">
                            <span class="material-icons-outlined text-sm mr-1">delete</span>
                            Usuń lekarza
                        </button>

                        <button type="submit"
                            class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors" title="Zapisz wszystkie wprowadzone zmiany w danych lekarza">
                            <span class="material-icons-outlined text-sm mr-1">save</span>
                            Zapisz zmiany
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function deleteDoctor(doctorId, doctorName) {
        if (confirm('Czy na pewno chcesz usunąć lekarza "' + doctorName + '"? Ta operacja jest nieodwracalna.')) {
            window.location.href = '/admin/lekarze/delete/' + doctorId;
        }
    }

    // Podgląd zdjęcia z pliku
    function previewPhoto(input) {
        if (input.files && input.files[0]) {
            const file = input.files[0];
            const reader = new FileReader();

            reader.onload = function(e) {
                document.getElementById('photo-preview').src = e.target.result;
                
                // Aktualizuj informacje o pliku
                const fileInfo = document.getElementById('file-info');
                const fileName = document.getElementById('file-name');
                const buttonText = document.getElementById('button-text');
                
                fileName.textContent = file.name;
                buttonText.textContent = 'Zmień plik';
                fileInfo.classList.remove('hidden');
            }

            reader.readAsDataURL(file);
        }
    }
    
    // Wyczyść zdjęcie
    function clearPhoto() {
        const input = document.getElementById('photo_upload');
        const preview = document.getElementById('photo-preview');
        const fileInfo = document.getElementById('file-info');
        const buttonText = document.getElementById('button-text');
        
        // Wyczyść input
        input.value = '';
        
        // Przywróć oryginalne zdjęcie
        const originalSrc = '<?= getDoctorPhotoUrl($doctor['photo_url']) ?>';
        preview.src = originalSrc;
        preview.onerror = function() {
            this.onerror = null;
            this.src = '<?= DOCTORS_PHOTOS_URL ?>/default-avatar.webp';
            this.setAttribute('data-fallback', 'true');
        };
        
        // Ukryj informacje o pliku
        fileInfo.classList.add('hidden');
        
        // Przywróć domyślny tekst przycisku
        buttonText.textContent = 'Wybierz plik';
    }
    
    // Modal zmiany hasła
    function showPasswordChangeModal(doctorId) {
        // Utwórz modal jeśli nie istnieje
        if (!document.getElementById('password-change-modal')) {
            const modal = document.createElement('div');
            modal.id = 'password-change-modal';
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
            modal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Zmień hasło lekarza</h3>
                        <form id="password-change-form" class="space-y-4">
                            <div>
                                <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">
                                    Nowe hasło
                                </label>
                                <input type="password"
                                       id="new_password"
                                       name="new_password"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required
                                       minlength="8">
                               <p class="text-xs text-gray-500 mt-1">Minimum 8 znaków</p>
                            </div>
                            <div>
                                <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                                    Potwierdź hasło
                                </label>
                                <input type="password"
                                       id="confirm_password"
                                       name="confirm_password"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       required
                                       minlength="8">
                            </div>
                            <div class="flex justify-end space-x-3 pt-4">
                                <button type="button"
                                        onclick="closePasswordChangeModal()"
                                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                                    Anuluj
                                </button>
                                <button type="submit"
                                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                    Zmień hasło
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            
            // Dodaj event listener do formularza
            document.getElementById('password-change-form').addEventListener('submit', function(e) {
                e.preventDefault();
                changeDoctorPassword(doctorId);
            });
        } else {
            // Ustaw ID lekarza w istniejącym modalu
            document.getElementById('password-change-form').setAttribute('data-doctor-id', doctorId);
        }
        
        // Pokaż modal
        document.getElementById('password-change-modal').style.display = 'block';
    }
    
    function closePasswordChangeModal() {
        document.getElementById('password-change-modal').style.display = 'none';
        document.getElementById('password-change-form').reset();
    }
    
    changeDoctorPassword = function(doctorId) {
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        
        if (newPassword !== confirmPassword) {
            alert('Hasła nie są identyczne');
            return;
        }
        
        if (newPassword.length < 8) {
            alert('Hasło musi mieć co najmniej 8 znaków');
            return;
        }
        
        const formData = new FormData();
        formData.append('new_password', newPassword);
        
        fetch('/admin/lekarze/change-password?id=' + doctorId, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Hasło zostało zmienione pomyślnie');
                closePasswordChangeModal();
            } else {
                alert('Błąd: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Wystąpił błąd podczas zmiany hasła');
        });
    };
    
    // Sprawdź unikalność loginu
    function checkLoginUniqueness(login) {
        if (!login) return;
        
        const doctorId = <?= $doctor['id'] ?>;
        
        fetch('/admin/lekarze/check-login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                login: login,
                doctor_id: doctorId
            })
        })
        .then(response => response.json())
        .then(data => {
            const loginInput = document.getElementById('login');
            const loginHelp = document.getElementById('login-help');
            
            if (!loginHelp) {
                const helpDiv = document.createElement('div');
                helpDiv.id = 'login-help';
                helpDiv.className = 'text-xs mt-1';
                loginInput.parentNode.appendChild(helpDiv);
            }
            
            const helpElement = document.getElementById('login-help');
            
            if (data.unique) {
                loginInput.classList.remove('border-red-500');
                loginInput.classList.add('border-green-500');
                helpElement.textContent = 'Login jest dostępny';
                helpElement.className = 'text-xs mt-1 text-green-600';
            } else {
                loginInput.classList.remove('border-green-500');
                loginInput.classList.add('border-red-500');
                helpElement.textContent = 'Login jest już zajęty';
                helpElement.className = 'text-xs mt-1 text-red-600';
            }
        })
        .catch(error => {
            console.error('Error checking login uniqueness:', error);
        });
    }
    
    // Dodaj nasłuchiwanie na zmianę loginu
    document.addEventListener('DOMContentLoaded', function() {
        const loginInput = document.getElementById('login');
        if (loginInput) {
            loginInput.addEventListener('blur', function() {
                checkLoginUniqueness(this.value);
            });
        }
    });
    
</script>