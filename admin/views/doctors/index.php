<!-- Zarządzanie lekarzami -->
<div class="px-6 py-4">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-slate-900">Zarządzanie lekarzami</h1>
            <p class="text-slate-600 mt-1">Dodawaj, edytuj i zarządzaj profilami lekarzy</p>
        </div>

        <div class="flex gap-3">
            <?php
            $importSettings = new ImportSettings();
            $importEnabled = $importSettings->isImportEnabled();
            ?>
            
            <?php if ($importEnabled): ?>
                <a href="/admin/lekarze/map-import"
                    class="flex items-center gap-2 px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors"
                    data-tooltip="Powiąż lekarzy z systemem zewnętrznym (np. iGabinet, drEryk)">
                    <span class="material-icons-outlined text-sm">link</span>
                    Powiąż lekarzy z importu
                </a>
            <?php else: ?>
                <div class="relative group">
                    <button disabled
                        class="flex items-center gap-2 px-4 py-2 bg-gray-400 text-white rounded-lg cursor-not-allowed opacity-60"
                        data-tooltip="Należy najpierw włączyć opcję importu w ustawieniach systemu">
                        <span class="material-icons-outlined text-sm">link</span>
                        Powiąż lekarzy z importu
                    </button>
                    <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none z-10">
                        Należy najpierw włączyć opcję importu w ustawieniach systemu
                        <div class="absolute top-full left-1/2 transform -translate-x-1/2 -mt-1">
                            <div class="border-4 border-transparent border-t-gray-800"></div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <a href="/admin/lekarze/create"
                class="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                data-tooltip="Dodaj nowego lekarza do systemu">
                <span class="material-icons-outlined text-sm">add</span>
                Dodaj lekarza
            </a>
        </div>
    </div>

    <!-- Instrukcja obsługi lekarzy -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg mb-6">
        <div class="p-4 border-b border-blue-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-blue-600 mr-3">info</span>
                    <h2 class="text-lg font-semibold text-blue-900">Instrukcja obsługi lekarzy</h2>
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800 transition-colors" onclick="toggleInstruction('doctors-instruction')">
                    <span id="doctors-instruction-toggle" class="material-icons-outlined">expand_less</span>
                </button>
            </div>
        </div>
        <div id="doctors-instruction" class="p-6">
            <div>
                <div class="space-y-3 text-sm text-blue-800">
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Co to jest system lekarzy?</h3>
                        <p>System lekarzy pozwala na zarządzanie profilami medycznymi w przychodni. Każdy lekarz otrzymuje unikalny login do aplikacji PWA, która umożliwia zarządzanie kolejką pacjentów.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Jak zarządzać lekarzami?</h3>
                        <ol class="list-decimal list-inside ml-4 mt-1 space-y-1">
                            <li><strong>Dodaj lekarza:</strong> Kliknij przycisk "Dodaj lekarza", aby utworzyć nowy profil medyczny.</li>
                            <li><strong>Edytuj dane:</strong> Zmień dane osobowe, specjalizację lub przypisania przez ikonę edycji.</li>
                            <li><strong>Zarządzaj kolejką:</strong> Przejrzyj i zarządzaj kolejką pacjentów dla każdego lekarza.</li>
                            <li><strong>Aktywuj/dezaktywuj:</strong> Włącz lub wyłącz dostęp lekarza do systemu.</li>
                        </ol>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Aplikacja PWA dla lekarzy</h3>
                        <p>Każdy lekarz może uruchomić swoją aplikację PWA w przeglądarce Chrome pod adresem: <code class="bg-blue-100 px-2 py-1 rounded text-blue-800">https://<?=$_SERVER['HTTP_HOST']?>/lekarz</code>. Do logowania wymagany jest unikalny login PWA oraz hasło, które lekarz otrzymuje od administratora.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Wskazówki</h3>
                        <ul class="list-disc list-inside ml-4 mt-1">
                            <li>Każdy lekarz powinien mieć unikalny login PWA dla identyfikacji w systemie</li>
                            <li>Zdjęcie profilowe ułatwia identyfikację lekarza w systemie</li>
                            <li>Przypisanie gabinetu domyślnego pomaga w organizacji pracy</li>
                            <li>Status aktywny/nieaktywny kontroluje dostęp lekarza do aplikacji</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-4 pt-3 border-t border-blue-200">
                    <p class="text-xs text-blue-600">
                        <strong>Uwaga:</strong> Pamiętaj, aby każdemu lekarzowi przekazać jego login PWA. Jest to niezbędne do korzystania z systemu zarządzania kolejką.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Często zadawane pytania -->
    <div class="bg-amber-50 border border-amber-200 rounded-lg mb-6">
        <div class="p-4 border-b border-amber-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-amber-600 mr-3">help_outline</span>
                    <h2 class="text-lg font-semibold text-amber-900">Często zadawane pytania (FAQ)</h2>
                </div>
                <button type="button" class="text-amber-600 hover:text-amber-800 transition-colors" onclick="toggleInstruction('doctors-faq-section')">
                    <span id="doctors-faq-section-toggle" class="material-icons-outlined">expand_less</span>
                </button>
            </div>
        </div>
        <div id="doctors-faq-section" class="p-6">
            <div>
                <div class="space-y-3 text-sm text-amber-800">
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Jak lekarz loguje się do aplikacji PWA?</h3>
                        <p>Lekarz wpisuje swój unikalny login PWA oraz hasło w polach logowania na stronie <code class="bg-amber-100 px-2 py-1 rounded text-amber-800">https://<?=$_SERVER['HTTP_HOST']?>/lekarz</code>. Pozytywna weryfikacja danych umożliwia dostęp do systemu zarządzania kolejką.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Czy mogę zmienić login PWA lekarza?</h3>
                        <p>Tak, login PWA można zmienić w trybie edycji lekarza. Pamiętaj, że po zmianie loginu lekarz będzie musiał używać nowego identyfikatora.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Co się stanie po usunięciu lekarza?</h3>
                        <p>Usunięcie lekarza jest operacją nieodwracalną. Wszystkie dane powiązane z lekarzem (w tym historia kolejek) zostaną usunięte z systemu.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Jak działa import lekarzy z systemów zewnętrznych?</h3>
                        <p>Jeśli włączono import w ustawieniach, możesz powiązać lekarzy z systemów zewnętrznych (np. iGabinet, drEryk) z lekarzami w KtoOstatni przez opcję "Powiąż lekarzy z importu".</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pole wyszukiwania -->
    <?php if (!empty($doctors)): ?>
        <div class="mb-6">
            <div class="relative">
                <span class="material-icons-outlined absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">search</span>
                <input type="text" id="searchInput" placeholder="Wyszukaj lekarza po imieniu, nazwisku lub specjalizacji..."
                    class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                    data-tooltip="Wpisz fragment imienia, nazwiska lub specjalizacji, aby znaleźć lekarza">
            </div>
        </div>
    <?php endif; ?>

    <!-- Komunikaty -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                <span class="text-green-800"><?= htmlspecialchars($_SESSION['success']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-red-600 mr-2">error</span>
                <span class="text-red-800"><?= htmlspecialchars($_SESSION['error']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <!-- Lista lekarzy -->
    <?php if (empty($doctors)): ?>
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-8 text-center">
            <span class="material-icons-outlined text-slate-400 text-6xl mb-4">person_add</span>
            <h3 class="text-lg font-medium text-slate-900 mb-2">Brak lekarzy</h3>
            <p class="text-slate-600 mb-4">Dodaj pierwszego lekarza, aby rozpocząć zarządzanie kolejkami.</p>
            <a href="/admin/lekarze/create"
                class="inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                <span class="material-icons-outlined text-sm">add</span>
                Dodaj lekarza
            </a>
        </div>
    <?php else: ?>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="doctorsGrid">
            <?php foreach ($doctors as $doctor): ?>
                <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6 doctor-card"
                    data-name="<?= htmlspecialchars(strtolower($doctor['first_name'] . ' ' . $doctor['last_name'])) ?>"
                    data-specialization="<?= htmlspecialchars(strtolower($doctor['specialization'] ?? '')) ?>">
                    <!-- Zdjęcie i podstawowe info -->
                    <div class="flex items-center mb-4">
                        <img src="<?= getDoctorPhotoUrl($doctor['photo_url']) ?>"
                            alt="<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>"
                            class="w-12 h-12 rounded-full object-cover mr-3"
                            onerror="this.onerror=null; this.src='<?= DOCTORS_PHOTOS_URL ?>/default-avatar.webp'; this.setAttribute('data-fallback', 'true');">
                        <div class="flex-1 min-w-0">
                            <h3 class="font-semibold text-slate-900 truncate" title="<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>">
                                <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                            </h3>
                            <?php if ($doctor['specialization']): ?>
                                <p class="text-sm text-slate-600 truncate" title="<?= htmlspecialchars($doctor['specialization']) ?>">
                                    <?= htmlspecialchars($doctor['specialization']) ?>
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Informacje dodatkowe -->
                    <div class="space-y-2 mb-4">
                        <?php if ($doctor['login']): ?>
                            <div class="flex items-center text-sm text-slate-600 bg-blue-50 p-2 rounded-lg">
                                <span class="material-icons-outlined text-sm mr-2 text-blue-600">person</span>
                                <div class="flex-1">
                                    <div class="text-xs text-slate-500 mb-1">Login (PWA):</div>
                                    <div class="font-mono text-sm text-blue-700 break-all">
                                        <?= htmlspecialchars($doctor['login']) ?>
                                    </div>
                                </div>
                                <div class="flex gap-1">
                                    <button onclick="copyLogin('<?= htmlspecialchars($doctor['login']) ?>')"
                                        class="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded transition-colors"
                                        title="Kopiuj login PWA do schowka">
                                        <span class="material-icons-outlined text-sm">content_copy</span>
                                    </button>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="flex items-center text-sm text-amber-600 bg-amber-50 p-2 rounded-lg">
                                <span class="material-icons-outlined text-sm mr-2">warning</span>
                                <span>Brak loginu PWA</span>
                            </div>
                        <?php endif; ?>

                        <?php if ($doctor['default_room_name']): ?>
                            <div class="flex items-center text-sm text-slate-600">
                                <span class="material-icons-outlined text-sm mr-2">room</span>
                                <?= htmlspecialchars($doctor['default_room_name']) ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($doctor['display_name']): ?>
                            <div class="flex items-center text-sm text-slate-600">
                                <span class="material-icons-outlined text-sm mr-2">tv</span>
                                <?= htmlspecialchars($doctor['display_name']) ?>
                            </div>
                        <?php endif; ?>

                        <div class="flex items-center text-sm text-slate-600">
                            <span class="material-icons-outlined text-sm mr-2">schedule</span>
                            Utworzony: <?= formatDate($doctor['created_at']) ?>
                        </div>

                        <?php if (isset($doctor['updated_at']) && $doctor['updated_at']): ?>
                            <div class="flex items-center text-sm text-slate-600">
                                <span class="material-icons-outlined text-sm mr-2">update</span>
                                Zaktualizowany: <?= formatDate($doctor['updated_at']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Status -->
                    <div class="mb-4">
                        <?php if ($doctor['active']): ?>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <span class="w-1.5 h-1.5 bg-green-600 rounded-full mr-1"></span>
                                Aktywny
                            </span>
                        <?php else: ?>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <span class="w-1.5 h-1.5 bg-gray-600 rounded-full mr-1"></span>
                                Nieaktywny
                            </span>
                        <?php endif; ?>
                    </div>

                    <!-- Akcje -->
                    <div class="flex items-center justify-between pt-4 border-t border-slate-200">
                        <a href="/admin/kolejki?doctor_id=<?= $doctor['id'] ?>"
                            class="text-sm text-indigo-600 hover:text-indigo-800 transition-colors"
                            title="Przejdź do zarządzania kolejką tego lekarza">
                            Zobacz kolejkę
                        </a>

                        <div class="flex gap-2">
                            <a href="/admin/lekarze/edit/<?= $doctor['id'] ?>"
                                class="p-2 text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors"
                                title="Edytuj dane lekarza">
                                <span class="material-icons-outlined text-sm">edit</span>
                            </a>

                            <button onclick="deleteDoctor(<?= $doctor['id'] ?>, '<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>')"
                                class="p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-lg transition-colors"
                                title="Usuń lekarza z systemu (operacja nieodwracalna)">
                                <span class="material-icons-outlined text-sm">delete</span>
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<script>
    function deleteDoctor(doctorId, doctorName) {
        if (confirm('Czy na pewno chcesz usunąć lekarza "' + doctorName + '"? Ta operacja jest nieodwracalna.')) {
            window.location.href = '/admin/lekarze/delete/' + doctorId;
        }
    }

    function copyLogin(login) {
        navigator.clipboard.writeText(login).then(function() {
            // Pokaż komunikat o skopiowaniu
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-opacity';
            toast.textContent = 'Login skopiowany!';
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 2000);
        }).catch(function(err) {
            console.error('Błąd kopiowania: ', err);
            alert('Nie udało się skopiować loginu');
        });
    }

    // Funkcja wyszukiwania
    function filterDoctors() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const doctorCards = document.querySelectorAll('.doctor-card');
        let visibleCount = 0;

        doctorCards.forEach(card => {
            const name = card.dataset.name || '';
            const specialization = card.dataset.specialization || '';

            const isVisible = name.includes(searchTerm) || specialization.includes(searchTerm);

            if (isVisible) {
                card.style.display = 'block';
                visibleCount++;
            } else {
                card.style.display = 'none';
            }
        });

        // Pokaż komunikat jeśli brak wyników
        let noResultsMsg = document.getElementById('noResultsMessage');
        if (visibleCount === 0 && searchTerm.length > 0) {
            if (!noResultsMsg) {
                noResultsMsg = document.createElement('div');
                noResultsMsg.id = 'noResultsMessage';
                noResultsMsg.className = 'col-span-full text-center py-8 text-slate-500';
                noResultsMsg.innerHTML = `
                    <span class="material-icons-outlined text-4xl mb-2 block">search_off</span>
                    <p>Nie znaleziono lekarzy pasujących do wyszukiwania</p>
                `;
                document.getElementById('doctorsGrid').appendChild(noResultsMsg);
            }
            noResultsMsg.style.display = 'block';
        } else if (noResultsMsg) {
            noResultsMsg.style.display = 'none';
        }
    }

    // Dodaj nasłuchiwanie na pole wyszukiwania
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', filterDoctors);
            searchInput.addEventListener('keyup', filterDoctors);
        }
        
        // Przywróć stan sekcji instrukcji z localStorage
        const sections = ['doctors-instruction', 'doctors-faq-section'];
        
        sections.forEach(function(sectionId) {
            const isCollapsed = localStorage.getItem(sectionId + '_collapsed') === 'true';
            const section = document.getElementById(sectionId);
            const toggleIcon = document.getElementById(sectionId + '-toggle');
            
            if (isCollapsed && section && toggleIcon) {
                section.style.display = 'none';
                toggleIcon.textContent = 'expand_more';
            }
        });
    });

    // Funkcja do zwijania/rozwijania sekcji instrukcji
    function toggleInstruction(sectionId) {
        const section = document.getElementById(sectionId);
        const toggleIcon = document.getElementById(sectionId + '-toggle');
        
        if (section.style.display === 'none') {
            section.style.display = 'block';
            toggleIcon.textContent = 'expand_less';
            // Zapisz stan w localStorage
            localStorage.setItem(sectionId + '_collapsed', 'false');
        } else {
            section.style.display = 'none';
            toggleIcon.textContent = 'expand_more';
            // Zapisz stan w localStorage
            localStorage.setItem(sectionId + '_collapsed', 'true');
        }
    }
</script>