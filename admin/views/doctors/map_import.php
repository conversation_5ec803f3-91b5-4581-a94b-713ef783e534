<!-- Powiązywanie lekarzy z importu -->
<div class="px-6 py-4">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-slate-900">Powiąż lekarzy z importu</h1>
            <p class="text-slate-600 mt-1">Mapuj lekarzy z systemu zewnętrznego z lekarzami w bazie danych</p>
        </div>

        <a href="/admin/lekarze"
            class="flex items-center gap-2 px-4 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors"
            title="Powrót do listy lekarzy">
            <span class="material-icons-outlined text-sm">arrow_back</span>
            Powrót do lekarzy
        </a>
    </div>

    <!-- Instrukcja obsługi importu -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg mb-6">
        <div class="p-4 border-b border-blue-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-blue-600 mr-3">info</span>
                    <h2 class="text-lg font-semibold text-blue-900">Instrukcja obsługi importu lekarzy</h2>
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800 transition-colors" onclick="toggleInstruction('import-instruction')">
                    <span id="import-instruction-toggle" class="material-icons-outlined">expand_less</span>
                </button>
            </div>
        </div>
        <div id="import-instruction" class="p-6">
            <div>
                
                <div class="space-y-3 text-sm text-blue-800">
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Co to jest mapowanie lekarzy?</h3>
                        <p>Mapowanie lekarzy pozwala powiązać lekarzy z systemów zewnętrznych (np. iGabinet, drEryk) z lekarzami zapisanymi w bazie KtoOstatni. Jest to wymagane, aby import danych wizyt działał poprawnie.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Kiedy potrzebujesz mapowania?</h3>
                        <p>Mapowanie jest potrzebne, gdy:</p>
                        <ul class="list-disc list-inside ml-4 mt-1">
                            <li>Włączono import danych z systemu zewnętrznego w ustawieniach</li>
                            <li>System zewnętrzny próbuje importować dane dla lekarzy, którzy nie są jeszcze zmapowani</li>
                            <li>Otrzymujesz komunikat o błędzie informujący o niezmapowanych lekarzach</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Jak przeprowadzić mapowanie?</h3>
                        <ol class="list-decimal list-inside ml-4 mt-1 space-y-1">
                            <li><strong>Zidentyfikuj lekarzy do zmapowania:</strong> Poniżej znajduje się lista lekarzy z systemu zewnętrznego, którzy wymagają powiązania z lekarzami w KtoOstatni.</li>
                            <li><strong>Wybierz odpowiedniego lekarza:</strong> Dla każdego lekarza z systemu zewnętrznego wybierz odpowiedniego lekarza z listy rozwijanej po prawej stronie.</li>
                            <li><strong>Lub dodaj nowego lekarza:</strong> Jeśli lekarz nie istnieje w systemie, kliknij przycisk "Dodaj do systemu", aby automatycznie utworzyć nowego lekarza.</li>
                            <li><strong>Zapisz mapowania:</strong> Po wybraniu wszystkich powiązań kliknij przycisk "Zapisz mapowania".</li>
                            <li><strong>Powtórz import:</strong> Po zapisaniu mapowań, uruchom ponownie import danych w systemie zewnętrznym.</li>
                        </ol>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Wskazówki</h3>
                        <ul class="list-disc list-inside ml-4 mt-1">
                            <li>System automatycznie wyświetla tylko lekarzy, którzy wymagają mapowania</li>
                            <li>Plik z niezmapowanymi lekarzami jest aktualizowany automatycznie podczas każdej próby importu</li>
                            <li>Możesz usunąć istniejące mapowania, zaznaczając je w tabeli poniżej i klikając "Usuń mapowania"</li>
                            <li>Automatyczne dodawanie lekarza tworzy nową kartę lekarza z podstawowymi danymi i generuje unikalny kod dostępu</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-4 pt-3 border-t border-blue-200">
                    <p class="text-xs text-blue-600">
                        <strong>Uwaga:</strong> Mapowanie jest jednorazową operacją dla każdego lekarza. Po zmapowaniu lekarza, system będzie automatycznie importował jego dane w przyszłości.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Często zadawane pytania -->
    <div class="bg-amber-50 border border-amber-200 rounded-lg mb-6">
        <div class="p-4 border-b border-amber-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-amber-600 mr-3">help_outline</span>
                    <h2 class="text-lg font-semibold text-amber-900">Często zadawane pytania (FAQ)</h2>
                </div>
                <button type="button" class="text-amber-600 hover:text-amber-800 transition-colors" onclick="toggleInstruction('faq-section')">
                    <span id="faq-section-toggle" class="material-icons-outlined">expand_less</span>
                </button>
            </div>
        </div>
        <div id="faq-section" class="p-6">
            <div>
                <div class="space-y-3 text-sm text-amber-800">
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Co się stanie, jeśli nie zmapuję lekarza?</h3>
                        <p>Dane wizyt dla niezmapowanego lekarza nie zostaną zaimportowane. System będzie odrzucał import aż do momentu zmapowania wszystkich lekarzy z pliku.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Czy mogę zmapować jednego lekarza zewnętrznego z wieloma lekarzami systemowymi?</h3>
                        <p>Nie, każdy lekarz z systemu zewnętrznego może być powiązany tylko z jednym lekarzem w systemie KtoOstatni.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Jak często muszę przeprowadzać mapowanie?</h3>
                        <p>Mapowanie jest jednorazowe dla każdego lekarza. Jednak gdy dodajesz nowych lekarzy do systemu zewnętrznego, będziesz musiał zmapować ich również w systemie KtoOstatni.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Jakie dane osobowe są przekazywane do KtoOstatni?</h3>
                        <p>Podczas importu na serwery KtoOstatni przesyłane są tylko: imię, nazwisko (lekarza i pacjenta) oraz numer telefonu. Wszystkie pozostałe dane z pliku źródłowego są usuwane przed importem.</p>
                    </div>
                    

                </div>
            </div>
        </div>
    </div>

    <!-- Komunikaty -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                <span class="text-green-800"><?= htmlspecialchars($_SESSION['success']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-red-600 mr-2">error</span>
                <span class="text-red-800"><?= htmlspecialchars($_SESSION['error']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <?php if ($unmappedDoctorsData && !empty($unmappedDoctorsData['unmapped_doctors'])): ?>
        <!-- Formularz do szybkiego dodawania lekarza -->
        <form id="quickAddForm" method="POST" action="/admin/lekarze/quick-add" style="display: none;">
            <input type="hidden" name="external_id" id="quickAddExternalId">
        </form>
        
        <!-- Mapowanie lekarzy -->
        <form method="POST" action="/admin/lekarze/save-mappings">
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6 mb-6">
                <h2 class="text-lg font-semibold text-slate-900 mb-4">Mapowanie lekarzy</h2>
                
                <div class="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-amber-600 mr-2">warning</span>
                        <div>
                            <div class="text-sm font-medium text-amber-800">Znaleziono niezmapowanych lekarzy</div>
                            <div class="text-xs text-amber-600 mt-1">
                                Znaleziono <?= count($unmappedDoctorsData['unmapped_doctors']) ?> niezmapowanych lekarzy
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Nagłówki kolumn -->
                <div class="mb-4 border-b border-slate-200 pb-2">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h3 class="text-sm font-semibold text-slate-700">Lekarze w systemie zewnętrznym</h3>
                        </div>
                        <div class="flex-1 max-w-md">
                            <h3 class="text-sm font-semibold text-slate-700">Lekarze w KtoOstatni.pl</h3>
                        </div>
                    </div>
                </div>

                <div class="space-y-4">
                    <?php foreach ($unmappedDoctorsData['unmapped_doctors'] as $doctor): ?>
                        <div class="border border-slate-200 rounded-lg p-4">
                            <div class="flex items-center justify-between gap-4">
                                <div class="flex-1">
                                    <div class="font-medium text-slate-900"><?= htmlspecialchars($doctor['name']) ?></div>
                                    <div class="text-sm text-slate-600">ID: <?= htmlspecialchars($doctor['external_id']) ?></div>
                                    <?php if (!empty($doctor['specialization'])): ?>
                                        <div class="text-sm text-slate-600">Specjalizacja: <?= htmlspecialchars($doctor['specialization']) ?></div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-1 max-w-md">
                                    <select name="mappings[<?= htmlspecialchars($doctor['external_id']) ?>]"
                                            class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="">-- Wybierz lekarza --</option>
                                        <?php if (!empty($systemDoctors)): ?>
                                            <?php foreach ($systemDoctors as $systemDoctor): ?>
                                                <option value="<?= htmlspecialchars($systemDoctor['id'] ?? '') ?>">
                                                    <?= htmlspecialchars($systemDoctor['name'] ?? '') ?>
                                                    <?php if (!empty($systemDoctor['specialization'])): ?>
                                                        - <?= htmlspecialchars($systemDoctor['specialization']) ?>
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <option value="" disabled>Brak lekarzy w systemie</option>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="flex-shrink-0">
                                    <button type="button"
                                            class="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                                            onclick="quickAddDoctor('<?= htmlspecialchars($doctor['external_id']) ?>', '<?= htmlspecialchars(addslashes($doctor['name'])) ?>')"
                                            title="Dodaj tego lekarza do systemu KtoOstatni">
                                        <span class="material-icons-outlined text-sm mr-1">person_add</span>
                                        Dodaj do systemu
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="mt-6 flex items-center justify-between">
                    <div class="text-sm text-slate-600">
                        Wybierz lekarzy z systemu, aby powiązać ich z lekarzami z importu
                    </div>
                    
                    <div class="space-x-3">
                        <button type="submit"
                                class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                                title="Zapisz powiązania lekarzy z systemu zewnętrznego z lekarzami w KtoOstatni">
                            <span class="material-icons-outlined text-sm mr-1">save</span>
                            Zapisz mapowania
                        </button>
                    </div>
                </div>
            </div>
        </form>
    <?php else: ?>
        <!-- Sekcja pusta -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-12 text-center mb-6">
            <span class="material-icons-outlined text-slate-400 text-6xl mb-4">folder_open</span>
            <h3 class="text-lg font-medium text-slate-900 mb-2">Brak niezmapowanych lekarzy</h3>
            <p class="text-slate-600 mb-4">Nie znaleziono żadnych niezmapowanych lekarzy.</p>
            <p class="text-sm text-slate-500">Plik jest aktualizowany automatycznie podczas importu, gdy system nie może powiązać lekarzy.</p>
        </div>
    <?php endif; ?>

    <!-- Aktualne mapowania -->
    <?php if (!empty($mappedDoctors)): ?>
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-slate-900">Aktualne mapowania</h2>
                <span class="text-sm text-slate-600"><?= count($mappedDoctors) ?> zmapowanych lekarzy</span>
            </div>

            <form method="POST" action="/admin/lekarze/remove-mappings" id="removeMappingsForm">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-slate-200">
                                <th class="text-left py-3 px-4 font-medium text-slate-700">
                                    <input type="checkbox" id="selectAll" class="rounded border-slate-300">
                                </th>
                                <th class="text-left py-3 px-4 font-medium text-slate-700">Lekarz systemowy</th>
                                <th class="text-left py-3 px-4 font-medium text-slate-700">Lekarz zewnętrzny</th>
                                <th class="text-left py-3 px-4 font-medium text-slate-700">ID zewnętrzny</th>
                                <th class="text-left py-3 px-4 font-medium text-slate-700">Ostatnia synchronizacja</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($mappedDoctors as $doctor): ?>
                                <tr class="border-b border-slate-100 hover:bg-slate-50">
                                    <td class="py-3 px-4">
                                        <input type="checkbox" name="doctor_ids[]" value="<?= $doctor['id'] ?>"
                                               class="doctor-checkbox rounded border-slate-300"
                                               title="Zaznacz lekarza do usunięcia mapowania">
                                    </td>
                                    <td class="py-3 px-4">
                                        <div class="font-medium text-slate-900">
                                            <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                        </div>
                                        <?php if (!empty($doctor['specialization'])): ?>
                                            <div class="text-sm text-slate-600"><?= htmlspecialchars($doctor['specialization']) ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="py-3 px-4">
                                        <div class="text-slate-900">
                                            <?= htmlspecialchars($doctor['external_doctor_name'] ?? 'Brak nazwy') ?>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <?= htmlspecialchars($doctor['external_doctor_id']) ?>
                                        </span>
                                    </td>
                                    <td class="py-3 px-4 text-sm text-slate-600">
                                        <?php if ($doctor['external_last_sync']): ?>
                                            <?= date('d.m.Y H:i', strtotime($doctor['external_last_sync'])) ?>
                                        <?php else: ?>
                                            <span class="text-slate-400">Brak danych</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="mt-4 flex items-center justify-between">
                    <div class="text-sm text-slate-600">
                        <span id="selectedCount">0</span> wybranych lekarzy
                    </div>
                    <button type="submit" id="removeButton"
                            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:bg-slate-300 disabled:cursor-not-allowed"
                            disabled
                            onclick="return confirm('Czy na pewno chcesz usunąć mapowania dla wybranych lekarzy?')"
                            title="Usuń wybrane mapowania lekarzy (operacja nieodwracalna)">
                        <span class="material-icons-outlined text-sm mr-1">delete</span>
                        Usuń mapowania
                    </button>
                </div>
            </form>
        </div>
    <?php else: ?>
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <div class="text-center py-8">
                <span class="material-icons-outlined text-slate-400 text-6xl mb-4">link_off</span>
                <h3 class="text-lg font-medium text-slate-900 mb-2">Brak mapowań</h3>
                <p class="text-slate-600">Żaden lekarz nie został jeszcze zmapowany z systemem zewnętrznym.</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
// Funkcja do szybkiego dodawania lekarza
function quickAddDoctor(externalId, doctorName) {
    if (confirm('Czy na pewno chcesz dodać tego lekarza do systemu?\n\nLekarz: ' + doctorName)) {
        document.getElementById('quickAddExternalId').value = externalId;
        document.getElementById('quickAddForm').submit();
    }
}

// Minimalny JavaScript tylko dla interakcji UI
document.addEventListener('DOMContentLoaded', function() {
    // Funkcje do pokazywania komunikatów (jeśli potrzebne)
    function showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        successDiv.textContent = message;
        document.body.appendChild(successDiv);
        
        setTimeout(() => {
            successDiv.style.opacity = '0';
            setTimeout(() => document.body.removeChild(successDiv), 300);
        }, 3000);
    }

    function showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);

        setTimeout(() => {
            errorDiv.style.opacity = '0';
            setTimeout(() => document.body.removeChild(errorDiv), 300);
        }, 3000);
    }

    // Obsługa checkboxów dla usuwania mapowań
    const selectAllCheckbox = document.getElementById('selectAll');
    const doctorCheckboxes = document.querySelectorAll('.doctor-checkbox');
    const selectedCountSpan = document.getElementById('selectedCount');
    const removeButton = document.getElementById('removeButton');

    if (selectAllCheckbox && doctorCheckboxes.length > 0) {
        // Zaznacz/odznacz wszystkie
        selectAllCheckbox.addEventListener('change', function() {
            doctorCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            
            updateSelectedCount();
        });

        // Obsługa pojedynczych checkboxów
        doctorCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateSelectedCount();

                // Aktualizuj stan "zaznacz wszystkie"
                const checkedCount = document.querySelectorAll('.doctor-checkbox:checked').length;
                selectAllCheckbox.checked = checkedCount === doctorCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < doctorCheckboxes.length;
            });
        });

        function updateSelectedCount() {
            const checkedCount = document.querySelectorAll('.doctor-checkbox:checked').length;
            if (selectedCountSpan) {
                selectedCountSpan.textContent = checkedCount;
            }
            if (removeButton) {
                removeButton.disabled = checkedCount === 0;
            }
        }

        // Inicjalna aktualizacja
        updateSelectedCount();
    }
});

// Funkcja do zwijania/rozwijania sekcji instrukcji
function toggleInstruction(sectionId) {
    const section = document.getElementById(sectionId);
    const toggleIcon = document.getElementById(sectionId + '-toggle');
    
    if (section.style.display === 'none') {
        section.style.display = 'block';
        toggleIcon.textContent = 'expand_less';
        // Zapisz stan w localStorage
        localStorage.setItem(sectionId + '_collapsed', 'false');
    } else {
        section.style.display = 'none';
        toggleIcon.textContent = 'expand_more';
        // Zapisz stan w localStorage
        localStorage.setItem(sectionId + '_collapsed', 'true');
    }
}

// Przywróć stan sekcji z localStorage przy ładowaniu strony
document.addEventListener('DOMContentLoaded', function() {
    const sections = ['import-instruction', 'faq-section'];
    
    sections.forEach(function(sectionId) {
        const isCollapsed = localStorage.getItem(sectionId + '_collapsed') === 'true';
        const section = document.getElementById(sectionId);
        const toggleIcon = document.getElementById(sectionId + '-toggle');
        
        if (isCollapsed && section && toggleIcon) {
            section.style.display = 'none';
            toggleIcon.textContent = 'expand_more';
        }
    });
});
</script>