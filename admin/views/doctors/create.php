<!-- Doda<PERSON>ie lekarza -->
<div class="px-6 py-4">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-slate-900">Dodaj nowego lekarza</h1>
            <p class="text-slate-600 mt-1">Wprowadź dane nowego lekarza do systemu</p>
        </div>

        <!-- Komunikaty -->
        <?php if (isset($_SESSION['error'])): ?>
            <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-red-600 mr-2">error</span>
                    <span class="text-red-800"><?= htmlspecialchars($_SESSION['error']) ?></span>
                </div>
            </div>
            <?php unset($_SESSION['error']); ?>
        <?php endif; ?>

        <!-- Formularz -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <form method="POST" action="/admin/lekarze/store" enctype="multipart/form-data" class="space-y-6">
                <!-- Zdjęcie -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">
                        Zdjęcie lekarza
                    </label>
                    <div class="flex items-center space-x-4">
                        <div id="photo-preview" class="w-16 h-16 bg-slate-200 rounded-full flex items-center justify-center overflow-hidden">
                            <span class="material-icons-outlined text-slate-500 text-2xl">person</span>
                        </div>
                        <div class="flex-1">
                            <div class="mb-3">
                                <label for="photo_upload" class="block text-sm font-medium text-slate-700 mb-2">
                                    Prześlij zdjęcie
                                </label>
                                <div class="relative">
                                    <input type="file"
                                        id="photo_upload"
                                        name="photo_upload"
                                        accept="image/*"
                                        onchange="previewPhoto(this)"
                                        class="sr-only">
                                    <button type="button"
                                        onclick="document.getElementById('photo_upload').click()"
                                        class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors inline-flex items-center" title="Wybierz plik zdjęcia z komputera (JPG, PNG, GIF, WebP, max 5MB)">
                                        <span class="material-icons-outlined text-sm mr-2">cloud_upload</span>
                                        <span id="button-text">Wybierz plik</span>
                                    </button>
                                </div>
                                <div id="file-info" class="mt-2 text-sm text-slate-600 hidden">
                                    <p class="flex items-center">
                                        <span class="material-icons-outlined text-xs mr-1">check_circle</span>
                                        <span id="file-name"></span>
                                        <button type="button" onclick="clearPhoto()" class="ml-2 text-red-600 hover:text-red-800" title="Usuń wybrane zdjęcie">
                                            <span class="material-icons-outlined text-sm">close</span>
                                        </button>
                                    </p>
                                </div>
                                <p class="text-xs text-slate-500 mt-2">Dozwolone formaty: JPG, PNG, GIF, WebP. Maksymalny rozmiar: 5MB</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Imię -->
                <div>
                    <label for="first_name" class="block text-sm font-medium text-slate-700 mb-2">
                        Imię *
                    </label>
                    <input type="text"
                        id="first_name"
                        name="first_name"
                        value="<?= htmlspecialchars($_POST['first_name'] ?? '') ?>"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        title="Wprowadź imię lekarza (pole wymagane)"
                        required>
                </div>

                <!-- Nazwisko -->
                <div>
                    <label for="last_name" class="block text-sm font-medium text-slate-700 mb-2">
                        Nazwisko *
                    </label>
                    <input type="text"
                        id="last_name"
                        name="last_name"
                        value="<?= htmlspecialchars($_POST['last_name'] ?? '') ?>"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        title="Wprowadź nazwisko lekarza (pole wymagane)"
                        required>
                </div>

                <!-- Specjalizacja -->
                <div>
                    <label for="specialization" class="block text-sm font-medium text-slate-700 mb-2">
                        Specjalizacja (opcjonalne)
                    </label>
                    <input type="text"
                        id="specialization"
                        name="specialization"
                        value="<?= htmlspecialchars($_POST['specialization'] ?? '') ?>"
                        placeholder="np. Kardiolog, Dermatolog, Internista"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        title="Wprowadź specjalizację lekarza (pole opcjonalne)">
                </div>

                <!-- Domyślny gabinet -->
                <div>
                    <label for="default_room_id" class="block text-sm font-medium text-slate-700 mb-2">
                        Domyślny gabinet
                    </label>
                    <select id="default_room_id"
                        name="default_room_id"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        title="Wybierz domyślny gabinet, w którym będzie przyjmował lekarz">
                        <option value="">-- Brak przypisanego gabinetu --</option>
                        <?php foreach ($rooms as $room): ?>
                            <option value="<?= $room['id'] ?>">
                                <?= htmlspecialchars($room['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-xs text-slate-500 mt-2">Wybierz domyślny gabinet, w którym będzie przyjmował lekarz</p>
                </div>

                <!-- Przypisany wyświetlacz -->
                <div>
                    <label for="display_id" class="block text-sm font-medium text-slate-700 mb-2">
                        Przypisany wyświetlacz
                    </label>
                    <select id="display_id"
                        name="display_id"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        title="Wybierz wyświetlacz, na którym będą wyświetlane informacje o lekarzu">
                        <option value="">-- Brak przypisanego wyświetlacza --</option>
                        <?php foreach ($displays as $display): ?>
                            <option value="<?= $display['id'] ?>">
                                <?= htmlspecialchars($display['display_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-xs text-slate-500 mt-2">Wybierz wyświetlacz, na którym będą wyświetlane informacje o lekarzu</p>
                </div>
                <!-- Informacje pomocnicze -->
                <div class="bg-blue-50 rounded-lg p-4">
                    <div class="flex items-start">
                        <span class="material-icons-outlined text-blue-600 mr-2 mt-0.5">info</span>
                        <div>
                            <h3 class="font-medium text-blue-900 mb-1">Informacje</h3>
                            <ul class="text-sm text-blue-800 space-y-1">
                                <li>• Lekarz zostanie automatycznie aktywowany po dodaniu</li>
                                <li>• Będzie mógł przyjmować pacjentów od razu</li>
                                <li>• Możesz później edytować wszystkie dane</li>
                                <li>• Domyślny czas wizyty: <?= DEFAULT_APPOINTMENT_DURATION ?> minut</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Przyciski -->
                <div class="flex items-center justify-between pt-6 border-t border-slate-200">
                    <a href="/admin/lekarze"
                        class="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors" title="Powrót do listy lekarzy bez dodawania">
                        ← Powrót do listy lekarzy
                    </a>

                    <div class="flex gap-3">
                        <button type="reset"
                            class="px-4 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors" title="Wyczyść wszystkie pola formularza i przywróć wartości domyślne">
                            <span class="material-icons-outlined text-sm mr-1">refresh</span>
                            Wyczyść formularz
                        </button>

                        <button type="submit"
                            class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors" title="Dodaj nowego lekarza do systemu i aktywuj jego konto">
                            <span class="material-icons-outlined text-sm mr-1">add</span>
                            Dodaj lekarza
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Przykłady specjalizacji -->
        <div class="mt-6 bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <h3 class="font-medium text-slate-900 mb-3">Przykłady specjalizacji</h3>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                <span class="px-3 py-1 bg-slate-100 text-slate-700 rounded-full">Kardiolog</span>
                <span class="px-3 py-1 bg-slate-100 text-slate-700 rounded-full">Dermatolog</span>
                <span class="px-3 py-1 bg-slate-100 text-slate-700 rounded-full">Internista</span>
                <span class="px-3 py-1 bg-slate-100 text-slate-700 rounded-full">Pediatra</span>
                <span class="px-3 py-1 bg-slate-100 text-slate-700 rounded-full">Ginekolog</span>
                <span class="px-3 py-1 bg-slate-100 text-slate-700 rounded-full">Neurolog</span>
                <span class="px-3 py-1 bg-slate-100 text-slate-700 rounded-full">Ortopeda</span>
                <span class="px-3 py-1 bg-slate-100 text-slate-700 rounded-full">Okulista</span>
                <span class="px-3 py-1 bg-slate-100 text-slate-700 rounded-full">Laryngolog</span>
            </div>
        </div>
    </div>
</div>

<script>
    // Podgląd zdjęcia z pliku
    function previewPhoto(input) {
        if (input.files && input.files[0]) {
            const file = input.files[0];
            const reader = new FileReader();

            reader.onload = function(e) {
                const preview = document.getElementById('photo-preview');
                preview.innerHTML = `<img src="${e.target.result}" alt="Podgląd" class="w-16 h-16 rounded-full object-cover">`;
                
                // Aktualizuj informacje o pliku
                const fileInfo = document.getElementById('file-info');
                const fileName = document.getElementById('file-name');
                const buttonText = document.getElementById('button-text');
                
                fileName.textContent = file.name;
                buttonText.textContent = 'Zmień plik';
                fileInfo.classList.remove('hidden');
            }

            reader.readAsDataURL(file);
        }
    }
    
    // Wyczyść zdjęcie
    function clearPhoto() {
        const input = document.getElementById('photo_upload');
        const preview = document.getElementById('photo-preview');
        const fileInfo = document.getElementById('file-info');
        const buttonText = document.getElementById('button-text');
        
        // Wyczyść input
        input.value = '';
        
        // Przywróć domyślny podgląd
        preview.innerHTML = '<span class="material-icons-outlined text-slate-500 text-2xl">person</span>';
        
        // Ukryj informacje o pliku
        fileInfo.classList.add('hidden');
        
        // Przywróć domyślny tekst przycisku
        buttonText.textContent = 'Wybierz plik';
    }

    // Kliknięcie na przykład specjalizacji
    document.querySelectorAll('.bg-slate-100').forEach(function(span) {
        span.addEventListener('click', function() {
            document.getElementById('specialization').value = this.textContent;
        });
        span.style.cursor = 'pointer';
        span.title = 'Kliknij aby wybrać tę specjalizację';
    });
</script>