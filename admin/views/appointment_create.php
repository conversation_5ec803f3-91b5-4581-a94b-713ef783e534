<div class="px-6 py-4">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <div class="flex items-center gap-4 mb-4">
                <a href="/admin/pulpit?date=<?= $selectedDate ?>" class="p-2 rounded-lg hover:bg-slate-100 transition-colors">
                    <span class="material-icons-outlined text-slate-600">arrow_back</span>
                </a>
                <div>
                    <h2 class="text-2xl font-bold text-slate-900">Dodaj nowego pacjenta</h2>
                    <p class="text-slate-600"><PERSON>ów wizytę na dzień <?= formatDate($selectedDate) ?></p>
                </div>
            </div>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <?php if ($error): ?>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <span class="material-icons-outlined text-red-400 text-sm mr-2">error</span>
                        <span class="text-sm text-red-800"><?= htmlspecialchars($error) ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <span class="material-icons-outlined text-green-400 text-sm mr-2">check_circle</span>
                        <span class="text-sm text-green-800"><?= htmlspecialchars($success) ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <form method="POST" class="space-y-6">
                <!-- Lekarz -->
                <div>
                    <label for="doctor_id" class="block text-sm font-medium text-slate-700 mb-2">
                        Lekarz *
                    </label>
                    <select id="doctor_id" name="doctor_id" required
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Wybierz lekarza</option>
                        <?php foreach ($doctors as $doctor): ?>
                            <option value="<?= $doctor['id'] ?>" <?= $selectedDoctor == $doctor['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                <?php if ($doctor['specialization']): ?>
                                    - <?= htmlspecialchars($doctor['specialization']) ?>
                                <?php endif; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Imię i nazwisko pacjenta -->
                <div>
                    <label for="patient_name" class="block text-sm font-medium text-slate-700 mb-2">
                        Imię i nazwisko pacjenta *
                    </label>
                    <input type="text" id="patient_name" name="patient_name" required
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Wprowadź imię i nazwisko pacjenta"
                        value="<?= htmlspecialchars($_POST['patient_name'] ?? '') ?>">
                </div>

                <!-- Data wizyty -->
                <div>
                    <label for="appointment_date" class="block text-sm font-medium text-slate-700 mb-2">
                        Data wizyty *
                    </label>
                    <input type="date" id="appointment_date" name="appointment_date" required
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        value="<?= $_POST['appointment_date'] ?? $selectedDate ?>">
                </div>

                <!-- Godzina wizyty -->
                <div>
                    <label for="appointment_time" class="block text-sm font-medium text-slate-700 mb-2">
                        Godzina wizyty *
                    </label>
                    <div class="grid grid-cols-4 gap-2 mb-3">
                        <!-- Sugerowane godziny -->
                        <?php
                        $suggestedTimes = [
                            '08:00',
                            '08:30',
                            '09:00',
                            '09:30',
                            '10:00',
                            '10:30',
                            '11:00',
                            '11:30',
                            '12:00',
                            '12:30',
                            '13:00',
                            '13:30',
                            '14:00',
                            '14:30',
                            '15:00',
                            '15:30',
                            '16:00',
                            '16:30',
                            '17:00',
                            '17:30'
                        ];
                        foreach ($suggestedTimes as $time): ?>
                            <button type="button" onclick="selectTime('<?= $time ?>')"
                                class="time-btn px-3 py-2 text-sm border border-slate-300 rounded hover:bg-indigo-50 hover:border-indigo-300 transition-colors">
                                <?= $time ?>
                            </button>
                        <?php endforeach; ?>
                    </div>
                    <input type="time" id="appointment_time" name="appointment_time" required
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        value="<?= $_POST['appointment_time'] ?? '' ?>">
                    <p class="text-xs text-slate-500 mt-1">Kliknij na sugerowaną godzinę powyżej lub wprowadź własną</p>
                </div>

                <!-- Dodatkowe pola -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Numer telefonu -->
                    <div>
                        <label for="phone_number" class="block text-sm font-medium text-slate-700 mb-2">
                            Numer telefonu
                        </label>
                        <input type="tel" id="phone_number" name="phone_number"
                            class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            placeholder="+48 123 456 789"
                            value="<?= htmlspecialchars($_POST['phone_number'] ?? '') ?>">
                        <p class="text-xs text-slate-500 mt-1">Opcjonalnie - do wysyłki SMS</p>
                    </div>

                    <!-- Czas trwania wizyty -->
                    <div>
                        <label for="appointment_duration" class="block text-sm font-medium text-slate-700 mb-2">
                            Czas trwania wizyty (minuty)
                        </label>
                        <input type="number" id="appointment_duration" name="appointment_duration" min="5" max="120" step="5"
                            class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            value="<?= $_POST['appointment_duration'] ?? 20 ?>">
                        <p class="text-xs text-slate-500 mt-1">Domyślnie 20 minut</p>
                    </div>
                </div>

                <!-- ID zewnętrzne -->
                <div>
                    <label for="external_id" class="block text-sm font-medium text-slate-700 mb-2">
                        ID zewnętrzne
                    </label>
                    <input type="text" id="external_id" name="external_id"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="ID z systemu zewnętrznego"
                        value="<?= htmlspecialchars($_POST['external_id'] ?? '') ?>">
                    <p class="text-xs text-slate-500 mt-1">Opcjonalnie - do integracji z zewnętrznymi systemami</p>
                </div>

                <!-- Przyciski -->
                <div class="flex items-center justify-between pt-6 border-t border-slate-200">
                    <a href="/admin/pulpit?date=<?= $selectedDate ?>"
                        class="px-4 py-2 text-slate-700 bg-slate-100 rounded-lg hover:bg-slate-200 transition-colors">
                        Anuluj
                    </a>
                    <button type="submit"
                        class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors">
                        Dodaj wizytę
                    </button>
                </div>
            </form>
        </div>

        <!-- Informacje o lekarzach -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 class="text-sm font-medium text-blue-900 mb-2">Dostępni lekarze</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <?php foreach ($doctors as $doctor): ?>
                    <div class="flex items-center gap-3 p-2 bg-white rounded border">
                        <img class="h-8 w-8 rounded-full object-cover"
                            src="<?= getDoctorPhotoUrl($doctor['photo_url']) ?>"
                            alt="<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>">
                        <div>
                            <div class="text-sm font-medium text-slate-900">
                                <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                            </div>
                            <div class="text-xs text-slate-600">
                                <?= htmlspecialchars($doctor['specialization']) ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<script>
    function selectTime(time) {
        document.getElementById('appointment_time').value = time;

        // Highlight selected button
        document.querySelectorAll('.time-btn').forEach(btn => {
            btn.classList.remove('bg-indigo-100', 'border-indigo-500', 'text-indigo-700');
            btn.classList.add('border-slate-300');
        });

        event.target.classList.add('bg-indigo-100', 'border-indigo-500', 'text-indigo-700');
        event.target.classList.remove('border-slate-300');
    }

    // Auto-select doctor if provided in URL
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const doctorId = urlParams.get('doctor');
        if (doctorId) {
            document.getElementById('doctor_id').value = doctorId;
        }
    });
</script>