<?php
$pageTitle = 'Kategorie materiałów video';
?>

<div class="min-h-screen bg-slate-50">
    <div class="w-full px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-slate-900">Kategorie materiałów video</h1>
                    <p class="text-slate-600 mt-1">Zarządzaj kategoriami i materiałami video</p>
                </div>
                <div class="flex gap-3">
                    <a href="/admin/video/all" class="inline-flex items-center px-4 py-2 bg-slate-600 text-white text-sm font-medium rounded-lg hover:bg-slate-700 transition-colors" title="Przeglądaj wszystkie materiały video w systemie">
                        <span class="material-icons-outlined text-sm mr-2">video_library</span>
                        Wszystkie video
                    </a>
                    <a href="/admin/video/add" class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors" title="Dodaj nowy materiał video do systemu">
                        <span class="material-icons-outlined text-sm mr-2">video_call</span>
                        Dodaj materiał
                    </a>
                    <button onclick="showAddCategoryModal()" class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors" title="Utwórz nową kategorię do organizacji materiałów">
                        <span class="material-icons-outlined text-sm mr-2">add</span>
                        Dodaj kategorię
                    </button>
                </div>
            </div>
        </div>

        <!-- Instrukcja obsługi kategorii video -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg mb-6">
            <div class="p-4 border-b border-blue-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-blue-600 mr-3">info</span>
                        <h2 class="text-lg font-semibold text-blue-900">Instrukcja obsługi kategorii video</h2>
                    </div>
                    <button type="button" class="text-blue-600 hover:text-blue-800 transition-colors" onclick="toggleInstruction('video-instruction')">
                        <span id="video-instruction-toggle" class="material-icons-outlined">expand_less</span>
                    </button>
                </div>
            </div>
            <div id="video-instruction" class="p-6">
                <div>
                    <div class="space-y-3 text-sm text-blue-800">
                        <div>
                            <h3 class="font-medium text-blue-900 mb-1">Co to są kategorie video?</h3>
                            <p>Kategorie video pozwalają na organizację materiałów w systemie KtoOstatni. Dzięki nim możesz grupować materiały według tematów, przeznaczenia lub innych kryteriów.</p>
                        </div>
                        
                        <div>
                            <h3 class="font-medium text-blue-900 mb-1">Jak zarządzać kategoriami?</h3>
                            <ol class="list-decimal list-inside ml-4 mt-1 space-y-1">
                                <li><strong>Utwórz kategorię:</strong> Kliknij przycisk "Dodaj kategorię", aby utworzyć nową grupę materiałów.</li>
                                <li><strong>Włącz automatyczną akceptację:</strong> Dla każdej kategorii możesz włączyć automatyczną akceptację materiałów.</li>
                                <li><strong>Przeglądaj materiały:</strong> Kliknij "Zobacz video", aby wyświetlić wszystkie materiały w danej kategorii.</li>
                                <li><strong>Zarządzaj materiałami:</strong> Akceptuj lub odrzucaj materiały bezpośrednio z poziomu kategorii.</li>
                            </ol>
                        </div>
                        
                        <div>
                            <h3 class="font-medium text-blue-900 mb-1">Automatyczna akceptacja</h3>
                            <p>Włącz automatyczną akceptację, aby materiały dodawane do tej kategorii były automatycznie zatwierdzane bez potrzeby ręcznej weryfikacji. Jest to przydatne dla zaufanych źródeł materiałów.</p>
                        </div>
                        
                        <div>
                            <h3 class="font-medium text-blue-900 mb-1">Wskazówki</h3>
                            <ul class="list-disc list-inside ml-4 mt-1">
                                <li>Każda kategoria może zawierać dowolną liczbę materiałów video</li>
                                <li>Możesz edytować nazwę i opis kategorii w dowolnym momencie</li>
                                <li>Usunięcie kategorii jest możliwe tylko wtedy, gdy nie zawiera żadnych materiałów</li>
                                <li>Statystyki pokazują liczbę materiałów w każdym statusie (oczekujące, zaakceptowane, odrzucone)</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-4 pt-3 border-t border-blue-200">
                        <p class="text-xs text-blue-600">
                            <strong>Uwaga:</strong> Zmiana ustawień automatycznej akceptacji wpływa tylko na nowo dodawane materiały. Istniejące materiały zachowują swój obecny status.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Często zadawane pytania -->
        <div class="bg-amber-50 border border-amber-200 rounded-lg mb-6">
            <div class="p-4 border-b border-amber-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <span class="material-icons-outlined text-amber-600 mr-3">help_outline</span>
                        <h2 class="text-lg font-semibold text-amber-900">Często zadawane pytania (FAQ)</h2>
                    </div>
                    <button type="button" class="text-amber-600 hover:text-amber-800 transition-colors" onclick="toggleInstruction('video-faq-section')">
                        <span id="video-faq-section-toggle" class="material-icons-outlined">expand_less</span>
                    </button>
                </div>
            </div>
            <div id="video-faq-section" class="p-6">
                <div>
                    <div class="space-y-3 text-sm text-amber-800">
                        <div>
                            <h3 class="font-medium text-amber-900 mb-1">Czy mogę przenieść materiały między kategoriami?</h3>
                            <p>Tak, możesz edytować każdy materiał i zmienić jego kategorię z poziomu widoku "Wszystkie video".</p>
                        </div>
                        
                        <div>
                            <h3 class="font-medium text-amber-900 mb-1">Co się stanie z materiałami po usunięciu kategorii?</h3>
                            <p>System nie pozwoli usunąć kategorii, która zawiera jakiekolwiek materiały. Musisz najpierw przenieść lub usunąć wszystkie materiały z tej kategorii.</p>
                        </div>
                        
                        <div>
                            <h3 class="font-medium text-amber-900 mb-1">Jakie formaty plików są obsługiwane?</h3>
                            <p>System obsługuje pliki video (MP4, AVI, MOV, WMV) oraz zdjęcia (JPEG, PNG, GIF, WEBP). Dodatkowo możesz dodawać linki do YouTube.</p>
                        </div>
                        
                        <div>
                            <h3 class="font-medium text-amber-900 mb-1">Czy mogę ustawić czas wyświetlania dla zdjęć?</h3>
                            <p>Tak, dla każdego zdjęcia możesz ustawić indywidualny czas wyświetlania w zakresie od 1 do 300 sekund.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Komunikaty -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg">
                <?= htmlspecialchars($_SESSION['success']) ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg">
                <?= htmlspecialchars($_SESSION['error']) ?>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <!-- Lista kategorii -->
        <?php if (empty($categories)): ?>
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-8 text-center">
                <div class="text-slate-400 mb-4">
                    <span class="material-icons-outlined text-6xl">category</span>
                </div>
                <h3 class="text-lg font-medium text-slate-900 mb-2">Brak kategorii</h3>
                <p class="text-slate-600 mb-4">Nie znaleziono żadnych kategorii materiałów video.</p>
                <button onclick="showAddCategoryModal()" class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors" title="Utwórz pierwszą kategorię materiałów video">
                    <span class="material-icons-outlined text-sm mr-2">add</span>
                    Dodaj pierwszą kategorię
                </button>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><?php foreach ($categories as $category): ?>
                    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow">
                        <!-- Header kategorii -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1 min-w-0">
                                <h3 class="text-lg font-semibold text-slate-900 truncate" title="<?= htmlspecialchars($category['name']) ?>">
                                    <?= htmlspecialchars($category['name']) ?>
                                </h3>
                                <div class="text-sm text-slate-600 mt-1 h-10 flex flex-col justify-start">
                                    <?php
                                                                                    $description = $category['description'] ?? '';
                                                                                    $lines = explode("\n", wordwrap($description, 50, "\n", true));
                                    ?>
                                    <div class="leading-5" title="<?= htmlspecialchars($description) ?>">
                                        <?= htmlspecialchars($lines[0] ?? '') ?>
                                    </div>
                                    <div class="leading-5" title="<?= htmlspecialchars($description) ?>">
                                        <?= htmlspecialchars($lines[1] ?? '') ?>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-1 ml-3">
                                <a href="/admin/video/edit-category?id=<?= $category['id'] ?>" class="p-1 text-slate-400 hover:text-indigo-600 transition-colors" title="Edytuj nazwę i opis kategorii">
                                    <span class="material-icons-outlined text-sm">edit</span>
                                </a>
                                <?php if ($category['video_count'] == 0): ?>
                                    <form method="POST" action="/admin/video/delete-category" class="inline" onsubmit="return confirm('Czy na pewno chcesz usunąć tę kategorię?')">
                                        <input type="hidden" name="id" value="<?= $category['id'] ?>">
                                        <button type="submit" class="p-1 text-slate-400 hover:text-red-600 transition-colors" title="Usuń kategorię (możliwe tylko jeśli nie zawiera materiałów)">
                                            <span class="material-icons-outlined text-sm">delete</span>
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Automatyczna akceptacja -->
                        <div class="mb-4">
                            <form method="POST" action="/admin/video/toggle-auto-accept" class="inline">
                                <input type="hidden" name="id" value="<?= $category['id'] ?>">
                                <button type="submit" class="flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium transition-colors <?= $category['auto_accept'] ? 'bg-green-100 text-green-800 hover:bg-green-200' : 'bg-slate-100 text-slate-800 hover:bg-slate-200' ?>" title="<?= $category['auto_accept'] ? 'Wyłącz automatyczną akceptację materiałów' : 'Włącz automatyczną akceptację materiałów' ?>">
                                    <span class="material-icons-outlined text-sm">
                                        <?= $category['auto_accept'] ? 'check_circle' : 'radio_button_unchecked' ?>
                                    </span>
                                    <?= $category['auto_accept'] ? 'Auto-akceptacja włączona' : 'Auto-akceptacja wyłączona' ?>
                                </button>
                            </form>
                        </div>

                        <!-- Statystyki -->
                        <div class="mb-4 space-y-2">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-slate-600">Materiały video:</span>
                                <span class="font-medium text-slate-900"><?= $category['video_count'] ?></span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-slate-600">Oczekujące:</span>
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium <?= $category['pending_count'] > 0 ? 'bg-yellow-100 text-yellow-800' : 'bg-slate-100 text-slate-600' ?>">
                                    <?= $category['pending_count'] ?>
                                </span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-slate-600">Zaakceptowane:</span>
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium <?= $category['approved_count'] > 0 ? 'bg-green-100 text-green-800' : 'bg-slate-100 text-slate-600' ?>">
                                    <?= $category['approved_count'] ?>
                                </span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-slate-600">Odrzucone:</span>
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium <?= $category['rejected_count'] > 0 ? 'bg-red-100 text-red-800' : 'bg-slate-100 text-slate-600' ?>">
                                    <?= $category['rejected_count'] ?>
                                </span>
                            </div>
                        </div>

                        <!-- Akcje -->
                        <div class="flex gap-2">
                            <a href="/admin/video/category?id=<?= $category['id'] ?>" class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-slate-100 text-slate-700 text-sm font-medium rounded-lg hover:bg-slate-200 transition-colors" title="Przeglądaj wszystkie materiały w tej kategorii">
                                <span class="material-icons-outlined text-sm mr-1">visibility</span>
                                Zobacz video
                            </a>
                            <?php if ($category['pending_count'] > 0): ?>
                                <form method="POST" action="/admin/video/approve-category" class="flex-1" onsubmit="return confirm('Czy na pewno chcesz zaakceptować wszystkie oczekujące materiały w tej kategorii?')">
                                    <input type="hidden" name="id" value="<?= $category['id'] ?>">
                                    <button type="submit" class="w-full inline-flex items-center justify-center px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors" title="Zaakceptuj wszystkie oczekujące materiały w tej kategorii">
                                        <span class="material-icons-outlined text-sm mr-1">check_circle</span>
                                        Zaakceptuj
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>

                        <!-- Data utworzenia -->
                        <div class="mt-3 pt-3 border-t border-slate-100">
                            <p class="text-xs text-slate-500">
                                Utworzono: <?= date('d.m.Y H:i', strtotime($category['created_at'])) ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Informacje o automatycznej akceptacji -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <span class="material-icons-outlined text-blue-600 mr-3 mt-0.5">info</span>
                <div>
                    <h3 class="text-sm font-medium text-blue-900 mb-1">Automatyczna akceptacja</h3>
                    <p class="text-sm text-blue-800">
                        Materiały video przypisane do kategorii z włączoną automatyczną akceptacją będą automatycznie zaakceptowane po przesłaniu.
                        Kategorie bez automatycznej akceptacji wymagają ręcznego zatwierdzenia każdego materiału.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal dodawania kategorii -->
<div id="addCategoryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-slate-200">
            <h3 class="text-lg font-medium text-slate-900">Dodaj nową kategorię</h3>
        </div>
        <form method="POST" action="/admin/video/add-category">
            <div class="px-6 py-4 space-y-4">
                <div>
                    <label for="categoryName" class="block text-sm font-medium text-slate-700 mb-1">Nazwa kategorii</label>
                    <input type="text" id="categoryName" name="name" required
                        class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="np. Edukacja medyczna">
                </div>
                <div>
                    <label for="categoryDescription" class="block text-sm font-medium text-slate-700 mb-1">Opis (opcjonalny)</label>
                    <textarea id="categoryDescription" name="description" rows="3"
                        class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Opis kategorii..."></textarea>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="autoAccept" name="auto_accept" value="1"
                        class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded">
                    <label for="autoAccept" class="ml-2 block text-sm text-slate-700">
                        Automatyczna akceptacja materiałów w tej kategorii
                    </label>
                </div>
            </div>
            <div class="px-6 py-4 bg-slate-50 border-t border-slate-200 flex justify-end gap-3">
                <button type="button" onclick="hideAddCategoryModal()"
                    class="px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-md hover:bg-slate-50 transition-colors" title="Anuluj tworzenie nowej kategorii">
                    Anuluj
                </button>
                <button type="submit"
                    class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 transition-colors" title="Utwórz nową kategorię materiałów video">
                    Dodaj kategorię
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    function showAddCategoryModal() {
        document.getElementById('addCategoryModal').classList.remove('hidden');
        document.getElementById('addCategoryModal').classList.add('flex');
        document.getElementById('categoryName').focus();
    }

    function hideAddCategoryModal() {
        document.getElementById('addCategoryModal').classList.add('hidden');
        document.getElementById('addCategoryModal').classList.remove('flex');
        // Wyczyść formularz
        document.getElementById('categoryName').value = '';
        document.getElementById('categoryDescription').value = '';
        document.getElementById('autoAccept').checked = false;
    }

    // Zamknij modal po kliknięciu w tło
    document.getElementById('addCategoryModal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideAddCategoryModal();
        }
    });

    // Zamknij modal po naciśnięciu ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideAddCategoryModal();
        }
    });

    // Funkcja do zwijania/rozwijania sekcji instrukcji
    function toggleInstruction(sectionId) {
        const section = document.getElementById(sectionId);
        const toggleIcon = document.getElementById(sectionId + '-toggle');
        
        if (section.style.display === 'none') {
            section.style.display = 'block';
            toggleIcon.textContent = 'expand_less';
            // Zapisz stan w localStorage
            localStorage.setItem(sectionId + '_collapsed', 'false');
        } else {
            section.style.display = 'none';
            toggleIcon.textContent = 'expand_more';
            // Zapisz stan w localStorage
            localStorage.setItem(sectionId + '_collapsed', 'true');
        }
    }

    // Przywróć stan sekcji z localStorage przy ładowaniu strony
    document.addEventListener('DOMContentLoaded', function() {
        const sections = ['video-instruction', 'video-faq-section'];
        
        sections.forEach(function(sectionId) {
            const isCollapsed = localStorage.getItem(sectionId + '_collapsed') === 'true';
            const section = document.getElementById(sectionId);
            const toggleIcon = document.getElementById(sectionId + '-toggle');
            
            if (isCollapsed && section && toggleIcon) {
                section.style.display = 'none';
                toggleIcon.textContent = 'expand_more';
            }
        });
    });
</script>