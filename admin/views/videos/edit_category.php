<?php
$pageTitle = 'Edytuj kategorię video';
?>

<div class="min-h-screen bg-slate-50">
    <div class="w-full max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center gap-4">
                <a href="/admin/video" class="p-2 text-slate-400 hover:text-slate-600 transition-colors">
                    <span class="material-icons-outlined">arrow_back</span>
                </a>
                <div>
                    <h1 class="text-2xl font-bold text-slate-900">Edytuj kategorię</h1>
                    <p class="text-slate-600 mt-1">Modyfikuj ustawienia kategorii materiałów video</p>
                </div>
            </div>
        </div>

        <!-- Formularz edycji -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200">
            <form method="POST" action="/admin/video/update-category">
                <input type="hidden" name="id" value="<?= $category['id'] ?>">
                
                <div class="px-6 py-4 border-b border-slate-200">
                    <h3 class="text-lg font-medium text-slate-900">Szczegóły kategorii</h3>
                </div>

                <div class="px-6 py-6 space-y-6">
                    <!-- Nazwa kategorii -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-slate-700 mb-2">
                            Nazwa kategorii <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="name" name="name" required 
                               value="<?= htmlspecialchars($category['name']) ?>"
                               class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="np. Edukacja medyczna">
                        <p class="text-xs text-slate-500 mt-1">Nazwa kategorii będzie widoczna dla użytkowników</p>
                    </div>

                    <!-- Opis kategorii -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-slate-700 mb-2">
                            Opis kategorii
                        </label>
                        <textarea id="description" name="description" rows="4"
                                  class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                  placeholder="Opcjonalny opis kategorii..."><?= htmlspecialchars($category['description'] ?? '') ?></textarea>
                        <p class="text-xs text-slate-500 mt-1">Opis pomoże użytkownikom zrozumieć przeznaczenie kategorii</p>
                    </div>

                    <!-- Automatyczna akceptacja -->
                    <div>
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input type="checkbox" id="auto_accept" name="auto_accept" value="1"
                                       <?= $category['auto_accept'] ? 'checked' : '' ?>
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded">
                            </div>
                            <div class="ml-3">
                                <label for="auto_accept" class="text-sm font-medium text-slate-700">
                                    Automatyczna akceptacja materiałów
                                </label>
                                <p class="text-xs text-slate-500 mt-1">
                                    Gdy włączone, wszystkie materiały video przypisane do tej kategorii będą automatycznie akceptowane po przesłaniu.
                                    Użyj tej opcji tylko dla zaufanych źródeł treści.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Informacje o kategorii -->
                    <div class="bg-slate-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-slate-900 mb-3">Informacje o kategorii</h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-slate-600">Data utworzenia:</span>
                                <span class="font-medium text-slate-900 ml-2">
                                    <?= date('d.m.Y H:i', strtotime($category['created_at'])) ?>
                                </span>
                            </div>
                            <?php if (isset($category['updated_at']) && $category['updated_at']): ?>
                                <div>
                                    <span class="text-slate-600">Ostatnia modyfikacja:</span>
                                    <span class="font-medium text-slate-900 ml-2">
                                        <?= date('d.m.Y H:i', strtotime($category['updated_at'])) ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Przyciski akcji -->
                <div class="px-6 py-4 bg-slate-50 border-t border-slate-200 flex justify-between items-center">
                    <a href="/admin/video" 
                       class="inline-flex items-center px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-md hover:bg-slate-50 transition-colors">
                        <span class="material-icons-outlined text-sm mr-2">arrow_back</span>
                        Powrót do kategorii
                    </a>
                    
                    <div class="flex gap-3">
                        <button type="button" onclick="window.history.back()" 
                                class="px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-md hover:bg-slate-50 transition-colors">
                            Anuluj
                        </button>
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 transition-colors">
                            <span class="material-icons-outlined text-sm mr-2">save</span>
                            Zapisz zmiany
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Ostrzeżenie o automatycznej akceptacji -->
        <div class="mt-6 bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div class="flex items-start">
                <span class="material-icons-outlined text-amber-600 mr-3 mt-0.5">warning</span>
                <div>
                    <h3 class="text-sm font-medium text-amber-900 mb-1">Uwaga dotycząca automatycznej akceptacji</h3>
                    <p class="text-sm text-amber-800">
                        Włączenie automatycznej akceptacji oznacza, że wszystkie materiały video przypisane do tej kategorii 
                        będą natychmiast publikowane bez ręcznej moderacji. Używaj tej funkcji tylko dla zaufanych źródeł treści.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
