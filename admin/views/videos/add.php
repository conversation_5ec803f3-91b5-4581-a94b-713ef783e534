<?php
$pageTitle = 'Dodaj materiał video';
?>

<div class="min-h-screen bg-slate-50">
    <div class="w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center gap-4">
                <a href="/admin/video/all" class="p-2 text-slate-400 hover:text-slate-600 transition-colors" data-tooltip="Powrót do listy kategorii video">
                    <span class="material-icons-outlined">arrow_back</span>
                </a>
                <div>
                    <h1 class="text-2xl font-bold text-slate-900">Dodaj nowy materiał video</h1>
                    <p class="text-slate-600 mt-1">Dodaj nowy materiał video do systemu</p>
                </div>
            </div>
        </div>

        <!-- Komunikaty -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg">
                <?= htmlspecialchars($_SESSION['success']) ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg">
                <?= htmlspecialchars($_SESSION['error']) ?>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <!-- Formularz dodawania -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200">
            <div class="px-6 py-4 border-b border-slate-200">
                <h2 class="text-lg font-medium text-slate-900">Informacje o materiale</h2>
            </div>
            
            <form method="POST" action="/admin/video/store" enctype="multipart/form-data" class="p-6 space-y-6">
                <!-- Nazwa -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">Nazwa materiału *</label>
                    <input type="text" name="name" required
                           class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Wprowadź nazwę materiału"
                           data-tooltip="Wprowadź nazwę materiału (pole wymagane)">
                </div>

                <!-- Opis -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">Opis</label>
                    <textarea name="description" rows="3"
                              class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="Opcjonalny opis materiału"
                              data-tooltip="Wprowadź opcjonalny opis materiału"></textarea>
                </div>

                <!-- Typ materiału -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">Typ materiału *</label>
                    <select name="media_type" id="mediaType" onchange="toggleMediaFields()" required
                            class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            title="Wybierz typ materiału, który chcesz dodać">
                        <option value="">Wybierz typ materiału</option>
                        <option value="video">Video (plik lokalny)</option>
                        <option value="youtube">YouTube</option>
                        <option value="image">Zdjęcie</option>
                    </select>
                </div>

                <!-- URL YouTube -->
                <div id="youtubeUrlField" class="hidden">
                    <label class="block text-sm font-medium text-slate-700 mb-2">Link YouTube *</label>
                    <input type="url" name="youtube_url"
                           class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="https://www.youtube.com/watch?v=..."
                           title="Wklej pełny link do video YouTube">
                    <p class="text-xs text-slate-500 mt-1">Wklej pełny link do video YouTube</p>
                </div>

                <!-- Plik lokalny -->
                <div id="localFileField" class="hidden">
                    <label class="block text-sm font-medium text-slate-700 mb-2">Plik *</label>
                    <input type="file" name="local_file" accept="video/*,image/*"
                           class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           title="Wybierz plik video lub zdjęcie z komputera">
                    <p class="text-xs text-slate-500 mt-1">Obsługiwane formaty: MP4, AVI, MOV, WMV (video) | JPEG, PNG, GIF, WEBP (zdjęcia)</p>
                </div>

                <!-- Czas wyświetlania (dla zdjęć) -->
                <div id="displayDurationField" class="hidden">
                    <label class="block text-sm font-medium text-slate-700 mb-2">Czas wyświetlania (sekundy) *</label>
                    <input type="number" name="display_duration" min="1" max="300" value="30"
                           class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           title="Jak długo zdjęcie ma być wyświetlane (1-300 sekund)">
                    <p class="text-xs text-slate-500 mt-1">Jak długo zdjęcie ma być wyświetlane (1-300 sekund)</p>
                </div>

                <!-- Kategoria -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">Kategoria</label>
                    <select name="category_id"
                            class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            title="Wybierz kategorię, do której należy przypisać materiał">
                        <option value="">Brak kategorii</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Reklamodawca -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">Reklamodawca</label>
                    <select name="ads_advertiser_id"
                            class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            title="Wybierz reklamodawcę, jeśli materiał jest reklamą">
                        <option value="">Brak reklamodawcy</option>
                        <?php foreach ($advertisers as $advertiser): ?>
                            <option value="<?= $advertiser['id'] ?>"><?= htmlspecialchars($advertiser['name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Status -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-2">Status</label>
                    <select name="approval_status"
                            class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            title="Wybierz status materiału po dodaniu">
                        <option value="pending">Oczekujący</option>
                        <option value="approved">Zaakceptowany</option>
                        <option value="rejected">Odrzucony</option>
                    </select>
                </div>

                <!-- Przyciski -->
                <div class="flex justify-end gap-3 pt-6 border-t border-slate-200">
                    <a href="/admin/video/all"
                       class="px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-md hover:bg-slate-50 transition-colors"
                       title="Anuluj dodawanie materiału i powrót do listy">
                        Anuluj
                    </a>
                    <button type="submit"
                            class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 transition-colors"
                            title="Dodaj nowy materiał video do systemu">
                        Dodaj materiał
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function toggleMediaFields() {
        const mediaType = document.getElementById('mediaType').value;
        const youtubeField = document.getElementById('youtubeUrlField');
        const localFileField = document.getElementById('localFileField');
        const durationField = document.getElementById('displayDurationField');
        
        // Ukryj wszystkie pola
        youtubeField.classList.add('hidden');
        localFileField.classList.add('hidden');
        durationField.classList.add('hidden');
        
        // Pokaż odpowiednie pola
        if (mediaType === 'youtube') {
            youtubeField.classList.remove('hidden');
        } else if (mediaType === 'video' || mediaType === 'image') {
            localFileField.classList.remove('hidden');
            if (mediaType === 'image') {
                durationField.classList.remove('hidden');
            }
        }
    }

</script>
