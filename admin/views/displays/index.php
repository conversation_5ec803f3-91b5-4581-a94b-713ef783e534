<!-- Zarząd<PERSON>ie wyświetlaczami -->
<div class="px-6 py-4">
    <!-- Komunikaty -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <span class="material-icons-outlined text-green-400 text-sm mr-2">check_circle</span>
                <span class="text-sm text-green-800"><?= htmlspecialchars($_SESSION['success']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <span class="material-icons-outlined text-red-400 text-sm mr-2">error</span>
                <span class="text-sm text-red-800"><?= htmlspecialchars($_SESSION['error']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <!-- Header -->
    <div class="mb-6">
        <div>
            <h1 class="text-2xl font-bold text-slate-900">Zarządzanie wyświetlaczami</h1>
            <p class="text-slate-600 mt-1">Paruj wyświetlacze i zarządzaj ich statusem</p>
        </div>
    </div>

    <!-- Instrukcja obsługi wyświetlaczy -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg mb-6">
        <div class="p-4 border-b border-blue-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-blue-600 mr-3">info</span>
                    <h2 class="text-lg font-semibold text-blue-900">Instrukcja obsługi wyświetlaczy</h2>
                </div>
                <button type="button" class="text-blue-600 hover:text-blue-800 transition-colors" onclick="toggleInstruction('displays-instruction')">
                    <span id="displays-instruction-toggle" class="material-icons-outlined">expand_less</span>
                </button>
            </div>
        </div>
        <div id="displays-instruction" class="p-6">
            <div>
                <div class="space-y-3 text-sm text-blue-800">
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Co to są wyświetlacze?</h3>
                        <p>Wyświetlacze to ekrany telewizyjne lub monitory, które wyświetlają informacje o pacjentach, kolejkach i materiałach reklamowych w poczekalni. Każdy wyświetlacz jest sparowany z systemem i może być zdalnie zarządzany.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Jak sparować wyświetlacz?</h3>
                        <ol class="list-decimal list-inside ml-4 mt-1 space-y-1">
                            <li><strong>Uruchom przeglądarkę:</strong> Na telewizorze otwórz przeglądarkę internetową i przejdź na adres <code class="bg-blue-100 px-2 py-1 rounded text-xs"><?= BASE_URL ?>/display</code>.</li>
                            <li><strong>Skopiuj kod:</strong> Na ekranie pojawi się 6-znakowy kod parowania, który jest ważny przez 15 minut.</li>
                            <li><strong>Wpisz kod:</strong> Wpisz kod w formularzu parowania w panelu administracyjnym i kliknij "Sparuj".</li>
                            <li><strong>Ustawienia:</strong> Skonfiguruj głośność, napisy i inne ustawienia wyświetlacza zgodnie z potrzebami.</li>
                        </ol>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Konfiguracja wyświetlacza</h3>
                        <p>Po sparowaniu wyświetlacza możesz zdalnie zarządzać jego ustawieniami:</p>
                        <ul class="list-disc list-inside ml-4 mt-1 space-y-1">
                            <li><strong>Głośność:</strong> Reguluj poziom głośności dla komunikatów głosowych</li>
                            <li><strong>Napisy:</strong> Włącz lub wyłącz wyświetlanie napisów na ekranie</li>
                            <li><strong>Rozmiar czcionki:</strong> Dostosuj wielkość tekstu napisów (20px-36px)</li>
                            <li><strong>Podgląd:</strong> Zobacz w czasie rzeczywistym, co jest wyświetlane</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-blue-900 mb-1">Wskazówki</h3>
                        <ul class="list-disc list-inside ml-4 mt-1">
                            <li>Dla automatycznego uruchamiania ustaw stronę startową przeglądarki na adres <code class="bg-blue-100 px-2 py-1 rounded text-xs"><?= BASE_URL ?>/display</code></li>
                            <li>Wyświetlacz automatycznie rozpoznaje, czy jest już sparowany z systemem</li>
                            <li>Status online/offline informuje o aktualnym połączeniu wyświetlacza z serwerem</li>
                            <li>Możesz edytować nazwę wyświetlacza, aby łatwiej go identyfikować</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-4 pt-3 border-t border-blue-200">
                    <p class="text-xs text-blue-600">
                        <strong>Uwaga:</strong> Kod parowania jest ważny przez 15 minut. Po tym czasie wygenerowany zostanie nowy kod. Upewnij się, że telewizor ma stałe połączenie z internetem.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Często zadawane pytania -->
    <div class="bg-amber-50 border border-amber-200 rounded-lg mb-6">
        <div class="p-4 border-b border-amber-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-amber-600 mr-3">help_outline</span>
                    <h2 class="text-lg font-semibold text-amber-900">Często zadawane pytania (FAQ)</h2>
                </div>
                <button type="button" class="text-amber-600 hover:text-amber-800 transition-colors" onclick="toggleInstruction('displays-faq-section')">
                    <span id="displays-faq-section-toggle" class="material-icons-outlined">expand_less</span>
                </button>
            </div>
        </div>
        <div id="displays-faq-section" class="p-6">
            <div>
                <div class="space-y-3 text-sm text-amber-800">
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Jakie typy telewizorów są kompatybilne?</h3>
                        <p>System współpracuje z większością nowoczesnych telewizorów Smart TV z przeglądarką internetową. W przypadku starszych modeli można użyć zewnętrznych urządzeń takich jak Chromecast, Apple TV lub Android TV Box.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Co się stanie po utracie połączenia z internetem?</h3>
                        <p>Wyświetlacz pokaże informację o braku połączenia i będzie automatycznie próbował odzyskać połączenie. Po przywróceniu internetu wyświetlacz wznowi normalne działanie.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Czy mogę sparować wiele wyświetlaczy z jednym kontem?</h3>
                        <p>Nie, system ogranicza liczbę wyświetlaczy do 5 sztuk. Większa liczba wyświetlaczy wymaga wykupienia dodatkowej licencji.</p>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-amber-900 mb-1">Jak odłączyć wyświetlacz od systemu?</h3>
                        <p>Możesz usunąć parowanie wyświetlacza z panelu administracyjnego, klikając ikonę "usuń parowanie". Wyświetlacz pozostanie w systemie, ale przestanie wyświetlać zawartość do ponownego sparowania.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <!-- Formularz parowania -->
    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6 mb-6">
        <h2 class="text-lg font-semibold text-slate-900 mb-4">
            <span class="material-icons-outlined text-indigo-600 mr-2" style="vertical-align: middle;">link</span>
            Sparuj wyświetlacz
        </h2>

        <form method="POST" action="/admin/wyswietlacze/pair" class="flex gap-4 items-end">
            <div class="flex-1">
                <label for="pairing_code" class="block text-sm font-medium text-slate-700 mb-2">
                    Kod parowania
                </label>
                <div class="relative">
                    <input type="text"
                        id="pairing_code"
                        name="pairing_code"
                        class="w-full px-3 py-2 pr-10 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 uppercase text-lg font-mono text-center"
                        placeholder="ABC123"
                        maxlength="6"
                        style="text-transform: uppercase; letter-spacing: 0.2em;"
                        data-tooltip="Wprowadź 6-znakowy kod wyświetlany na ekranie telewizora"
                        required>
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <span class="material-icons-outlined text-slate-400">tv</span>
                    </div>
                </div>
            </div>
            <button type="submit"
                class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2" data-tooltip="Sparuj wyświetlacz z systemem (kod jest ważny 15 minut)">
                <span class="material-icons-outlined text-sm">link</span>
                Sparuj wyświetlacz
            </button>
        </form>

        <div class="mt-3 p-3 bg-blue-50 rounded-md">
            <p class="text-sm text-blue-800">
                <span class="material-icons-outlined text-xs mr-1" style="vertical-align: middle;">info</span>
                <strong>Pamiętaj:</strong> Kod parowania jest wyświetlany na ekranie telewizora po wejściu na adres <code class="bg-blue-100 px-1 rounded text-xs"><?= BASE_URL ?>/display</code>
            </p>
        </div>
    </div>
    <!-- Lista wyświetlaczy -->
    <div class="bg-white rounded-lg shadow-sm border border-slate-200">
        <div class="px-6 py-4 border-b border-slate-200">
            <h2 class="text-lg font-semibold text-slate-900">Lista wyświetlaczy</h2>
        </div>

        <?php if (empty($displays)): ?>
            <div class="p-8 text-center">
                <span class="material-icons-outlined text-slate-400 text-4xl mb-4">tv_off</span>
                <p class="text-slate-600">Brak wyświetlaczy w systemie</p>
                <p class="text-sm text-slate-600 mt-4">
                    <span class="material-icons-outlined text-xs mr-1" style="vertical-align: middle;">info</span>
                    Wyświetlacze dodawane są automatycznie przez proces parowania na stronie <?= BASE_URL ?>/display
                </p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-slate-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Wyświetlacz
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Kod
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Status parowania
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Status połączenia
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Ostatnia aktywność
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Głośność
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Napisy
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Wielkość czcionki
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Akcje
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-slate-200">
                        <?php foreach ($displays as $display): ?>
                            <tr class="hover:bg-slate-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <span class="material-icons-outlined text-slate-400 mr-3">tv</span>
                                        <div>
                                            <div class="text-sm font-medium text-slate-900">
                                                <?= htmlspecialchars($display['display_name']) ?>
                                            </div>
                                            <div class="text-sm text-slate-500">
                                                ID: <?= $display['id'] ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <code class="px-2 py-1 bg-slate-100 rounded text-sm font-mono">
                                        <?= strtoupper($display['display_code']) ?>
                                    </code>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($display['pairing_status'] === 'paired'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <span class="material-icons-outlined text-xs mr-1">check_circle</span>
                                            Sparowany
                                        </span>
                                    <?php elseif ($display['pairing_status'] === 'pending'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <span class="material-icons-outlined text-xs mr-1">schedule</span>
                                            Oczekuje
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                                            <span class="material-icons-outlined text-xs mr-1">link_off</span>
                                            Nie sparowany
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($display['is_online']): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <span class="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                                            Online
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                                            <span class="w-2 h-2 bg-slate-400 rounded-full mr-1"></span>
                                            Offline
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                                    <?php if ($display['last_heartbeat']): ?>
                                        <?= formatDateTime($display['last_heartbeat']) ?>
                                    <?php else: ?>
                                        Nigdy
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center space-x-2">
                                        <input type="range"
                                               id="volume_<?= $display['id'] ?>"
                                               value="<?= $display['volume'] ?? 100 ?>"
                                               min="0" max="100"
                                               class="flex-1 h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer"
                                               title="Reguluj głośność komunikatów głosowych na wyświetlaczu (0-100%)"
                                               onchange="updateDisplayVolume(<?= $display['id'] ?>, this.value)">
                                        <span id="volume_value_<?= $display['id'] ?>" class="text-xs font-medium text-slate-900 bg-slate-100 px-2 py-1 rounded min-w-[45px] text-center">
                                            <?= $display['volume'] ?? 100 ?>%
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox"
                                                   id="subtitles_<?= $display['id'] ?>"
                                                   <?= ($display['show_subtitles'] ?? 1) ? 'checked' : '' ?>
                                                   class="sr-only peer"
                                                   title="Włącz lub wyłącz wyświetlanie napisów na ekranie"
                                                   onchange="updateDisplaySubtitles(<?= $display['id'] ?>, this.checked)">
                                            <div class="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                        </label>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <select id="subtitle_font_size_<?= $display['id'] ?>"
                                            class="px-3 py-1 border border-slate-300 rounded-lg text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                            data-previous-value="<?= $display['subtitle_font_size'] ?? 16 ?>"
                                            title="Wybierz rozmiar czcionki napisów wyświetlanych na ekranie"
                                            onchange="updateDisplaySubtitleFontSize(<?= $display['id'] ?>, this.value)">
                                        <option value="20" <?= ($display['subtitle_font_size'] ?? 24) == 20 ? 'selected' : '' ?>>20px</option>
                                        <option value="22" <?= ($display['subtitle_font_size'] ?? 24) == 22 ? 'selected' : '' ?>>22px</option>
                                        <option value="24" <?= ($display['subtitle_font_size'] ?? 24) == 24 ? 'selected' : '' ?>>24px</option>
                                        <option value="26" <?= ($display['subtitle_font_size'] ?? 24) == 26 ? 'selected' : '' ?>>26px</option>
                                        <option value="28" <?= ($display['subtitle_font_size'] ?? 24) == 28 ? 'selected' : '' ?>>28px</option>
                                        <option value="30" <?= ($display['subtitle_font_size'] ?? 24) == 30 ? 'selected' : '' ?>>30px</option>
                                        <option value="32" <?= ($display['subtitle_font_size'] ?? 24) == 32 ? 'selected' : '' ?>>32px</option>
                                        <option value="34" <?= ($display['subtitle_font_size'] ?? 24) == 34 ? 'selected' : '' ?>>34px</option>
                                        <option value="36" <?= ($display['subtitle_font_size'] ?? 24) == 36 ? 'selected' : '' ?>>36px</option>
                                    </select>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center gap-2">
                                        <!-- Przycisk podglądu -->
                                        <?php if ($display['pairing_status'] === 'paired'): ?>
                                            <button type="button"
                                                class="text-green-600 hover:text-green-900"
                                                onclick="previewDisplay('<?= $display['display_code'] ?>', '<?= htmlspecialchars($display['display_name'], ENT_QUOTES) ?>')"
                                                title="Otwórz podgląd zawartości wyświetlacza w nowym oknie">
                                                <span class="material-icons-outlined text-sm">visibility</span>
                                            </button>
                                        <?php endif; ?>

                                        <!-- Przycisk edycji nazwy -->
                                        <button type="button"
                                            class="text-blue-600 hover:text-blue-900"
                                            onclick="editDisplayName(<?= $display['id'] ?>, '<?= htmlspecialchars($display['display_name'], ENT_QUOTES) ?>')"
                                            title="Zmień nazwę wyświetlacza dla łatwiejszej identyfikacji">
                                            <span class="material-icons-outlined text-sm">edit</span>
                                        </button>


                                        <form method="POST" action="/admin/wyswietlacze/delete" class="inline">
                                            <input type="hidden" name="display_id" value="<?= $display['id'] ?>">
                                            <button type="submit"
                                                class="text-red-600 hover:text-red-900"
                                                onclick="return confirm('Czy na pewno chcesz usunąć ten wyświetlacz?')"
                                                title="Usuń wyświetlacz z systemu (operacja nieodwracalna)">
                                                <span class="material-icons-outlined text-sm">delete</span>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal edycji nazwy wyświetlacza -->
<div id="editDisplayModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Edytuj nazwę wyświetlacza</h3>
            <form id="editDisplayForm" method="POST" action="/admin/wyswietlacze/update-name">
                <input type="hidden" id="editDisplayId" name="display_id" value="">
                <div class="mb-4">
                    <label for="editDisplayName" class="block text-sm font-medium text-gray-700 mb-2">
                        Nazwa wyświetlacza
                    </label>
                    <input type="text"
                        id="editDisplayName"
                        name="display_name"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                        maxlength="100">
                </div>
                <div class="flex justify-end gap-3">
                    <button type="button"
                        onclick="closeEditModal()"
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Anuluj
                    </button>
                    <button type="submit"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Zapisz
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal podglądu wyświetlacza -->
<div id="previewDisplayModal" class="fixed inset-0 bg-gray-900 bg-opacity-95 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative inset-0 h-full w-full">
        <div class="absolute top-4 right-4 z-10">
            <button onclick="closePreviewModal()"
                class="bg-red-600 hover:bg-red-700 text-white rounded-full p-2 shadow-lg transition-colors">
                <span class="material-icons-outlined text-white">close</span>
            </button>
        </div>
        <iframe id="previewFrame" class="w-full h-full border-0" src="" title="Podgląd wyświetlacza"></iframe>
    </div>
</div>

<script>
    function editDisplayName(displayId, currentName) {
        document.getElementById('editDisplayId').value = displayId;
        document.getElementById('editDisplayName').value = currentName;
        document.getElementById('editDisplayModal').classList.remove('hidden');
    }

    function closeEditModal() {
        document.getElementById('editDisplayModal').classList.add('hidden');
    }

    // Zamknij modal po kliknięciu poza nim
    document.getElementById('editDisplayModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeEditModal();
        }
    });

    function previewDisplay(displayCode, displayName) {
        document.getElementById('previewFrame').src = '/display/' + displayCode;
        document.getElementById('previewDisplayModal').classList.remove('hidden');
    }

    function closePreviewModal() {
        document.getElementById('previewDisplayModal').classList.add('hidden');
        document.getElementById('previewFrame').src = '';
    }

    // Zamknij modal podglądu po kliknięciu poza nim
    document.getElementById('previewDisplayModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closePreviewModal();
        }
    });

    // Zamknij modal klawiszem ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const editModal = document.getElementById('editDisplayModal');
            const previewModal = document.getElementById('previewDisplayModal');

            if (!editModal.classList.contains('hidden')) {
                closeEditModal();
            } else if (!previewModal.classList.contains('hidden')) {
                closePreviewModal();
            }
        }
    });
</script>

<script>
    // Auto-uppercase dla kodu parowania
    document.getElementById('pairing_code').addEventListener('input', function(e) {
        e.target.value = e.target.value.toUpperCase();
    });

    // Funkcja do aktualizacji głośności wyświetlacza
    function updateDisplayVolume(displayId, volume) {
        // Aktualizuj wyświetlaną wartość
        document.getElementById('volume_value_' + displayId).textContent = volume + '%';

        // Wyślij zapytanie AJAX do serwera
        fetch('/admin/wyswietlacze/update-volume', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'display_id=' + displayId + '&volume=' + volume
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Głośność zaktualizowana:', volume + '%');
                // Opcjonalnie: pokaż powiadomienie o sukcesie
            } else {
                console.error('Błąd aktualizacji głośności:', data.message);
                // Opcjonalnie: pokaż powiadomienie o błędzie
            }
        })
        .catch(error => {
            console.error('Błąd sieci:', error);
        });
    }

    // Funkcja do aktualizacji ustawienia napisów wyświetlacza
    function updateDisplaySubtitles(displayId, showSubtitles) {
        // Wyślij zapytanie AJAX do serwera
        fetch('/admin/wyswietlacze/update-subtitles', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'display_id=' + displayId + '&show_subtitles=' + (showSubtitles ? '1' : '0')
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Ustawienie napisów zaktualizowane:', showSubtitles ? 'włączone' : 'wyłączone');
                // Opcjonalnie: pokaż powiadomienie o sukcesie
            } else {
                console.error('Błąd aktualizacji ustawienia napisów:', data.message);
                // Przywróć poprzedni stan checkboxa
                document.getElementById('subtitles_' + displayId).checked = !showSubtitles;
            }
        })
        .catch(error => {
            console.error('Błąd sieci:', error);
            // Przywróć poprzedni stan checkboxa
            document.getElementById('subtitles_' + displayId).checked = !showSubtitles;
        });
    }

    // Funkcja do aktualizacji wielkości czcionki napisów wyświetlacza
    function updateDisplaySubtitleFontSize(displayId, fontSize) {
        // Wyślij zapytanie AJAX do serwera
        fetch('/admin/wyswietlacze/update-subtitle-font-size', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'display_id=' + displayId + '&subtitle_font_size=' + fontSize
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Wielkość czcionki napisów zaktualizowana:', fontSize + 'px');
                // Opcjonalnie: pokaż powiadomienie o sukcesie
            } else {
                console.error('Błąd aktualizacji wielkości czcionki napisów:', data.message);
                // Przywróć poprzednią wartość selecta
                const selectElement = document.getElementById('subtitle_font_size_' + displayId);
                selectElement.value = selectElement.getAttribute('data-previous-value') || '16';
            }
        })
        .catch(error => {
            console.error('Błąd sieci:', error);
            // Przywróć poprzednią wartość selecta
            const selectElement = document.getElementById('subtitle_font_size_' + displayId);
            selectElement.value = selectElement.getAttribute('data-previous-value') || '16';
        });
    }

    // Funkcja do zwijania/rozwijania sekcji instrukcji
    function toggleInstruction(sectionId) {
        const section = document.getElementById(sectionId);
        const toggleIcon = document.getElementById(sectionId + '-toggle');
        
        if (section.style.display === 'none') {
            section.style.display = 'block';
            toggleIcon.textContent = 'expand_less';
            // Zapisz stan w localStorage
            localStorage.setItem(sectionId + '_collapsed', 'false');
        } else {
            section.style.display = 'none';
            toggleIcon.textContent = 'expand_more';
            // Zapisz stan w localStorage
            localStorage.setItem(sectionId + '_collapsed', 'true');
        }
    }

    // Przywróć stan sekcji z localStorage przy ładowaniu strony
    document.addEventListener('DOMContentLoaded', function() {
        const sections = ['displays-instruction', 'displays-faq-section'];
        
        sections.forEach(function(sectionId) {
            const isCollapsed = localStorage.getItem(sectionId + '_collapsed') === 'true';
            const section = document.getElementById(sectionId);
            const toggleIcon = document.getElementById(sectionId + '-toggle');
            
            if (isCollapsed && section && toggleIcon) {
                section.style.display = 'none';
                toggleIcon.textContent = 'expand_more';
            }
        });
    });
</script>