<!-- <PERSON><PERSON><PERSON><PERSON> wizyty -->
<div class="px-6 py-4">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-slate-900">Edytuj wizytę</h1>
            <p class="text-slate-600 mt-1">Zmień szczegóły wizyty</p>
        </div>

        <!-- Komunikaty -->
        <?php if (isset($error)): ?>
            <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-red-600 mr-2">error</span>
                    <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                    <span class="text-green-800"><?= htmlspecialchars($success) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <!-- Formularz -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <form method="POST" class="space-y-6">
                <!-- Wybór lekarza -->
                <div>
                    <label for="doctor_id" class="block text-sm font-medium text-slate-700 mb-2">
                        Lekarz *
                    </label>
                    <select id="doctor_id"
                        name="doctor_id"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        required>
                        <option value="">-- Wybierz lekarza --</option>
                        <?php foreach ($doctors as $doctor): ?>
                            <option value="<?= $doctor['id'] ?>"
                                <?= ($doctor['id'] == $appointment['doctor_id']) ? 'selected' : '' ?>>
                                <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                <?php if ($doctor['specialization']): ?>
                                    (<?= htmlspecialchars($doctor['specialization']) ?>)
                                <?php endif; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Dane pacjenta -->
                <div>
                    <label for="patient_name" class="block text-sm font-medium text-slate-700 mb-2">
                        Imię i nazwisko pacjenta *
                    </label>
                    <input type="text"
                        id="patient_name"
                        name="patient_name"
                        value="<?= htmlspecialchars($appointment['patient_name']) ?>"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        required>
                </div>

                <!-- Data wizyty -->
                <div>
                    <label for="appointment_date" class="block text-sm font-medium text-slate-700 mb-2">
                        Data wizyty *
                    </label>
                    <input type="date"
                        id="appointment_date"
                        name="appointment_date"
                        value="<?= htmlspecialchars($appointment['appointment_date']) ?>"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        required>
                </div>

                <!-- Godzina wizyty -->
                <div>
                    <label for="appointment_time" class="block text-sm font-medium text-slate-700 mb-2">
                        Godzina wizyty *
                    </label>
                    <input type="time"
                        id="appointment_time"
                        name="appointment_time"
                        value="<?= formatTime($appointment['appointment_time']) ?>"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        required>
                </div>

                <!-- Status wizyty -->
                <div>
                    <label for="status" class="block text-sm font-medium text-slate-700 mb-2">
                        Status wizyty *
                    </label>
                    <select id="status"
                        name="status"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        required>
                        <option value="waiting" <?= ($appointment['status'] == 'waiting') ? 'selected' : '' ?>>Oczekuje</option>
                        <option value="current" <?= ($appointment['status'] == 'current') ? 'selected' : '' ?>>Aktualna</option>
                        <option value="completed" <?= ($appointment['status'] == 'completed') ? 'selected' : '' ?>>Zakończona</option>
                        <option value="closed" <?= ($appointment['status'] == 'closed') ? 'selected' : '' ?>>Zamknięta</option>
                    </select>
                </div>

                <!-- Dodatkowe pola -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Numer telefonu -->
                    <div>
                        <label for="phone_number" class="block text-sm font-medium text-slate-700 mb-2">
                            Numer telefonu
                        </label>
                        <input type="tel" id="phone_number" name="phone_number"
                            class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            placeholder="+48 123 456 789"
                            value="<?= htmlspecialchars($appointment['phone_number'] ?? '') ?>">
                        <p class="text-xs text-slate-500 mt-1">Do wysyłki SMS</p>
                    </div>

                    <!-- Czas trwania wizyty -->
                    <div>
                        <label for="appointment_duration" class="block text-sm font-medium text-slate-700 mb-2">
                            Czas trwania wizyty (minuty)
                        </label>
                        <input type="number" id="appointment_duration" name="appointment_duration" min="5" max="120" step="5"
                            class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            value="<?= htmlspecialchars($appointment['appointment_duration'] ?? 20) ?>">
                        <p class="text-xs text-slate-500 mt-1">Domyślnie 20 minut</p>
                    </div>
                </div>

                <!-- ID zewnętrzne -->
                <div>
                    <label for="external_id" class="block text-sm font-medium text-slate-700 mb-2">
                        ID zewnętrzne
                    </label>
                    <input type="text" id="external_id" name="external_id"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="ID z systemu zewnętrznego"
                        value="<?= htmlspecialchars($appointment['external_id'] ?? '') ?>">
                    <p class="text-xs text-slate-500 mt-1">Do integracji z zewnętrznymi systemami</p>
                </div>

                <!-- Dodatkowe statusy -->
                <div>
                    <h3 class="font-medium text-slate-900 mb-2">Dodatkowe statusy</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <!-- Potwierdzona -->
                        <div class="flex items-center">
                            <input type="checkbox"
                                id="is_confirmed"
                                name="is_confirmed"
                                value="1"
                                <?= (!empty($appointment['is_confirmed'])) ? 'checked' : '' ?>
                                class="h-4 w-4 text-indigo-600 border-slate-300 rounded focus:ring-indigo-500">
                            <label for="is_confirmed" class="ml-2 block text-sm text-slate-700">
                                Wizyta potwierdzona
                            </label>
                        </div>

                        <!-- SMS wysłany -->
                        <div class="flex items-center">
                            <input type="checkbox"
                                id="is_sms_sent"
                                name="is_sms_sent"
                                value="1"
                                <?= (!empty($appointment['is_sms_sent'])) ? 'checked' : '' ?>
                                class="h-4 w-4 text-indigo-600 border-slate-300 rounded focus:ring-indigo-500">
                            <label for="is_sms_sent" class="ml-2 block text-sm text-slate-700">
                                SMS wysłany
                            </label>
                        </div>

                        <!-- Pacjent obecny -->
                        <div class="flex items-center">
                            <input type="checkbox"
                                id="is_patient_present"
                                name="is_patient_present"
                                value="1"
                                <?= (!empty($appointment['is_patient_present'])) ? 'checked' : '' ?>
                                class="h-4 w-4 text-indigo-600 border-slate-300 rounded focus:ring-indigo-500">
                            <label for="is_patient_present" class="ml-2 block text-sm text-slate-700">
                                Pacjent obecny
                            </label>
                        </div>

                        <!-- Wizyta zakończona -->
                        <div class="flex items-center">
                            <input type="checkbox"
                                id="is_completed"
                                name="is_completed"
                                value="1"
                                <?= (!empty($appointment['is_completed'])) ? 'checked' : '' ?>
                                class="h-4 w-4 text-indigo-600 border-slate-300 rounded focus:ring-indigo-500">
                            <label for="is_completed" class="ml-2 block text-sm text-slate-700">
                                Wizyta zakończona
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Informacje o statusie -->
                <div class="bg-slate-50 rounded-lg p-4">
                    <h3 class="font-medium text-slate-900 mb-2">Historia statusu</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-slate-600">Utworzona:</span>
                            <span class="ml-2"><?= formatDateTime($appointment['created_at']) ?></span>
                        </div>
                        <?php if ($appointment['called_at']): ?>
                            <div>
                                <span class="text-slate-600">Wywołana:</span>
                                <span class="ml-2"><?= formatDateTime($appointment['called_at']) ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if ($appointment['completed_at']): ?>
                            <div>
                                <span class="text-slate-600">Zakończona:</span>
                                <span class="ml-2"><?= formatDateTime($appointment['completed_at']) ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if ($appointment['tracking_code']): ?>
                            <div>
                                <span class="text-slate-600">Kod śledzenia:</span>
                                <span class="ml-2 font-mono"><?= htmlspecialchars($appointment['tracking_code']) ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Przyciski -->
                <div class="flex items-center justify-between pt-6 border-t border-slate-200">
                    <a href="/admin/pulpit?date=<?= htmlspecialchars($appointment['appointment_date']) ?>"
                        class="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors" title="Powrót do pulpitu bez zapisywania zmian">
                        ← Powrót do pulpitu
                    </a>

                    <div class="flex gap-3">
                        <button type="button"
                            onclick="deleteAppointment(<?= $appointment['id'] ?>)"
                            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors" title="Usuń wizytę z systemu (operacja nieodwracalna)">
                            <span class="material-icons-outlined text-sm mr-1">delete</span>
                            Usuń wizytę
                        </button>

                        <button type="submit"
                            class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors" title="Zapisz wprowadzone zmiany">
                            <span class="material-icons-outlined text-sm mr-1">save</span>
                            Zapisz zmiany
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function deleteAppointment(appointmentId) {
        if (confirm('Czy na pewno chcesz usunąć tę wizytę? Ta operacja jest nieodwracalna.')) {
            window.location.href = '/admin/appointments/delete/' + appointmentId;
        }
    }
</script>