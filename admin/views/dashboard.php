<!-- Dashboard - Pulpit główny -->
<div class="px-6 py-4">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-slate-900">Pulpit główny</h1>
        <p class="text-slate-600 mt-1">Przegląd systemu KtoOstatni.pl</p>
    </div>

    <!-- Statystyki główne -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Kolejka -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <span class="material-icons-outlined text-blue-500 text-3xl">queue</span>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-slate-600"><PERSON><PERSON><PERSON><PERSON> dzisiaj</p>
                    <p class="text-2xl font-bold text-slate-900"><?= $stats['total_patients'] ?? 0 ?></p>
                </div>
            </div>
            <div class="mt-4">
                <a href="/admin/kolejki" class="text-blue-600 hover:text-blue-800 text-sm font-medium" title="Przejdź do zarządzania kolejkami">
                    Zobacz kolejki →
                </a>
            </div>
        </div>

        <!-- Lekarze -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <span class="material-icons-outlined text-green-500 text-3xl">people</span>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-slate-600">Pracujący lekarze</p>
                    <p class="text-2xl font-bold text-slate-900"><?= $stats['working_doctors'] ?? 0 ?></p>
                </div>
            </div>
            <div class="mt-4">
                <a href="/admin/lekarze" class="text-green-600 hover:text-green-800 text-sm font-medium" title="Przejdź do zarządzania lekarzami">
                    Zarządzaj lekarzami →
                </a>
            </div>
        </div>

        <!-- Wyświetlacze -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <span class="material-icons-outlined text-purple-500 text-3xl">tv</span>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-slate-600">Wyświetlacze online</p>
                    <p class="text-2xl font-bold text-slate-900"><?= $displayStats['online'] ?? 0 ?></p>
                </div>
            </div>
            <div class="mt-4">
                <a href="/admin/wyswietlacze" class="text-purple-600 hover:text-purple-800 text-sm font-medium" title="Przejdź do zarządzania wyświetlaczami">
                    Zarządzaj wyświetlaczami →
                </a>
            </div>
        </div>

        <!-- Video -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <span class="material-icons-outlined text-orange-500 text-3xl">play_circle</span>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-slate-600">Aktywne video</p>
                    <p class="text-2xl font-bold text-slate-900"><?= $adStats['approved'] ?? 0 ?></p>
                </div>
            </div>
            <div class="mt-4">
                <a href="/admin/video" class="text-orange-600 hover:text-orange-800 text-sm font-medium" title="Przejdź do zarządzania video">
                    Zarządzaj video →
                </a>
            </div>
        </div>
    </div>

    <!-- Szczegółowe statystyki kolejki -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- Statystyki wizyt -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <h3 class="text-lg font-semibold text-slate-900 mb-4">
                <span class="material-icons-outlined text-blue-500 mr-2">event</span>
                Wizyty dzisiaj
            </h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-slate-600">Oczekujące</span>
                    <span class="font-semibold text-yellow-600"><?= $stats['waiting_patients'] ?? 0 ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-slate-600">Aktualne</span>
                    <span class="font-semibold text-blue-600"><?= $stats['current_patients'] ?? 0 ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-slate-600">Zakończone</span>
                    <span class="font-semibold text-green-600"><?= $stats['completed_patients'] ?? 0 ?></span>
                </div>
                <div class="flex justify-between items-center pt-2 border-t border-slate-200">
                    <span class="text-slate-600 font-medium">Łącznie</span>
                    <span class="font-bold text-slate-900"><?= $stats['total_patients'] ?? 0 ?></span>
                </div>
            </div>
        </div>

        <!-- Statystyki wyświetlaczy -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <h3 class="text-lg font-semibold text-slate-900 mb-4">
                <span class="material-icons-outlined text-purple-500 mr-2">tv</span>
                Wyświetlacze
            </h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-slate-600">Łącznie wyświetlaczy</span>
                    <span class="font-semibold text-slate-900"><?= $displayStats['total'] ?? 0 ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-slate-600">Sparowane</span>
                    <span class="font-semibold text-green-600"><?= $displayStats['paired'] ?? 0 ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-slate-600">Online</span>
                    <span class="font-semibold text-blue-600"><?= $displayStats['online'] ?? 0 ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-slate-600">Oczekuje na parowanie</span>
                    <span class="font-semibold text-yellow-600"><?= $displayStats['pending'] ?? 0 ?></span>
                </div>
            </div>
        </div>

        <!-- Statystyki systemu -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <h3 class="text-lg font-semibold text-slate-900 mb-4">
                <span class="material-icons-outlined text-indigo-500 mr-2">analytics</span>
                System
            </h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-slate-600">Lekarze aktywni</span>
                    <span class="font-semibold text-green-600"><?= $stats['total_doctors'] ?? 0 ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-slate-600">Gabinety</span>
                    <span class="font-semibold text-blue-600"><?= $stats['total_rooms'] ?? 0 ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-slate-600">Baza danych</span>
                    <span class="font-semibold text-slate-900"><?= $dbStats['database_size_mb'] ?? 0 ?> MB</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-slate-600">Średnie opóźnienie</span>
                    <span class="font-semibold <?= ($averageDelay > 15) ? 'text-red-600' : 'text-green-600' ?>">
                        <?= $averageDelay ?? 0 ?> min
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Aktywni lekarze -->
    <div class="grid grid-cols-1 gap-8 mb-8">
        <!-- Aktywni lekarze -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <h3 class="text-lg font-semibold text-slate-900 mb-4">
                <span class="material-icons-outlined text-green-500 mr-2">people</span>
                Aktywni lekarze (<?= formatDate($selectedDate) ?>)
            </h3>
            <?php if (!empty($doctors)): ?>
                <div class="space-y-3">
                    <?php foreach (array_slice($doctors, 0, 5) as $doctor): ?>
                        <div class="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="material-icons-outlined text-green-600 text-sm">person</span>
                                </div>
                                <div>
                                    <div class="font-medium text-slate-900">
                                        <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                    </div>
                                    <div class="text-sm text-slate-500">
                                        <?= $doctor['current_appointment'] ? 'Aktualnie: ' . htmlspecialchars($doctor['current_appointment']['patient_name']) : 'Brak aktualnej wizyty' ?>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-slate-900">
                                    <?= count($doctor['waiting_appointments']) ?> oczekujących
                                </div>
                                <?php if ($doctor['delay_minutes'] > 0): ?>
                                    <div class="text-xs text-red-600">
                                        Opóźnienie: <?= $doctor['delay_minutes'] ?> min
                                    </div>
                                <?php else: ?>
                                    <div class="text-xs text-green-600">
                                        Na czas
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    <?php if (count($doctors) > 5): ?>
                        <div class="text-center pt-2">
                            <a href="/admin/kolejki" class="text-blue-600 hover:text-blue-800 text-sm font-medium" title="Zobacz wszystkich lekarzy w systemie">
                                Zobacz wszystkich lekarzy (<?= count($doctors) ?>) →
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-8 text-slate-500">
                    <span class="material-icons-outlined text-4xl mb-2">event_busy</span>
                    <p>Brak aktywnych lekarzy na wybraną datę</p>
                </div>
            <?php endif; ?>
        </div>
    </div>


    <!-- Szybkie akcje -->
    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
        <h3 class="text-lg font-semibold text-slate-900 mb-4">
            <span class="material-icons-outlined text-blue-500 mr-2">flash_on</span>
            Szybkie akcje
        </h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="/admin/kolejki" class="flex flex-col items-center p-4 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors" title="Zarządzaj kolejkami pacjentów">
                <span class="material-icons-outlined text-2xl mb-2">queue</span>
                <span class="text-sm font-medium text-center">Zarządzaj kolejkami</span>
            </a>
            <a href="/admin/wyswietlacze" class="flex flex-col items-center p-4 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors" title="Dodaj nowy wyświetlacz">
                <span class="material-icons-outlined text-2xl mb-2">tv</span>
                <span class="text-sm font-medium text-center">Dodaj wyświetlacz</span>
            </a>
            <a href="/admin/video" class="flex flex-col items-center p-4 bg-orange-50 text-orange-700 rounded-lg hover:bg-orange-100 transition-colors" title="Zarządzaj video">
                <span class="material-icons-outlined text-2xl mb-2">play_circle</span>
                <span class="text-sm font-medium text-center">Zarządzaj video</span>
            </a>
            <a href="/admin/lekarze" class="flex flex-col items-center p-4 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors" title="Zarządzaj lekarzami">
                <span class="material-icons-outlined text-2xl mb-2">people</span>
                <span class="text-sm font-medium text-center">Zarządzaj lekarzami</span>
            </a>
        </div>
    </div>
</div>