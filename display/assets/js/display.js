// Zmienne globalne
// currentAd jest zdefiniowany w display_public.php
let ytPlayer = null;
let heartbeatInterval = null;
let queueCheckInterval = null;
let queueData = null;
let currentRoomIndex = 0;
let roomRotationInterval = null;
let isVideoPlaying = false;
let videoPlaybackStarted = false;
let userHasInteracted = false; // Zmienna śledząca czy użytkownik kliknął play
let currentDisplayDate = new Date().toISOString().split('T')[0]; // Format YYYY-MM-DD

// Zmienne dla kolejki powiadomień o wywołaniach pacjentów
let notificationQueue = [];
let isNotificationActive = false;
let currentNotificationTimeout = null;

// Zmienne dla mechanizmu retry
let retryAttempts = {};
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY_BASE = 1000; // 1 sekunda bazowego opóźnienia

// Zmienne do śledzenia zmian
let lastKnownTimestamps = {
    appointments: 0,
    settings: 0
};

// Zmienne dla ustawień wyświetlacza - używaj globalnej zmiennej z window lub utwórz jeśli nie istnieje
if (!window.displaySettings) {
    window.displaySettings = {
        volume: 100, // Domyślna głośność 100%
        subtitles: true, // Domyślnie napisy włączone
        subtitleFontSize: 16 // Domyślna wielkość czcionki napisów 16px
    };
}
let displaySettings = window.displaySettings;

// Obsługa trybu pełnoekranowego
const displayContainer = document.getElementById("displayContainer");
const fullscreenBtn = document.getElementById("fullscreenBtn");

if (fullscreenBtn) {
  fullscreenBtn.addEventListener("click", toggleFullscreen);
}

// Funkcja do przełączania trybu pełnoekranowego
function toggleFullscreen() {
  if (
    !document.fullscreenElement &&
    !document.mozFullScreenElement &&
    !document.webkitFullscreenElement &&
    !document.msFullscreenElement
  ) {
    // Przejście do trybu pełnoekranowego
    if (displayContainer.requestFullscreen) {
      displayContainer.requestFullscreen();
    } else if (displayContainer.mozRequestFullScreen) {
      displayContainer.mozRequestFullScreen();
    } else if (displayContainer.webkitRequestFullscreen) {
      displayContainer.webkitRequestFullscreen();
    } else if (displayContainer.msRequestFullscreen) {
      displayContainer.msRequestFullscreen();
    }
    if (fullscreenBtn) {
      fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
    }
  } else {
    // Wyjście z trybu pełnoekranowego
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
    if (fullscreenBtn) {
      fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
    }
  }
}

// Nasłuchiwanie zmiany stanu pełnoekranowego
document.addEventListener("fullscreenchange", updateFullscreenButton);
document.addEventListener("webkitfullscreenchange", updateFullscreenButton);
document.addEventListener("mozfullscreenchange", updateFullscreenButton);
document.addEventListener("MSFullscreenChange", updateFullscreenButton);

function updateFullscreenButton() {
  if (fullscreenBtn) {
    if (
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.mozFullScreenElement ||
      document.msFullscreenElement
    ) {
      fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
    } else {
      fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
    }
  }
}

// Funkcja pomocnicza do retry wywołań API
function fetchWithRetry(url, options = {}, retryKey = null) {
  const key = retryKey || url;

  // Inicjalizuj licznik prób dla tego klucza jeśli nie istnieje
  if (!retryAttempts[key]) {
    retryAttempts[key] = 0;
  }

  return fetch(url, options)
    .then(response => {
      // Jeśli sukces, zresetuj licznik prób
      if (response.ok) {
        retryAttempts[key] = 0;
        return response;
      }

      // Jeśli błąd HTTP, spróbuj ponownie
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    })
    .catch(error => {
      retryAttempts[key]++;
      console.warn(`fetchWithRetry: Próba ${retryAttempts[key]}/${MAX_RETRY_ATTEMPTS} nieudana dla ${url}:`, error.message);

      // Jeśli przekroczono maksymalną liczbę prób, rzuć błąd
      if (retryAttempts[key] >= MAX_RETRY_ATTEMPTS) {
        console.error(`fetchWithRetry: Przekroczono maksymalną liczbę prób (${MAX_RETRY_ATTEMPTS}) dla ${url}`);
        retryAttempts[key] = 0; // Zresetuj dla przyszłych wywołań
        throw error;
      }

      // Oblicz opóźnienie (exponential backoff)
      const delay = RETRY_DELAY_BASE * Math.pow(2, retryAttempts[key] - 1);
      console.log(`fetchWithRetry: Ponowna próba za ${delay}ms...`);

      // Czekaj i spróbuj ponownie
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(fetchWithRetry(url, options, retryKey));
        }, delay);
      });
    });
}

// Funkcje API
function sendHeartbeat() {
  fetch(`/api/v2/display/${display.display_code}/heartbeat`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    }
  });
}

function recordAdView(adsId, durationSeconds) {
  console.log('recordAdView: Rozpoczynam rejestrowanie wyświetlenia dla ads_id:', adsId, 'display_id:', display.id);

  return fetch("/api/v2/ads/view", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      ads_id: adsId,
      display_id: display.id
    }),
  })
  .then(response => {
    console.log('recordAdView: Otrzymano odpowiedź HTTP:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('recordAdView: Otrzymano odpowiedź JSON:', data);

    // Jeśli API zwróciło kolejny materiał, zaktualizuj currentAd
    if (data.success && data.data && data.data.next_ad) {
      console.log('recordAdView: Otrzymano kolejny materiał:', data.data.next_ad);
      console.log('recordAdView: Poprzedni currentAd:', currentAd);
      currentAd = data.data.next_ad;
      console.log('recordAdView: Zaktualizowano currentAd na:', currentAd);
    } else {
      console.warn('recordAdView: Brak kolejnego materiału w odpowiedzi API');
    }

    return data;
  })
  .catch(error => {
    console.error('recordAdView: Błąd podczas rejestrowania wyświetlenia:', error);
    throw error;
  });
}

// Funkcja do pobierania ustawień wyświetlacza
function loadDisplaySettings() {
  console.log('loadDisplaySettings: Pobieram ustawienia wyświetlacza...');

  return fetchWithRetry(`/api/v2/settings/display/${display.id}`, {}, 'display-settings')
    .then(response => {
      console.log('loadDisplaySettings: Odpowiedź HTTP:', response.status);
      return response.json();
    })
    .then(data => {
      console.log('loadDisplaySettings: Otrzymano ustawienia:', data);

      if (data.success && data.data) {
        displaySettings.volume = data.data.display_volume !== undefined ? data.data.display_volume : 100;
        displaySettings.subtitles = data.data.show_subtitles !== undefined ? data.data.show_subtitles : true;
        displaySettings.subtitleFontSize = data.data.subtitle_font_size !== undefined ? data.data.subtitle_font_size : 16;
        console.log('loadDisplaySettings: Ustawiono głośność na:', displaySettings.volume);
        console.log('loadDisplaySettings: Ustawiono napisy na:', displaySettings.subtitles);
        console.log('loadDisplaySettings: Ustawiono wielkość czcionki napisów na:', displaySettings.subtitleFontSize + 'px');

        // Zaktualizuj głośność wszystkich aktywnych odtwarzaczy
        updateAllPlayersVolume();

        // Zaktualizuj napisy wszystkich aktywnych odtwarzaczy
        updateAllPlayersSubtitles();

        // Zaktualizuj wielkość czcionki napisów
        updateAllPlayersSubtitleFontSize();
      }

      return data; // Zwróć dane dla Promise
    })
    .catch(error => {
      console.error('loadDisplaySettings: Błąd podczas pobierania ustawień:', error);
      throw error; // Przekaż błąd dalej
    });
}

// Funkcja do aktualizacji głośności wszystkich aktywnych odtwarzaczy
function updateAllPlayersVolume() {
    console.log('updateAllPlayersVolume: Aktualizuję głośność wszystkich odtwarzaczy na:', displaySettings.volume);

    // Zaktualizuj głośność HTML5 video
    const videoElements = document.querySelectorAll('video');
    console.log('updateAllPlayersVolume: Znaleziono', videoElements.length, 'elementów video');
    videoElements.forEach((video, index) => {
        const oldVolume = video.volume;
        video.volume = displaySettings.volume / 100;
        console.log(`updateAllPlayersVolume: Video ${index}: zmieniono głośność z ${oldVolume} na ${video.volume} (paused: ${video.paused}, muted: ${video.muted}, currentTime: ${video.currentTime})`);
    });

    // Zaktualizuj głośność YouTube player
    if (ytPlayer && typeof ytPlayer.setVolume === 'function') {
        try {
            const oldVolume = ytPlayer.getVolume();
            ytPlayer.setVolume(displaySettings.volume);
            console.log('updateAllPlayersVolume: YouTube player: zmieniono głośność z', oldVolume, 'na', displaySettings.volume);
        } catch (error) {
            console.error('updateAllPlayersVolume: Błąd ustawiania głośności YouTube player:', error);
        }
    } else {
        console.log('updateAllPlayersVolume: YouTube player nie jest dostępny lub nie ma funkcji setVolume');
    }
}

// Funkcja do aktualizacji napisów wszystkich aktywnych odtwarzaczy
function updateAllPlayersSubtitles() {
    console.log('updateAllPlayersSubtitles: Aktualizuję napisy wszystkich odtwarzaczy na:', displaySettings.subtitles);

    // Zaktualizuj napisy HTML5 video
    const videoElements = document.querySelectorAll('video');
    console.log('updateAllPlayersSubtitles: Znaleziono', videoElements.length, 'elementów video');

    videoElements.forEach((video, index) => {
        if (video.textTracks && video.textTracks.length > 0) {
            const track = video.textTracks[0];
            const oldMode = track.mode;

            if (displaySettings.subtitles) {
                track.mode = 'showing';
                console.log(`updateAllPlayersSubtitles: Video ${index}: włączono napisy (${oldMode} -> ${track.mode})`);
            } else {
                track.mode = 'hidden';
                console.log(`updateAllPlayersSubtitles: Video ${index}: wyłączono napisy (${oldMode} -> ${track.mode})`);
            }
        } else {
            console.log(`updateAllPlayersSubtitles: Video ${index}: brak ścieżek tekstowych`);
        }
    });
}

// Funkcja do aktualizacji wielkości czcionki napisów wszystkich aktywnych odtwarzaczy
function updateAllPlayersSubtitleFontSize() {
    console.log('updateAllPlayersSubtitleFontSize: Aktualizuję wielkość czcionki napisów wszystkich odtwarzaczy na:', displaySettings.subtitleFontSize + 'px');

    // Użyj funkcji applySubtitleFontSize jeśli jest dostępna (zdefiniowana w display_public.php)
    if (typeof applySubtitleFontSize === 'function') {
        applySubtitleFontSize();
        return;
    }

    // Fallback - jeśli funkcja applySubtitleFontSize nie jest dostępna
    console.log('updateAllPlayersSubtitleFontSize: Używam fallback - ustawiam zmienną CSS');

    // Ustaw zmienną CSS --subtitle-font-size w :root
    document.documentElement.style.setProperty('--subtitle-font-size', displaySettings.subtitleFontSize + 'px');
    console.log('updateAllPlayersSubtitleFontSize: Ustawiono zmienną CSS --subtitle-font-size na:', displaySettings.subtitleFontSize + 'px');

    // Dodatkowe style dla YouTube (które nie używają ::cue)
    let subtitleStyles = document.getElementById('dynamic-subtitle-styles');
    if (!subtitleStyles) {
        subtitleStyles = document.createElement('style');
        subtitleStyles.id = 'dynamic-subtitle-styles';
        document.head.appendChild(subtitleStyles);
    }

    // Style dla YouTube napisów
    subtitleStyles.textContent = `
        .ytp-caption-segment {
            font-size: ${displaySettings.subtitleFontSize}px !important;
        }
        .caption-window {
            font-size: ${displaySettings.subtitleFontSize}px !important;
        }
    `;

    console.log('updateAllPlayersSubtitleFontSize: Zaktualizowano zmienną CSS i style YouTube (fallback)');
}

// Funkcja testowa do sprawdzania głośności (dostępna z konsoli przeglądarki)
function testVolumeUpdate() {
    console.log('=== TEST GŁOŚNOŚCI ===');
    console.log('Aktualne ustawienie głośności:', displaySettings.volume);

    // Sprawdź HTML5 video
    const videoElements = document.querySelectorAll('video');
    console.log('Znaleziono', videoElements.length, 'elementów video:');
    videoElements.forEach((video, index) => {
        console.log(`Video ${index}: volume=${video.volume}, paused=${video.paused}, currentTime=${video.currentTime}, src=${video.src}`);
    });

    // Sprawdź YouTube player
    if (ytPlayer) {
        console.log('YouTube player dostępny:', typeof ytPlayer.setVolume === 'function');
        if (typeof ytPlayer.getVolume === 'function') {
            try {
                console.log('YouTube player volume:', ytPlayer.getVolume());
            } catch (e) {
                console.log('Błąd pobierania głośności YouTube:', e);
            }
        }
    } else {
        console.log('YouTube player nie jest dostępny');
    }

    // Wywołaj aktualizację głośności, napisów i wielkości czcionki
    updateAllPlayersVolume();
    updateAllPlayersSubtitles();
    updateAllPlayersSubtitleFontSize();
    console.log('=== KONIEC TESTU ===');
}

// Funkcja do sprawdzania i aktualizacji danych o kolejce
function checkQueueStatus() {
  console.log('checkQueueStatus: Sprawdzanie statusu kolejki...');
  console.log('checkQueueStatus: display.id =', display.id);
  console.log('checkQueueStatus: currentDisplayDate =', currentDisplayDate);
  console.log('checkQueueStatus: Ostatnie znane timestampy:', lastKnownTimestamps);

  // Jeśli nie ma jeszcze danych kolejki, pobierz je bezpośrednio
  if (!queueData) {
    console.log('checkQueueStatus: Brak danych kolejki, pobieram bezpośrednio...');
    fetchWithRetry(`/api/v2/queue/${display.id}?date=${currentDisplayDate}`, {}, 'queue-initial')
      .then((response) => {
        console.log('checkQueueStatus: Odpowiedź HTTP dla queue (direct):', response.status);
        return response.json();
      })
      .then((response) => {
        console.log('checkQueueStatus: Otrzymano dane kolejki (direct):', response);
        processQueueData(response);
        // Po pierwszym pobraniu zapisz timestampy
        updateLocalTimestamps();
      })
      .catch((error) => {
        console.error("Błąd podczas pobierania danych o kolejce (direct):", error);
      });
    return;
  }

  // Sprawdź czy są zmiany w systemie dla appointments i settings jednym zapytaniem
  checkForChanges('appointments,settings')
    .then((changes) => {
      const appointmentsChanged = changes.appointments;
      const settingsChanged = changes.settings;

      console.log('checkQueueStatus: Sprawdzono zmiany - appointments:', appointmentsChanged, 'settings:', settingsChanged);

      // Przygotuj promises dla operacji, które trzeba wykonać
      const promises = [];

      // Obsłuż zmiany w appointments
      if (appointmentsChanged) {
        console.log('checkQueueStatus: Wykryto zmiany w appointments, pobieram pełne dane...');
        const queuePromise = fetchWithRetry(`/api/v2/queue/${display.id}?date=${currentDisplayDate}`, {}, 'queue-update')
          .then((response) => {
            console.log('checkQueueStatus: Odpowiedź HTTP dla queue:', response.status);
            return response.json();
          })
          .then((response) => {
            console.log('checkQueueStatus: Otrzymano dane kolejki:', response);
            processQueueData(response);
          });
        promises.push(queuePromise);
      } else {
        console.log('checkQueueStatus: Brak zmian w appointments, pomijam pobieranie danych');
      }

      // Obsłuż zmiany w settings
      if (settingsChanged) {
        console.log('checkQueueStatus: Wykryto zmiany w ustawieniach, pobieram nowe ustawienia...');
        promises.push(loadDisplaySettings());
      } else {
        console.log('checkQueueStatus: Brak zmian w ustawieniach');
      }

      // Wykonaj wszystkie operacje równolegle
      return Promise.all(promises);
    })
    .catch((error) => {
      console.error("Błąd podczas sprawdzania/pobierania danych:", error);
    });
}

// Funkcja do sprawdzania zmian dla konkretnego typu encji (lub wielu typów)
function checkForChanges(entityType) {
  console.log(`checkForChanges: Sprawdzam zmiany dla ${entityType}...`);
  return fetchWithRetry(`/api/v2/changes/${entityType}`, {}, `changes-${entityType}`)
    .then((response) => {
      console.log(`checkForChanges: Odpowiedź HTTP dla ${entityType}:`, response.status);
      return response.json();
    })
    .then((changesResponse) => {
      console.log(`checkForChanges: Otrzymano dane o zmianach dla ${entityType}:`, changesResponse);

      if (!changesResponse.success) {
        console.error(`checkForChanges: Błąd API changes dla ${entityType}:`, changesResponse.error);
        return false;
      }

      // Sprawdź czy to odpowiedź dla wielu encji czy pojedynczej
      if (changesResponse.data.entities) {
        // Wiele encji - zwróć obiekt z wynikami dla każdej encji
        const results = {};

        for (const [type, entityData] of Object.entries(changesResponse.data.entities)) {
          const serverTimestamp = entityData.lastUpdate;
          const localTimestamp = lastKnownTimestamps[type] || 0;
          const hasChanges = serverTimestamp > localTimestamp;

          console.log(`checkForChanges: Porównuję timestampy dla ${type}:`, {
            server: serverTimestamp,
            local: localTimestamp,
            serverDate: new Date(serverTimestamp * 1000).toISOString(),
            localDate: new Date(localTimestamp * 1000).toISOString(),
            hasChanges: hasChanges
          });

          if (hasChanges) {
            lastKnownTimestamps[type] = serverTimestamp;
          }

          results[type] = hasChanges;
        }

        return results;
      } else {
        // Pojedyncza encja - zachowaj stary format dla kompatybilności
        const serverTimestamp = changesResponse.data ? changesResponse.data.lastUpdate : 0;
        const localTimestamp = lastKnownTimestamps[entityType] || 0;
        const hasChanges = serverTimestamp > localTimestamp;

        console.log(`checkForChanges: Porównuję timestampy dla ${entityType}:`, {
          server: serverTimestamp,
          local: localTimestamp,
          serverDate: new Date(serverTimestamp * 1000).toISOString(),
          localDate: new Date(localTimestamp * 1000).toISOString(),
          hasChanges: hasChanges
        });

        if (hasChanges) {
          lastKnownTimestamps[entityType] = serverTimestamp;
        }

        return hasChanges;
      }
    })
    .catch((error) => {
      console.error(`checkForChanges: Błąd podczas sprawdzania zmian dla ${entityType}:`, error);
      return false;
    });
}

// Funkcja do aktualizacji lokalnych timestampów
function updateLocalTimestamps() {
  console.log('updateLocalTimestamps: Aktualizuję lokalne timestampy...');
  
  // Pobierz aktualny timestamp dla appointments
  fetch('/api/v2/changes/appointments')
    .then(r => r.json())
    .then((appointmentsResponse) => {
      console.log('updateLocalTimestamps: Odpowiedź z serwera dla appointments:', appointmentsResponse);

      if (appointmentsResponse.success && appointmentsResponse.data) {
        lastKnownTimestamps.appointments = appointmentsResponse.data.lastUpdate;
        console.log('updateLocalTimestamps: Zaktualizowano timestamp appointments:', lastKnownTimestamps.appointments);
        console.log('updateLocalTimestamps: Data appointments:', new Date(lastKnownTimestamps.appointments * 1000).toISOString());
      }
    }).catch((error) => {
      console.error('updateLocalTimestamps: Błąd podczas aktualizacji timestamp appointments:', error);
    });
    
  // Pobierz aktualny timestamp dla settings
  fetch('/api/v2/changes/settings')
    .then(r => r.json())
    .then((settingsResponse) => {
      console.log('updateLocalTimestamps: Odpowiedź z serwera dla settings:', settingsResponse);

      if (settingsResponse.success && settingsResponse.data) {
        lastKnownTimestamps.settings = settingsResponse.data.lastUpdate;
        console.log('updateLocalTimestamps: Zaktualizowano timestamp settings:', lastKnownTimestamps.settings);
        console.log('updateLocalTimestamps: Data settings:', new Date(lastKnownTimestamps.settings * 1000).toISOString());
      }
    }).catch((error) => {
      console.error('updateLocalTimestamps: Błąd podczas aktualizacji timestamp settings:', error);
    });
}

// Funkcja do przetwarzania danych kolejki
function processQueueData(response) {
  if (!response.success) {
    console.error('processQueueData: Błąd API queue:', response.error);
    return;
  }

  const data = response.data || response;

  // Porównaj tylko istotne dane (bez timestamp)
  const newDataForComparison = {
    client_id: data.client_id,
    doctors: data.doctors
  };
  const oldDataForComparison = queueData ? {
    client_id: queueData.client_id,
    doctors: queueData.doctors
  } : null;

  const newDataStr = JSON.stringify(newDataForComparison);
  const oldDataStr = JSON.stringify(oldDataForComparison);

  console.log('processQueueData: Porównuję dane...');
  console.log('processQueueData: oldDataStr length:', oldDataStr ? oldDataStr.length : 0);
  console.log('processQueueData: newDataStr length:', newDataStr.length);

  if (newDataStr !== oldDataStr) {
    console.log('processQueueData: Dane się zmieniły, aktualizuję wyświetlacz');

    // Zapisz stare dane przed aktualizacją
    const oldQueueData = queueData;

    // Sprawdź czy zmieniła się aktualna wizyta u któregoś lekarza
    checkForCurrentAppointmentChanges(oldQueueData, data);

    // Sprawdź czy zmieniła się lista lekarzy z aktualnymi lub oczekującymi wizytami
    const oldDoctorsWithAppointments = oldQueueData ? oldQueueData.doctors.filter(d => d.current || (d.waiting && d.waiting.length > 0)).map(d => d.id) : [];
    const newDoctorsWithAppointments = data.doctors.filter(d => d.current || (d.waiting && d.waiting.length > 0)).map(d => d.id);
    const doctorsListChanged = JSON.stringify(oldDoctorsWithAppointments.sort()) !== JSON.stringify(newDoctorsWithAppointments.sort());

    // Aktualizuj dane po sprawdzeniu zmian
    queueData = data;

    // Sprawdź czy zmieniły się szczegóły wizyt
    const oldAppointments = oldQueueData ? oldQueueData.doctors.filter(d => d.current || (d.waiting && d.waiting.length > 0)).map(d => ({ id: d.id, current: d.current, waiting: d.waiting })) : [];
    const newAppointments = data.doctors.filter(d => d.current || (d.waiting && d.waiting.length > 0)).map(d => ({ id: d.id, current: d.current, waiting: d.waiting }));

    console.log('processQueueData: Stare wizyty:', JSON.stringify(oldAppointments));
    console.log('processQueueData: Nowe wizyty:', JSON.stringify(newAppointments));

    // Sprawdź czy zmieniły się szczegóły wizyt (nie tylko lista lekarzy)
    const appointmentsChanged = JSON.stringify(oldAppointments) !== JSON.stringify(newAppointments);

    // Aktualizuj wyświetlacz jeśli zmieniła się lista lekarzy LUB szczegóły wizyt
    if (doctorsListChanged || appointmentsChanged) {
      console.log('processQueueData: Lista lekarzy lub szczegóły wizyt się zmieniły, przebudowuję wyświetlacz');
      updateQueueDisplay();
    } else {
      console.log('processQueueData: Brak zmian, pomijam aktualizację');
    }

    // Ustawienie rotacji lekarzy, jeśli jest więcej niż 1 lekarz z aktualną lub oczekującymi wizytami
    const doctorsWithCurrent = data.doctors.filter(doctor => doctor.current || (doctor.waiting && doctor.waiting.length > 0));
    if (doctorsWithCurrent.length > 1) {
      if (!roomRotationInterval) {
        roomRotationInterval = setInterval(rotateRooms, 10000);
      }
    } else {
      if (roomRotationInterval) {
        clearInterval(roomRotationInterval);
        roomRotationInterval = null;
      }
    }
    
    // Zaktualizuj lokalne timestampy po przetworzeniu danych
    updateLocalTimestamps();
  } else {
    console.log('processQueueData: Dane się nie zmieniły');
  }
}



// Funkcja do rotacji lekarzy
function rotateRooms() {
  console.log('rotateRooms: Rozpoczynam rotację, currentRoomIndex =', currentRoomIndex);
  if (!queueData || !queueData.doctors) return;

  // Filtruj lekarzy z aktualną wizytą lub oczekującymi wizytami
  const doctorsWithCurrent = queueData.doctors.filter(doctor => doctor.current || (doctor.waiting && doctor.waiting.length > 0));
  console.log('rotateRooms: Liczba lekarzy z aktualnymi lub oczekującymi wizytami:', doctorsWithCurrent.length);
  if (doctorsWithCurrent.length <= 1) return;

  const currentRoomContainer = document.querySelector(
    `.queue-rooms-container.active`,
  );
  if (currentRoomContainer) {
    currentRoomContainer.classList.remove("active");
  }

  currentRoomIndex = (currentRoomIndex + 1) % doctorsWithCurrent.length;
  console.log('rotateRooms: Nowy currentRoomIndex =', currentRoomIndex);

  const nextRoomContainer = document.querySelector(
    `.queue-rooms-container[data-room-index="${currentRoomIndex}"]`,
  );
  if (nextRoomContainer) {
    nextRoomContainer.classList.add("active");
    console.log('rotateRooms: Aktywowano kontener lekarza o indeksie', currentRoomIndex);
  } else {
    console.log('rotateRooms: Nie znaleziono kontenera o indeksie', currentRoomIndex);
  }
}

// Funkcja do aktualizacji wyświetlania kolejki
function updateQueueDisplay() {
  console.log('updateQueueDisplay: Rozpoczynam aktualizację wyświetlacza kolejki', new Date().toISOString());
  console.log('updateQueueDisplay: queueData =', queueData);

  if (!queueData) {
    console.log('updateQueueDisplay: Brak queueData, przerywam');
    return;
  }

  console.log('updateQueueDisplay: Liczba lekarzy w kolejce:', queueData.doctors ? queueData.doctors.length : 'brak doctors');

  // Sprawdź, czy istnieje panel boczny kolejki, jeśli nie - utwórz go
  let queueSidebar = document.querySelector(".queue-sidebar");
  console.log('updateQueueDisplay: queueSidebar =', queueSidebar);

  if (!queueSidebar) {
    console.log('updateQueueDisplay: Tworzę nowy queueSidebar');
    const contentContainer = document.getElementById("contentContainer");
    console.log('updateQueueDisplay: contentContainer =', contentContainer);

    if (!contentContainer) {
      console.log('updateQueueDisplay: Brak contentContainer, przerywam');
      return;
    }

    // Przekształć kontener na układ z kolejką
    contentContainer.parentElement.classList.add("display-with-queue");

    // Utwórz panel boczny kolejki
    queueSidebar = document.createElement("div");
    queueSidebar.className = "queue-sidebar";
    contentContainer.parentElement.insertBefore(queueSidebar, contentContainer);

    // Zmień klasę kontenera reklam
    contentContainer.className = "queue-ad-container";
    console.log('updateQueueDisplay: queueSidebar utworzony');
  }

  console.log('updateQueueDisplay: Czyszczę zawartość queueSidebar');
  queueSidebar.innerHTML = "";

  // Filtruj lekarzy z aktualną wizytą lub oczekującymi wizytami
  const doctorsWithCurrent = queueData.doctors.filter(doctor => doctor.current || (doctor.waiting && doctor.waiting.length > 0));
  console.log('updateQueueDisplay: Lekarzy z aktualną lub oczekującymi wizytami:', doctorsWithCurrent.length);

  // Resetuj currentRoomIndex tylko jeśli przekracza liczbę dostępnych lekarzy
  if (currentRoomIndex >= doctorsWithCurrent.length) {
    currentRoomIndex = 0;
  }

  console.log('updateQueueDisplay: currentRoomIndex =', currentRoomIndex);

  doctorsWithCurrent.forEach((doctor, index) => {
    console.log('updateQueueDisplay: Przetwarzam lekarza:', doctor.name, 'index:', index);
    const room = doctor.room;
    const current = doctor.current;
    const waiting = doctor.waiting;

    console.log('updateQueueDisplay: Dodaję lekarza do wyświetlacza:', doctor.name);

    const doctorContainer = document.createElement("div");
    doctorContainer.className = `queue-rooms-container ${index === currentRoomIndex ? "active" : ""}`;
    doctorContainer.setAttribute("data-room-index", index);

    const roomElement = document.createElement("div");
    roomElement.className = "queue-room";

    // Nagłówek z informacjami o lekarzu
    const roomHeader = document.createElement("div");
    roomHeader.className = "queue-room-header";

    const doctorName = DataHelper.normalizeName(doctor.name) || 'Lekarz';
    const doctorPhoto = doctor.photo_url || null;
    const roomName = doctor.room && doctor.room.name ? DataHelper.normalizeRoomName(doctor.room.name) : '';

    roomHeader.innerHTML = `
                <div class="doctor-photo slide-in-top">
                    ${doctorPhoto ? `<img src="${doctorPhoto}" alt="Lekarz" onerror="this.onerror=null; this.src='/uploads/doctors/default-avatar.webp'; this.setAttribute('data-fallback', 'true');">` : '<i class="fas fa-user-md" style="font-size: 60px; color: #fff; line-height: 150px;"></i>'}
                </div>
                <div class="slide-in-bottom">
                    <div class="doctor-name">
                        ${doctorName}
                    </div>
                    ${roomName ? `<div class="room-name">${roomName}</div>` : ''}
                </div>
            `;
    roomElement.appendChild(roomHeader);

    // Aktualna wizyta lub następna jeśli nie ma aktualnej
    const currentElement = document.createElement("div");
    currentElement.className = "queue-current slide-in-left";

    let displayAppointment = current;
    let labelText = "Aktualnie:";

    // Jeśli nie ma aktualnej wizyty, weź pierwszą oczekującą
    if (!current && waiting && waiting.length > 0) {
      displayAppointment = waiting[0];
      labelText = "Następna:";
    }

    if (displayAppointment) {
      // Formatuj czas - wyciągnij tylko godzinę z appointment_time
      let time = displayAppointment.number || displayAppointment.appointment_time;
      if (time && time.includes(' ')) {
        // Jeśli appointment_time zawiera datę i czas, wyciągnij tylko czas
        time = time.split(' ')[1] || time;
      }
      
      const firstName = displayAppointment.patient_name ? displayAppointment.patient_name.split(" ")[0] : '';
      const patientName = firstName ? `p. ${DataHelper.normalizeName(firstName)}` : 'Pacjent';
      currentElement.innerHTML = `
                <div class="current-label">${labelText}</div>
                <div class="current-number">${time}</div>
                <div class="current-patient">${patientName}</div>
            `;
    } else {
      currentElement.innerHTML = `
                <div class="current-label">Aktualnie:</div>
                <div class="current-number">-</div>
                <div class="current-patient">Brak wizyty</div>
            `;
    }
    roomElement.appendChild(currentElement);

    // Oczekujące wizyty
    if (waiting && waiting.length > 0) {
      const waitingElement = document.createElement("div");
      waitingElement.className = "queue-waiting slide-in-right";
      waitingElement.innerHTML =
        '<div class="waiting-label">Następne wizyty:</div>';

      // Jeśli nie ma aktualnej wizyty, pomiń pierwszą oczekującą (już pokazana jako "Następna")
      // Ogranicz do maksymalnie 3 wizyt oczekujących
      const waitingToShow = (!current ? waiting.slice(1) : waiting).slice(0, 3);

      waitingToShow.forEach((appointment) => {
        const appointmentElement = document.createElement("div");
        appointmentElement.className = "queue-number";

        // Formatuj czas - wyciągnij tylko godzinę z appointment_time
        let time = appointment.number || appointment.appointment_time;
        if (time && time.includes(' ')) {
          // Jeśli appointment_time zawiera datę i czas, wyciągnij tylko czas
          time = time.split(' ')[1] || time;
        }
        
        const firstName = appointment.patient_name ? appointment.patient_name.split(" ")[0] : '';
        const patientName = firstName ? `p. ${DataHelper.normalizeName(firstName)}` : 'Pacjent';

        appointmentElement.innerHTML = `
                    <div class="number">${time}</div>
                    <div class="patient">${patientName}</div>
                `;
        waitingElement.appendChild(appointmentElement);
      });

      // Dodaj element tylko jeśli są wizyty do pokazania
      if (waitingToShow.length > 0) {
        roomElement.appendChild(waitingElement);
      }
    }

    doctorContainer.appendChild(roomElement);
    queueSidebar.appendChild(doctorContainer);
    console.log('updateQueueDisplay: Dodano lekarza do queueSidebar:', doctorName);
  });

  console.log('updateQueueDisplay: Zakończono aktualizację wyświetlacza kolejki');
}

// Funkcja wyświetlania reklam
function showNextAd() {
  console.log('showNextAd: Rozpoczynam wyświetlanie materiału');
  console.log('showNextAd: currentAd:', currentAd);

  if (!currentAd) {
    console.log('showNextAd: Brak materiału do wyświetlenia');
    // Nie wyświetlaj żadnych komunikatów - system kolejkowy działa niezależnie
    return;
  }

  console.log('showNextAd: Wyświetlam materiał:', currentAd.name, 'ID:', currentAd.id, 'Typ:', currentAd.media_type);

  if (ytPlayer) {
    ytPlayer.stopVideo();
    ytPlayer = null;
  }

  const ad = currentAd;
  let adContainer;

  if (queueSystemEnabled) {
    const contentContainer = document.getElementById("contentContainer");

    if (!document.querySelector(".display-with-queue")) {
      // Formatuj datę jeśli jest dostępna
      let dateText = '';
      if (queueData && queueData.display_date) {
        const date = new Date(queueData.display_date);
        dateText = `<div class="queue-date">${date.toLocaleDateString('pl-PL', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })}</div>`;
      }

      contentContainer.innerHTML = `
                <div class="display-with-queue">
                    <div class="queue-sidebar">
                        <div class="queue-header">
                          System kolejkowy
                          ${dateText}
                        </div>
                    </div>
                    <div class="queue-ad-container" id="adDisplay"></div>
                </div>
            `;

      // Sprawdź czy interwał nie jest już ustawiony
      if (!queueCheckInterval) {
        checkQueueStatus();
        queueCheckInterval = setInterval(checkQueueStatus, 3000);
        console.log('showNextAd: Ustawiono interwał sprawdzania kolejki');
      }
    }

    adContainer = document.getElementById("adDisplay");
  } else {
    adContainer = document.getElementById("contentContainer");
  }

  adContainer.innerHTML = "";
  adContainer.removeAttribute("style");

  let adElement = null;
  let adDuration = ad.duration || 15;

  switch (ad.media_type) {
    case "image":
      adElement = document.createElement("img");
      adElement.className = "ad-image";
      adElement.src = ad.media_url;
      adElement.alt = ad.name;
      adElement.style.display = "block";
      adElement.style.margin = "auto";

      const imageTimeout = setTimeout(function () {
        console.error("Timeout podczas ładowania obrazu:", ad.media_url);
        rotateAd();
      }, 5000);

      adElement.onerror = function () {
        clearTimeout(imageTimeout);
        console.error("Błąd podczas ładowania obrazu:", ad.media_url);
        setTimeout(function () {
          rotateAd();
        }, 1000);
      };

      adElement.onload = function () {
        clearTimeout(imageTimeout);
        if (adElement.naturalWidth > adElement.naturalHeight) {
          adElement.style.width = "calc(100vw - 300px)";
          adElement.style.height = "auto";
          adElement.style.maxHeight = "100vh";
        } else {
          adElement.style.height = "100vh";
          adElement.style.width = "auto";
          adElement.style.maxWidth = "calc(100vw - 300px)";
        }

        // Oznacz interakcję użytkownika dla automatycznego odtwarzania kolejnych materiałów
        if (!userHasInteracted) {
          console.log('Image onload: Ustawiam userHasInteracted = true dla automatycznego odtwarzania kolejnych materiałów');
          userHasInteracted = true;
        }
      };

      adContainer.style.display = "flex";
      adContainer.style.justifyContent = "center";
      adContainer.style.alignItems = "center";
      adContainer.appendChild(adElement);
      break;

    case "video":
      console.log('rotateAd: Rozpoczynam tworzenie elementu video dla:', ad.name);
      console.log('rotateAd: ad.has_subtitles:', ad.has_subtitles, 'ad.subtitles_url:', ad.subtitles_url);
      const videoContainer = document.createElement("div");
      videoContainer.className = "video-container";
      videoContainer.style.display = "flex";
      videoContainer.style.justifyContent = "center";
      videoContainer.style.alignItems = "center";

      adElement = document.createElement("video");
      adElement.className = "ad-video";
      adElement.controls = false; // Wyłącz kontrolki dla normalnego działania
      adElement.setAttribute('controlsList', 'nodownload nofullscreen noremoteplayback');
      // Włącz autoplay tylko jeśli użytkownik już kliknął play wcześniej
      adElement.autoplay = userHasInteracted;
      adElement.muted = false;
      adElement.preload = "metadata"; // Zmienione z "auto" na "metadata" dla lepszej obsługi napisów
      adElement.loop = false;
      adElement.playsInline = true;
      adElement.crossOrigin = "anonymous"; // Przywrócone dla napisów
      adElement.style.display = "block";
      adElement.style.margin = "auto";

      // Ustaw głośność z ustawień wyświetlacza (przekonwertuj z 0-100 na 0-1)
      adElement.volume = displaySettings.volume / 100;
      console.log('rotateAd: Ustawiono głośność HTML5 video na:', adElement.volume, '(z ustawienia:', displaySettings.volume, ')');

      // Ustaw src NAJPIERW
      adElement.src = ad.media_url;
      console.log('rotateAd: Ustawiono src video na:', ad.media_url);

      // Dodaj napisy VTT jeśli są dostępne i włączone - PO ustawieniu src
      console.log('rotateAd: Sprawdzam napisy - displaySettings.subtitles:', displaySettings.subtitles, 'ad.has_subtitles:', ad.has_subtitles, 'ad.subtitles_url:', ad.subtitles_url);

      if (displaySettings.subtitles && ad.has_subtitles && ad.subtitles_url) {
        const track = document.createElement("track");
        track.kind = "captions"; // Zmienione z "subtitles" na "captions"
        // Upewnij się że URL jest pełny
        const fullSubtitlesUrl = ad.subtitles_url.startsWith('http') ? ad.subtitles_url : window.location.origin + ad.subtitles_url;
        track.src = fullSubtitlesUrl;
        console.log('rotateAd: Ustawiono track.src na:', fullSubtitlesUrl);
        track.srclang = "pl";
        track.label = "Polski";
        track.setAttribute("default", ""); // Poprawny sposób ustawienia default

        // Dodaj event listenery dla debugowania
        track.onload = function() {
          console.log('rotateAd: Napisy VTT załadowane pomyślnie:', ad.subtitles_url);
        };
        track.onerror = function(e) {
          console.error('rotateAd: Błąd ładowania napisów VTT:', ad.subtitles_url, e);
        };

        // Spróbuj appendChild
        adElement.appendChild(track);
        console.log('rotateAd: Dodano napisy VTT przez appendChild');
        console.log('rotateAd: Element video HTML PO appendChild:', adElement.outerHTML);

        // Jeśli appendChild nie zadziałał, spróbuj insertAdjacentHTML
        if (!adElement.outerHTML.includes('<track')) {
          console.log('rotateAd: appendChild nie zadziałał, próbuję insertAdjacentHTML');
          const trackHTML = `<track kind="captions" src="${fullSubtitlesUrl}" srclang="pl" label="Polski" default>`;
          adElement.insertAdjacentHTML('beforeend', trackHTML);
          console.log('rotateAd: Element video HTML PO insertAdjacentHTML:', adElement.outerHTML);
        }

        // Natychmiast spróbuj włączyć napisy
        setTimeout(() => {
          console.log('rotateAd: Sprawdzam textTracks po 100ms...');
          console.log('rotateAd: textTracks length:', adElement.textTracks ? adElement.textTracks.length : 'undefined');
          if (adElement.textTracks && adElement.textTracks.length > 0) {
            console.log('rotateAd: Track 0 readyState:', adElement.textTracks[0].readyState);
            console.log('rotateAd: Track 0 mode przed zmianą:', adElement.textTracks[0].mode);
            adElement.textTracks[0].mode = 'showing';
            console.log('rotateAd: Track 0 mode po zmianie:', adElement.textTracks[0].mode);
          }
        }, 100);
      } else {
        console.log('rotateAd: Napisy nie będą wyświetlane - warunki nie spełnione');
      }

      // Włącz napisy po załadowaniu metadanych
      adElement.addEventListener('loadedmetadata', function() {
        console.log('rotateAd: Metadane video załadowane');
        if (adElement.textTracks && adElement.textTracks.length > 0) {
          console.log('rotateAd: Znaleziono', adElement.textTracks.length, 'ścieżek tekstowych');
          for (let i = 0; i < adElement.textTracks.length; i++) {
            console.log('rotateAd: Track', i, '- kind:', adElement.textTracks[i].kind, 'language:', adElement.textTracks[i].language, 'mode:', adElement.textTracks[i].mode);
          }
          adElement.textTracks[0].mode = 'showing';
          console.log('rotateAd: Włączono wyświetlanie napisów, mode:', adElement.textTracks[0].mode);
        } else {
          console.log('rotateAd: Brak ścieżek tekstowych');
        }
      });

      // Dodaj event listener dla zmian w track
      adElement.addEventListener('loadstart', function() {
        console.log('rotateAd: Video loadstart event');
      });

      adElement.addEventListener('canplay', function() {
        console.log('rotateAd: Video canplay event');
        // Spróbuj ponownie włączyć napisy
        if (adElement.textTracks && adElement.textTracks.length > 0) {
          adElement.textTracks[0].mode = 'showing';
          console.log('rotateAd: Ponownie włączono napisy w canplay');
        }
        // Zastosuj ustawienia wielkości czcionki napisów
        setTimeout(() => {
          console.log('rotateAd: Stosowanie ustawień wielkości czcionki napisów w canplay...');
          updateAllPlayersSubtitleFontSize();
        }, 100);
      });

      adElement.onerror = function () {
        console.error("Błąd podczas ładowania pliku wideo:", ad.media_url);
        setTimeout(function () {
          rotateAd();
        }, 1000);
      };

      adElement.onloadedmetadata = function () {
        if (adElement.videoWidth > adElement.videoHeight) {
          adElement.style.width = "calc(100vw - 300px)";
          adElement.style.height = "auto";
          adElement.style.maxHeight = "100vh";
        } else {
          adElement.style.height = "100vh";
          adElement.style.width = "auto";
          adElement.style.maxWidth = "calc(100vw - 300px)";
        }

        // Automatycznie odtwarzaj jeśli użytkownik już kliknął play wcześniej
        if (userHasInteracted) {
          console.log('Video onloadedmetadata: Automatyczne odtwarzanie włączone - użytkownik już kliknął play');
          adElement.play().then(() => {
            isVideoPlaying = true;
            // Dodaj event listener dla zakończenia wideo
            adElement.addEventListener("ended", function () {
              console.log("[Video] Zakończono odtwarzanie video (autoplay)");
              isVideoPlaying = false;
              recordAdView(ad.id, adDuration)
                .then(() => {
                  // Po zarejestrowaniu wyświetlenia i pobraniu nowego materiału, przejdź do następnego
                  rotateAd();
                })
                .catch(error => {
                  console.error('Błąd podczas rejestrowania wyświetlenia video:', error);
                  // W przypadku błędu, mimo to przejdź do następnego materiału
                  rotateAd();
                });
            }, { once: true });
          }).catch(function (error) {
            console.error("Błąd podczas automatycznego odtwarzania wideo:", error);
            isVideoPlaying = false;
            setTimeout(function () {
              rotateAd();
            }, 1000);
          });
        }
      };

      // Pokazuj przycisk play tylko jeśli autoplay jest wyłączony lub użytkownik jeszcze nie kliknął
      const playButton = document.createElement("div");
      playButton.className = "play-button";
      playButton.innerHTML = '<i class="fas fa-play"></i>';
      playButton.style.zIndex = "1000";

      // Ukryj przycisk play jeśli autoplay jest włączony i użytkownik już kliknął wcześniej
      if (userHasInteracted) {
        playButton.classList.add("hidden");
      }

      playButton.addEventListener("click", function () {
        if (isVideoPlaying) return;

        isVideoPlaying = true;
        videoPlaybackStarted = true; // Oznacz że rozpoczęto odtwarzanie
        userHasInteracted = true; // Oznacz że użytkownik kliknął play

        adElement.currentTime = 0;
        adElement
          .play()
          .then(function () {
            playButton.classList.add("hidden");

            adElement.addEventListener(
              "ended",
              function () {
                console.log("[Video] Zakończono odtwarzanie video");
                isVideoPlaying = false;
                recordAdView(ad.id, adDuration)
                  .then(() => {
                    // Po zarejestrowaniu wyświetlenia i pobraniu nowego materiału, przejdź do następnego
                    rotateAd();
                  })
                  .catch(error => {
                    console.error('Błąd podczas rejestrowania wyświetlenia video:', error);
                    // W przypadku błędu, mimo to przejdź do następnego materiału
                    rotateAd();
                  });
              },
              { once: true },
            );
          })
          .catch(function (error) {
            console.error("Błąd podczas odtwarzania wideo:", error);
            isVideoPlaying = false;
            setTimeout(function () {
              rotateAd();
            }, 1000);
          });
      });

      videoContainer.appendChild(playButton);

      videoContainer.appendChild(adElement);
      adContainer.style.display = "flex";
      adContainer.style.justifyContent = "center";
      adContainer.style.alignItems = "center";
      adContainer.appendChild(videoContainer);

      // Dodaj napisy AFTER dodania do DOM
      if (displaySettings.subtitles && ad.has_subtitles && ad.subtitles_url) {
        setTimeout(() => {
          console.log('rotateAd: Ponowne sprawdzenie napisów po dodaniu do DOM...');
          if (adElement.textTracks && adElement.textTracks.length > 0) {
            console.log('rotateAd: Znaleziono textTracks po dodaniu do DOM:', adElement.textTracks.length);
            adElement.textTracks[0].mode = 'showing';
            console.log('rotateAd: Włączono napisy po dodaniu do DOM');
          } else {
            console.log('rotateAd: Brak textTracks po dodaniu do DOM');
          }
        }, 500);
      }

      // Zastosuj ustawienia wielkości czcionki napisów dla nowego filmu
      setTimeout(() => {
        console.log('rotateAd: Stosowanie ustawień wielkości czcionki napisów dla nowego filmu...');
        updateAllPlayersSubtitleFontSize();
      }, 600);
      break;

    case "youtube":
      const youtubeContainer = document.createElement("div");
      youtubeContainer.id = "youtubePlayer";
      youtubeContainer.className = "ad-youtube";
      adContainer.appendChild(youtubeContainer);

      // Dodaj przycisk play dla YouTube tylko jeśli użytkownik jeszcze nie kliknął
      const youtubePlayButton = document.createElement("div");
      youtubePlayButton.className = "play-button";
      youtubePlayButton.innerHTML = '<i class="fas fa-play"></i>';
      youtubePlayButton.style.zIndex = "1000";
      
      // Ukryj przycisk play jeśli użytkownik już kliknął wcześniej
      if (userHasInteracted) {
        youtubePlayButton.classList.add("hidden");
      }
      
      youtubePlayButton.addEventListener("click", function () {
        if (isVideoPlaying) return;
        
        isVideoPlaying = true;
        userHasInteracted = true; // Oznacz że użytkownik kliknął play
        
        if (ytPlayer && typeof ytPlayer.playVideo === 'function') {
          ytPlayer.playVideo();
          youtubePlayButton.classList.add("hidden");
        }
      });
      
      youtubeContainer.appendChild(youtubePlayButton);

      ytPlayer = new YT.Player("youtubePlayer", {
        height: "100%",
        width: "100%",
        videoId: ad.youtube_id,
        playerVars: {
          autoplay: userHasInteracted ? 1 : 0, // Włącz autoplay tylko jeśli użytkownik już kliknął play
          controls: 0,
          mute: 0, // Nie wyciszamy, będziemy kontrolować głośność
          rel: 0,
        },
        events: {
          onReady: function (event) {
            // Ustaw głośność z ustawień wyświetlacza (przekonwertuj z 0-100 na 0-100)
            event.target.setVolume(displaySettings.volume);
            console.log('YouTube onReady: Ustawiono głośność na:', displaySettings.volume, '(rzeczywista:', event.target.getVolume(), ')');
            
            // Automatycznie odtwarzaj jeśli użytkownik już kliknął play wcześniej
            if (userHasInteracted) {
              console.log('YouTube onReady: Automatyczne odtwarzanie włączone - użytkownik już kliknął play');
              event.target.playVideo();
              youtubePlayButton.classList.add("hidden");
            } else {
              console.log('YouTube onReady: Autoodtwarzanie wyłączone - oczekiwanie na interakcję użytkownika');
            }
          },
          onStateChange: function (event) {
            if (event.data === YT.PlayerState.PLAYING) {
              isVideoPlaying = true;
            } else if (event.data === YT.PlayerState.ENDED) {
              isVideoPlaying = false;
              recordAdView(ad.id, adDuration)
                .then(() => {
                  // Po zarejestrowaniu wyświetlenia i pobraniu nowego materiału, przejdź do następnego
                  rotateAd();
                })
                .catch(error => {
                  console.error('Błąd podczas rejestrowania wyświetlenia YouTube:', error);
                  // W przypadku błędu, mimo to przejdź do następnego materiału
                  rotateAd();
                });
            } else if (event.data === YT.PlayerState.PAUSED) {
              isVideoPlaying = false;
            }
          },
          onError: function (event) {
            console.error(
              "Błąd podczas ładowania filmu YouTube:",
              ad.youtube_id,
            );
            setTimeout(function () {
              rotateAd();
            }, 1000);
          },
        },
      });
      break;

    default:
      adElement = document.createElement("div");
      adElement.className = "ad-container";
      adElement.innerHTML = `<h2>${ad.name}</h2><p>${ad.description}</p>`;
      adContainer.appendChild(adElement);

      // Oznacz interakcję użytkownika dla automatycznego odtwarzania kolejnych materiałów
      if (!userHasInteracted) {
        console.log('Default case: Ustawiam userHasInteracted = true dla automatycznego odtwarzania kolejnych materiałów');
        userHasInteracted = true;
      }
  }

  // Jeśli to nie jest YouTube (obsługiwany przez zdarzenia) lub wideo (obsługiwane przez onloadedmetadata)
  if (ad.media_type !== "youtube" && ad.media_type !== "video") {
    setTimeout(function () {
      recordAdView(ad.id, adDuration)
        .then(() => {
          // Po zarejestrowaniu wyświetlenia i pobraniu nowego materiału, przejdź do następnego
          rotateAd();
        })
        .catch(error => {
          console.error('Błąd podczas rejestrowania wyświetlenia:', error);
          // W przypadku błędu, mimo to przejdź do następnego materiału
          rotateAd();
        });
    }, adDuration * 1000);
  }
}

function rotateAd() {
  console.log('rotateAd: Rozpoczynam rotację do następnego materiału');
  console.log('rotateAd: currentAd:', currentAd);

  if (!currentAd) {
    console.warn('rotateAd: Brak currentAd, przerywam rotację');
    return;
  }

  // Po prostu wyświetl kolejny materiał (który został już ustawiony przez recordAdView)
  console.log('rotateAd: Wywołuję showNextAd()');
  showNextAd();
}

// Funkcja do aktualizacji zawartości wizyt bez przebudowywania wyświetlacza
function updateCurrentAppointmentContent() {
  console.log('updateCurrentAppointmentContent: Aktualizuję zawartość wizyt');
  if (!queueData || !queueData.doctors) return;

  const doctorsWithCurrent = queueData.doctors.filter(doctor => doctor.current);
  console.log('updateCurrentAppointmentContent: Lekarzy z aktualnymi wizytami:', doctorsWithCurrent.length);

  doctorsWithCurrent.forEach((doctor, index) => {
    const doctorContainer = document.querySelector(`.queue-rooms-container[data-room-index="${index}"]`);
    if (!doctorContainer) {
      console.log('updateCurrentAppointmentContent: Nie znaleziono kontenera dla indeksu', index);
      return;
    }

    console.log('updateCurrentAppointmentContent: Aktualizuję lekarza:', doctor.name);

    // Aktualizuj informacje o aktualnej wizycie
    const currentPatientElement = doctorContainer.querySelector('.current-patient');
    const currentNumberElement = doctorContainer.querySelector('.current-number');

    if (currentPatientElement && doctor.current) {
      const firstName = doctor.current.patient_name.split(" ")[0];
      const patientName = `p. ${DataHelper.normalizeName(firstName)}`;
      currentPatientElement.textContent = patientName;
      console.log('updateCurrentAppointmentContent: Zaktualizowano pacjenta na:', patientName);
    }
    if (currentNumberElement && doctor.current) {
      currentNumberElement.textContent = doctor.current.number;
      console.log('updateCurrentAppointmentContent: Zaktualizowano numer na:', doctor.current.number);
    }

    // Aktualizuj listę oczekujących - znajdź kontener z oczekującymi
    const waitingElement = doctorContainer.querySelector('.queue-waiting');
    if (waitingElement && doctor.waiting) {
      // Usuń stare numery oczekujących (zachowaj label)
      const oldNumbers = waitingElement.querySelectorAll('.queue-number');
      oldNumbers.forEach(el => el.remove());

      // Dodaj nowe numery oczekujących
      doctor.waiting.forEach((appointment) => {
        const appointmentElement = document.createElement("div");
        appointmentElement.className = "queue-number";

        const time = appointment.number || appointment.appointment_time;
        const firstName = appointment.patient_name.split(" ")[0];
        const patientName = `p. ${DataHelper.normalizeName(firstName)}`;

        appointmentElement.innerHTML = `
          <span class="number">${time}</span>
          <span class="patient">${patientName}</span>
        `;
        waitingElement.appendChild(appointmentElement);
      });
      console.log('updateCurrentAppointmentContent: Zaktualizowano listę oczekujących');
    }
  });
}

// Funkcje powiadomień o zmianie wizyty
function checkForCurrentAppointmentChanges(oldData, newData) {
  console.log('checkForCurrentAppointmentChanges: Sprawdzam zmiany wizyt');
  console.log('checkForCurrentAppointmentChanges: oldData =', oldData);
  console.log('checkForCurrentAppointmentChanges: newData =', newData);
  
  if (!oldData || !oldData.doctors || !newData || !newData.doctors) {
    console.log('checkForCurrentAppointmentChanges: Brak danych, przerywam');
    return;
  }

  // Sprawdź każdego lekarza w nowych danych
  newData.doctors.forEach(newDoctor => {
    if (!newDoctor.current) return;

    console.log('checkForCurrentAppointmentChanges: Sprawdzam lekarza', newDoctor.id, 'z aktualną wizytą', newDoctor.current.id);

    // Znajdź tego samego lekarza w starych danych
    const oldDoctor = oldData.doctors.find(d => d.id === newDoctor.id);

    if (!oldDoctor || !oldDoctor.current) {
      // Nowy lekarz z aktualną wizytą lub lekarz który wcześniej nie miał aktualnej wizyty
      console.log('checkForCurrentAppointmentChanges: Nowa wizyta dla lekarza', newDoctor.id, '- pokazuję powiadomienie');
      showAppointmentNotification(newDoctor);
    } else if (oldDoctor.current.id !== newDoctor.current.id) {
      // Ten sam lekarz, ale inna wizyta
      console.log('checkForCurrentAppointmentChanges: Zmiana wizyty dla lekarza', newDoctor.id, 'z', oldDoctor.current.id, 'na', newDoctor.current.id, '- pokazuję powiadomienie');
      showAppointmentNotification(newDoctor);
    } else {
      console.log('checkForCurrentAppointmentChanges: Brak zmian dla lekarza', newDoctor.id);
    }
  });
}

// Funkcja do dodawania powiadomienia do kolejki
function queueAppointmentNotification(doctorData) {
  console.log('queueAppointmentNotification: Dodaję powiadomienie do kolejki dla', doctorData);

  if (!doctorData || !doctorData.current) {
    console.log('queueAppointmentNotification: Brak danych wizyty, pomijam');
    return;
  }

  // Sprawdź czy to powiadomienie już nie jest w kolejce (deduplikacja)
  const isDuplicate = notificationQueue.some(queuedDoctor =>
    queuedDoctor.id === doctorData.id &&
    queuedDoctor.current &&
    queuedDoctor.current.id === doctorData.current.id
  );

  if (isDuplicate) {
    console.log('queueAppointmentNotification: Powiadomienie już jest w kolejce, pomijam duplikat');
    return;
  }

  // Dodaj powiadomienie do kolejki
  notificationQueue.push(doctorData);
  console.log('queueAppointmentNotification: Kolejka powiadomień ma teraz', notificationQueue.length, 'elementów');

  // Jeśli nie ma aktywnego powiadomienia, rozpocznij przetwarzanie kolejki
  if (!isNotificationActive) {
    processNotificationQueue();
  }
}

// Funkcja do przetwarzania kolejki powiadomień
function processNotificationQueue() {
  console.log('processNotificationQueue: Przetwarzam kolejkę powiadomień');

  // Jeśli kolejka jest pusta, zakończ
  if (notificationQueue.length === 0) {
    console.log('processNotificationQueue: Kolejka pusta, kończę przetwarzanie');
    isNotificationActive = false;
    return;
  }

  // Oznacz że powiadomienie jest aktywne
  isNotificationActive = true;

  // Pobierz pierwsze powiadomienie z kolejki
  const doctorData = notificationQueue.shift();
  console.log('processNotificationQueue: Pokazuję powiadomienie dla', doctorData);

  // Wyświetl powiadomienie
  showAppointmentNotificationImmediate(doctorData);
}

// Funkcja do natychmiastowego wyświetlenia powiadomienia (bez kolejkowania)
function showAppointmentNotificationImmediate(doctorData) {
  console.log('showAppointmentNotificationImmediate: Pokazuję powiadomienie dla', doctorData);

  const notification = document.getElementById('appointmentNotification');
  const doctorElement = document.getElementById('notificationDoctor');
  const patientElement = document.getElementById('notificationPatient');
  const timeElement = document.getElementById('notificationTime');
  const roomElement = document.getElementById('notificationRoom');
  const doctorPhotoElement = document.getElementById('notificationDoctorPhoto');

  if (!notification || !doctorData.current) {
    console.log('showAppointmentNotificationImmediate: Brak elementów DOM lub danych wizyty');
    // Przejdź do następnego powiadomienia w kolejce
    setTimeout(() => processNotificationQueue(), 100);
    return;
  }

  // Funkcja do formatowania imienia pacjenta (p. Imię)
  function formatPatientName(fullName) {
    if (!fullName) return 'Nieznany pacjent';

    const parts = fullName.trim().split(' ');
    if (parts.length === 0) return 'Nieznany pacjent';

    const firstName = parts[0];
    return `p. ${DataHelper.normalizeName(firstName)}`;
  }

  // Wypełnij dane powiadomienia
  const doctorName = DataHelper.normalizeName(doctorData.name) || 'Lekarz';
  const roomName = doctorData.room ? doctorData.room.name : 'Gabinet';
  const doctorPhoto = doctorData.photo_url || '/uploads/doctors/default-avatar.webp';

  doctorElement.textContent = doctorName;
  patientElement.textContent = formatPatientName(doctorData.current.patient_name);
  timeElement.textContent = `z ${doctorData.current.appointment_time}`;
  roomElement.textContent = roomName;
  doctorPhotoElement.src = doctorPhoto;
  doctorPhotoElement.onerror = function() {
    this.onerror = null;
    this.src = '/uploads/doctors/default-avatar.webp';
    this.setAttribute('data-fallback', 'true');
  };

  // Pauzuj video podczas wyświetlania powiadomienia
  pauseAllVideos();

  // Odczytaj powiadomienie przez syntezator mowy
  speakNotification(doctorData, roomName);

  // Pokaż powiadomienie
  console.log('showAppointmentNotificationImmediate: Dodaję klasę show do powiadomienia');
  notification.classList.add('show');

  // Ukryj powiadomienie po 15 sekundach i przejdź do następnego
  currentNotificationTimeout = setTimeout(() => {
    console.log('showAppointmentNotificationImmediate: Ukrywam powiadomienie po 15 sekundach');
    hideAppointmentNotification();
    // Wznów video po ukryciu powiadomienia
    resumeAllVideos();
    // Przejdź do następnego powiadomienia w kolejce po krótkim opóźnieniu
    setTimeout(() => processNotificationQueue(), 500);
  }, 15000);
}

// Funkcja zastępująca oryginalną showAppointmentNotification - teraz używa kolejki
function showAppointmentNotification(doctorData) {
  queueAppointmentNotification(doctorData);
}

function hideAppointmentNotification() {
  console.log('hideAppointmentNotification: Ukrywam powiadomienie');

  const notification = document.getElementById('appointmentNotification');
  if (notification) {
    notification.classList.remove('show');
  }

  // Anuluj aktualny timeout jeśli istnieje
  if (currentNotificationTimeout) {
    clearTimeout(currentNotificationTimeout);
    currentNotificationTimeout = null;
  }

  // Zatrzymaj syntezator mowy
  if (speechSynthesis.speaking) {
    speechSynthesis.cancel();
  }
}

// Funkcja do czyszczenia kolejki powiadomień (np. przy zmianie daty)
function clearNotificationQueue() {
  console.log('clearNotificationQueue: Czyszczę kolejkę powiadomień');

  notificationQueue = [];
  isNotificationActive = false;

  // Anuluj aktualny timeout
  if (currentNotificationTimeout) {
    clearTimeout(currentNotificationTimeout);
    currentNotificationTimeout = null;
  }

  // Ukryj aktualnie wyświetlane powiadomienie
  hideAppointmentNotification();

  // Wznów video
  resumeAllVideos();
}

// Funkcje do pauzowania i wznawiania video
function pauseAllVideos() {
  console.log('pauseAllVideos: Pauzuję wszystkie video');
  
  // Pauzuj YouTube player
  if (ytPlayer && typeof ytPlayer.pauseVideo === 'function') {
    try {
      // Zapisz aktualną głośność przed pauzowaniem
      ytPlayer.savedVolume = ytPlayer.getVolume();
      ytPlayer.pauseVideo();
      console.log('pauseAllVideos: YouTube video zostało spauzowane z głośnością:', ytPlayer.savedVolume);
    } catch (error) {
      console.error('pauseAllVideos: Błąd pauzowania YouTube video:', error);
    }
  }
  
  // Pauzuj HTML5 video
  const videoElements = document.querySelectorAll('video');
  videoElements.forEach(video => {
    if (!video.paused) {
      // Zapisz aktualną głośność przed pauzowaniem
      video.dataset.savedVolume = video.volume;
      video.pause();
      console.log('pauseAllVideos: HTML5 video zostało spauzowane z głośnością:', video.volume);
    }
  });
}

// Funkcja do odczytywania powiadomienia przez syntezator mowy
function speakNotification(doctorData, roomName) {
  console.log('speakNotification: Rozpoczynam odczyt powiadomienia');
  
  // Sprawdź czy przeglądarka obsługuje Web Speech API
  if (!('speechSynthesis' in window)) {
    console.warn('speakNotification: Przeglądarka nie obsługuje syntezatora mowy');
    return;
  }

  // Zatrzymaj poprzednie odczyty
  speechSynthesis.cancel();

  // Przygotuj tekst do odczytu
  const fullName = doctorData.current.patient_name;
  const firstName = fullName ? fullName.split(" ")[0] : 'Nieznany';
  const patientName = DataHelper.normalizeName(firstName);
  const appointmentTime = doctorData.current.appointment_time;
  
  // Przygotuj tekst powiadomienia z kropkami
  const text = `Zapraszam. ${patientName} z godziny ${appointmentTime}. ${roomName}.`;
  
  console.log('speakNotification: Tekst do odczytu:', text);

  // Funkcja do odczytu z powtórzeniem
  function speakWithRepeat() {
    // Utwórz obiekt SpeechSynthesisUtterance
    const utterance = new SpeechSynthesisUtterance(text);
    
    // Ustawienia syntezatora mowy
    utterance.lang = 'pl-PL';
    utterance.rate = 1.0; // Tempo 1
    utterance.pitch = 0.6; // Wysokość 0.6
    // Użyj głośności z ustawień wyświetlacza (przekonwertuj z 0-100 na 0-1)
    utterance.volume = displaySettings.volume / 100;
    
    // Obsługa zdarzeń
    utterance.onstart = function() {
      console.log('speakNotification: Rozpoczęto odczyt');
    };
    
    utterance.onend = function() {
      console.log('speakNotification: Zakończono odczyt');
      
      // Po 2 sekundach powtórz
      setTimeout(() => {
        console.log('speakNotification: Powtarzam odczyt po 2 sekundach');
        const repeatUtterance = new SpeechSynthesisUtterance(text);
        repeatUtterance.lang = 'pl-PL';
        repeatUtterance.rate = 1.0;
        repeatUtterance.pitch = 0.6;
        // Użyj głośności z ustawień wyświetlacza (przekonwertuj z 0-100 na 0-1)
        repeatUtterance.volume = displaySettings.volume / 100;
        
        repeatUtterance.onstart = function() {
          console.log('speakNotification: Rozpoczęto powtórzenie');
        };
        
        repeatUtterance.onend = function() {
          console.log('speakNotification: Zakończono powtórzenie');
        };
        
        repeatUtterance.onerror = function(event) {
          console.error('speakNotification: Błąd powtórzenia:', event.error);
        };
        
        speechSynthesis.speak(repeatUtterance);
      }, 2000);
    };
    
    utterance.onerror = function(event) {
      console.error('speakNotification: Błąd syntezatora mowy:', event.error);
    };

    // Rozpocznij odczyt
    speechSynthesis.speak(utterance);
  }

  // Rozpocznij odczyt z powtórzeniem
  speakWithRepeat();
}

// Funkcja do określania płci na podstawie imienia
function isFemaleName(name) {
  // Lista typowych żeńskich imion w Polsce
  const femaleNames = [
    'anna', 'maria', 'katarzyna', 'małgorzata', 'agnieszka', 'krystyna', 'barbara', 'ewa', 'elżbieta', 'zofia',
    'joanna', 'magdalena', 'monika', 'dorota', 'teresa', 'halina', 'helena', 'beata', 'danuta', 'jolanta',
    'grażyna', 'stanisława', 'irena', 'mariola', 'marzena', 'bożena', 'wiesława', 'genowefa', 'kazimiera', 'alina',
    'aleksandra', 'natalia', 'patrycja', 'sylwia', 'marlena', 'karolina', 'paulina', 'wiktoria', 'oliwia', 'zuzanna',
    'maja', 'amelia', 'lena', 'julia', 'hanna', 'laura', 'nikola', 'marcelina', 'martyna', 'kinga'
  ];
  
  // Pobierz pierwsze imię (przed spacją)
  const firstName = name.toLowerCase().split(' ')[0];
  
  return femaleNames.includes(firstName);
}

function resumeAllVideos() {
  console.log('resumeAllVideos: Wznawiam wideo z uwzględnieniem stanu interakcji użytkownika');
  
  // Wznawiaj odtwarzanie tylko jeśli użytkownik już kliknął play wcześniej
  if (userHasInteracted) {
    console.log('resumeAllVideos: Użytkownik już kliknął play - wznawiam odtwarzanie');
    
    // Wznów YouTube player
    if (ytPlayer && typeof ytPlayer.playVideo === 'function') {
      try {
        ytPlayer.setVolume(displaySettings.volume);
        ytPlayer.playVideo();
        console.log('resumeAllVideos: Wznawiam YouTube video');
      } catch (error) {
        console.error('resumeAllVideos: Błąd wznawiania YouTube video:', error);
      }
    }
    
    // Wznów HTML5 video
    const videoElements = document.querySelectorAll('video');
    videoElements.forEach(video => {
      video.volume = displaySettings.volume / 100;
      if (video.paused && !video.ended) {
        video.play().then(() => {
          console.log('resumeAllVideos: Wznawiam HTML5 video');
        }).catch(error => {
          console.error('resumeAllVideos: Błąd wznawiania HTML5 video:', error);
        });
      }
    });
  } else {
    console.log('resumeAllVideos: Użytkownik nie kliknął jeszcze play - tylko aktualizuję głośność');
    
    // Zaktualizuj głośność YouTube player bez wznawiania
    if (ytPlayer && typeof ytPlayer.setVolume === 'function') {
      try {
        ytPlayer.setVolume(displaySettings.volume);
        console.log('resumeAllVideos: Zaktualizowano głośność YouTube na:', displaySettings.volume);
      } catch (error) {
        console.error('resumeAllVideos: Błąd aktualizacji głośności YouTube:', error);
      }
    }
    
    // Zaktualizuj głośność HTML5 video bez wznawiania
    const videoElements = document.querySelectorAll('video');
    videoElements.forEach(video => {
      video.volume = displaySettings.volume / 100;
      console.log('resumeAllVideos: Zaktualizowano głośność HTML5 video na:', displaySettings.volume);
    });
  }
}

// Inicjalizacja
document.addEventListener("DOMContentLoaded", function () {
  console.log("DOMContentLoaded: Inicjalizacja wyświetlacza");
  console.log("DOMContentLoaded: display =", display);
  console.log("DOMContentLoaded: queueSystemEnabled =", queueSystemEnabled);
  console.log("DOMContentLoaded: currentAd =", currentAd);
  console.log("DOMContentLoaded: currentDisplayDate =", currentDisplayDate);

  // Zawsze inicjalizuj system kolejkowy
  console.log("DOMContentLoaded: Wywołuję checkQueueStatus");
  checkQueueStatus();

  // Ustaw interwał tylko jeśli nie jest już ustawiony
  if (!queueCheckInterval) {
    console.log("DOMContentLoaded: Ustawiam interval dla checkQueueStatus (co 3 sekundy)");
    queueCheckInterval = setInterval(checkQueueStatus, 3000);
    console.log("DOMContentLoaded: System sprawdzania zmian uruchomiony. Sprawdzanie co 3 sekundy.");
  } else {
    console.log("DOMContentLoaded: Interwał sprawdzania kolejki już istnieje, pomijam ustawienie");
  }

  // Pokaż reklamę tylko jeśli jest dostępna
  if (currentAd) {
    console.log('DOMContentLoaded: Wyświetlam pierwszy materiał (wymaga ręcznego uruchomienia)');
    showNextAd();
  } else {
    // Jeśli nie ma reklam, przygotuj interfejs dla samego systemu kolejkowego
    const contentContainer = document.getElementById("contentContainer");
    if (contentContainer) {
      contentContainer.parentElement.classList.add("display-with-queue");

      // Utwórz panel boczny kolejki
      const queueSidebar = document.createElement("div");
      queueSidebar.className = "queue-sidebar";
      contentContainer.parentElement.insertBefore(
        queueSidebar,
        contentContainer,
      );

      // Zmień klasę kontenera treści
      contentContainer.className = "queue-ad-container";

      // Pusty kontener bez dodatkowych komunikatów
      contentContainer.innerHTML = "";
    }
  }

  sendHeartbeat();
  setInterval(sendHeartbeat, 60000);
  
  // Inicjalizuj nawigację dat
  updateDateDisplay();
  
  // Załaduj ustawienia wyświetlacza (funkcja updateAllPlayersSubtitleFontSize zostanie wywołana automatycznie po załadowaniu)
  loadDisplaySettings();
  
  // Załaduj dane kolejki dla dzisiejszej daty (pokazuje też nawigację dat)
  if (queueSystemEnabled) {
    loadQueueDataForDate();
  }
});

// Funkcja dla YouTube API
function onYouTubeIframeAPIReady() {
  if (
    currentAd &&
    currentAd.media_type === "youtube"
  ) {
    showNextAd();
  }
}

// Funkcje nawigacji dat
window.changeDate = function(days) {
  console.log('changeDate: Zmieniam datę o', days, 'dni. Obecna data:', currentDisplayDate);
  
  const newDate = new Date(currentDisplayDate);
  newDate.setDate(newDate.getDate() + days);
  currentDisplayDate = newDate.toISOString().split('T')[0];
  
  console.log('changeDate: Nowa data:', currentDisplayDate);
  
  updateDateDisplay();
  loadQueueDataForDate();
}

function updateDateDisplay() {
  const dateElement = document.getElementById('currentDateDisplay');
  if (dateElement) {
    const date = new Date(currentDisplayDate);
    
    // Zawsze pokazuj pełną datę w formacie DD.MM.YYYY
    const displayText = date.toLocaleDateString('pl-PL', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
    
    dateElement.textContent = displayText;
    
    // Aktualizuj stan przycisków
    const prevBtn = document.getElementById('prevDayBtn');
    const nextBtn = document.getElementById('nextDayBtn');
    
    if (prevBtn) {
      // Ogranicz do ostatnich 7 dni wstecz
      const minDate = new Date();
      minDate.setDate(minDate.getDate() - 7);
      prevBtn.disabled = currentDisplayDate <= minDate.toISOString().split('T')[0];
    }
    
    if (nextBtn) {
      // Ogranicz do następnych 7 dni
      const maxDate = new Date();
      maxDate.setDate(maxDate.getDate() + 7);
      const maxDateStr = maxDate.toISOString().split('T')[0];
      nextBtn.disabled = currentDisplayDate >= maxDateStr;
      console.log('nextBtn disabled:', nextBtn.disabled, 'currentDisplayDate:', currentDisplayDate, 'maxDate:', maxDateStr);
    }
  }
}

function loadQueueDataForDate() {
  console.log('loadQueueDataForDate: Ładuję dane dla daty:', currentDisplayDate);

  // Wyczyść kolejkę powiadomień przy zmianie daty
  clearNotificationQueue();

  // Pokaż nawigację dat tylko gdy system kolejkowy jest aktywny
  const dateNavigation = document.getElementById('dateNavigation');
  if (dateNavigation && queueSystemEnabled) {
    dateNavigation.style.display = 'flex';
  }
  
  // Pobierz dane kolejki dla wybranej daty
  console.log('loadQueueDataForDate: Pobieram dane z URL:', `/api/v2/queue/${display.id}?date=${currentDisplayDate}`);
  
  fetchWithRetry(`/api/v2/queue/${display.id}?date=${currentDisplayDate}`, {}, `queue-date-${currentDisplayDate}`)
    .then(response => {
      console.log('loadQueueDataForDate: Odpowiedź HTTP:', response.status);
      return response.json();
    })
    .then(response => {
      console.log('loadQueueDataForDate: Otrzymano dane dla daty:', currentDisplayDate);
      console.log('loadQueueDataForDate: Liczba lekarzy:', response.data?.doctors?.length || 0);
      console.log('loadQueueDataForDate: Lekarze z wizytami:', response.data?.doctors?.filter(d => d.current || (d.waiting && d.waiting.length > 0)).length || 0);
      processQueueData(response);
    })
    .catch(error => {
      console.error('Błąd podczas pobierania danych kolejki dla daty:', error);
    });
}
