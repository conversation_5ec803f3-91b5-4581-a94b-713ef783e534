<!DOCTYPE html>
<html lang="pl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wyświetlacz reklamowy - <?= htmlspecialchars(
                                        $display["display_name"],
                                    ) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/display/assets/css/display.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #000;
            color: #fff;
        }

        .display-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
        }

        .display-header {
            padding: 10px 15px;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #333;
            z-index: 10;
        }

        .display-content {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            height: 100vh;
        }

        .ad-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .ad-image {
            width: 100vw;
            height: 100vh;
            object-fit: contain;
            background: #000;
        }

        .ad-video {
            width: 100vw;
            height: 100vh;
            object-fit: contain;
            object-position: left;
            background: #000;
        }

        .ad-youtube {
            width: 100%;
            height: 100%;
        }

        .company-info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 18px;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
        }

        .no-ads {
            text-align: center;
            font-size: 24px;
            color: #ccc;
        }

        .fullscreen-btn {
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin-left: 10px;
        }

        .fullscreen-btn:hover {
            background-color: rgba(0, 0, 0, 0.8);
        }

        /* Styl podczas pełnego ekranu */
        :fullscreen,
        ::backdrop {
            background-color: black;
        }

        /* Style dla systemu kolejkowego */
        .display-with-queue {
            display: grid;
            grid-template-columns: 300px 1fr;
            grid-template-rows: 1fr;
            height: 100%;
        }

        .queue-sidebar {
            grid-column: 1;
            grid-row: 1;
            background-color: #1a1a1a;
            color: white;
            border-right: 1px solid #333;
            padding: 15px;
            overflow: hidden;
            width: 300px;
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            z-index: 10;
        }

        .queue-header {
            background-color: #2c3e50;
            color: white;
            padding: 10px 15px;
            margin: -15px -15px 15px -15px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
        }

        .queue-date {
            font-size: 14px;
            font-weight: normal;
            margin-top: 5px;
            opacity: 0.9;
            font-style: italic;
        }

        .queue-room {
            margin-bottom: 20px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #222;
        }

        .queue-room-header {
            background-color: #333;
            color: white;
            padding: 8px 12px;
            font-weight: bold;
            border-bottom: 1px solid #444;
        }

        .queue-current {
            padding: 15px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            background-color: #2c3e50;
            color: white;
        }

        .queue-waiting {
            padding: 10px;
            text-align: center;
        }

        .queue-number {
            padding: 5px 10px;
            margin-bottom: 5px;
            background-color: #333;
            border-radius: 3px;
            text-align: center;
        }

        .queue-ad-container {
            grid-column: 2;
            grid-row: 1;
            position: relative;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-center {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-grow: 1;
        }

        .header-right {
            display: flex;
            align-items: center;
        }

        .current-number-display {
            display: flex;
            align-items: center;
            font-size: 20px;
            color: #f39c12;
            margin-left: 15px;
        }

        .current-room-name {
            font-weight: bold;
            color: white;
            margin-right: 10px;
        }

        .current-number {
            font-weight: bold;
            font-size: 24px;
            color: #f39c12;
        }

        .queue-rooms-container {
            display: none;
        }

        .queue-rooms-container.active {
            display: block;
        }

        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border: 2px solid white;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 24px;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background-color: rgba(0, 0, 0, 0.9);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .play-button.hidden {
            display: none;
        }

        .video-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .ad-video {
            height: 100vh;
            width: auto;
            max-width: 100vw;
            display: block;
            margin: 0 auto;
            object-fit: contain;
            object-position: center;
        }

        /* Animacje wjazdu dla aktualnej wizyty i elementów panelu */
        .slide-in-left {
            animation: slideInLeft 0.5s;
        }

        .slide-in-right {
            animation: slideInRight 0.5s;
        }

        .slide-in-bottom {
            animation: slideInBottom 0.5s;
        }

        .slide-in-top {
            animation: slideInTop 0.5s;
        }

        @keyframes slideInLeft {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }

            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }

            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideInBottom {
            from {
                transform: translateY(100%);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideInTop {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Powiadomienie o zmianie wizyty */
        .appointment-notification {
            position: fixed;
            top: 0;
            left: 300px;
            /* Zaczyna się po systemie kolejkowym */
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: 5;
            /* Niższy niż system kolejkowy (10), ale wyższy niż reklamy (1) */
            color: white;
            text-align: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .appointment-notification.show {
            opacity: 1;
            visibility: visible;
        }

        .notification-content {
            background: rgba(0, 0, 0, 0.85);
            backdrop-filter: blur(15px);
            border-radius: 30px;
            margin-left: 300px;
            padding: 40px 60px;
            border: 4px solid rgba(255, 255, 255, 0.8);
            animation: pulse 2s infinite;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            width: 90%;
            flex-shrink: 0;
        }

        .notification-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
            color: #ffffff;
        }

        .notification-patient {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ffd700;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .notification-time {
            font-size: 32px;
            margin-bottom: 15px;
            color: #ffd700;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .notification-room {
            font-size: 28px;
            margin-bottom: 20px;
            color: #ffffff;
            font-weight: 600;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .notification-doctor-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .notification-doctor-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid rgba(255, 255, 255, 0.8);
        }

        .notification-doctor {
            font-size: 24px;
            color: #ffffff;
            font-weight: 600;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
            }

            50% {
                transform: scale(1.05);
                box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
            }

            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
            }
        }

        /* Responsywność powiadomienia - gdy nie ma systemu kolejkowego */
        .display-container:not(.display-with-queue) .appointment-notification {
            left: 0;
            /* Pełny ekran gdy nie ma systemu kolejkowego */
        }

        /* Box z datą i nawigacją - minimalistyczny */
        .date-navigation {
            position: fixed;
            bottom: 15px;
            right: 15px;
            background-color: rgba(0, 0, 0, 0.4);
            color: rgba(255, 255, 255, 0.7);
            padding: 6px 10px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            z-index: 1000;
            backdrop-filter: blur(3px);
            transition: all 0.3s ease;
        }

        .date-navigation:hover {
            background-color: rgba(0, 0, 0, 0.6);
            color: rgba(255, 255, 255, 0.9);
        }

        .date-navigation button {
            background: none;
            border: none;
            color: inherit;
            padding: 2px 4px;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.2s;
            opacity: 0.6;
        }

        .date-navigation button:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        .date-navigation button:disabled {
            opacity: 0.3;
            cursor: not-allowed;
            transform: none;
        }

        .date-display {
            font-weight: normal;
            min-width: 70px;
            text-align: center;
            font-size: 11px;
        }
    </style>
</head>

<body>
    <div class="display-container" id="displayContainer">
        <div id="contentContainer" class="display-content">
            <?php if (empty($campaigns)): ?>
                <div class="no-ads">
                    <p>Brak dostępnych reklam do wyświetlenia</p>
                    <p>System kolejkowy działa niezależnie od reklam</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Powiadomienie o zmianie wizyty -->
        <div class="appointment-notification" id="appointmentNotification">
            <div class="notification-content">
                <div class="notification-title">
                    <i class="fas fa-bell"></i> ZAPRASZAM
                </div>
                <div class="notification-patient" id="notificationPatient">
                    <!-- Imię pacjenta -->
                </div>
                <div class="notification-time" id="notificationTime">
                    <!-- Godzina wizyty -->
                </div>
                <div class="notification-room" id="notificationRoom">
                    <!-- Nazwa gabinetu -->
                </div>
                <div class="notification-doctor-info">
                    <img class="notification-doctor-photo" id="notificationDoctorPhoto" src="" alt="Lekarz">
                    <div class="notification-doctor" id="notificationDoctor">
                        <!-- Nazwa lekarza -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Nawigacja dat -->
        <div class="date-navigation" id="dateNavigation" style="display: flex;">
            <button id="prevDayBtn" onclick="changeDate(-1)">
                <i class="fas fa-chevron-left"></i>
            </button>
            <div class="date-display" id="currentDateDisplay">
                <!-- Data będzie ustawiona przez JavaScript -->
            </div>
            <button id="nextDayBtn" onclick="changeDate(1)">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://www.youtube.com/iframe_api"></script>
    <script>
        // Dane reklam z PHP do JavaScript
        const campaigns = <?= json_encode($campaigns) ?>; // Zachowujemy dla kompatybilności, ale nie używamy
        let currentAd = campaigns.length > 0 ? campaigns[0] : null; // Ustawiamy pierwszy materiał jako aktualny (let aby można było modyfikować)
        const display = <?= json_encode($display) ?>;
        const queueSystemEnabled = true;

        // Debug
        console.log('Display data:', display);
        console.log('Current ad data:', currentAd);
        console.log('Queue system enabled:', queueSystemEnabled);

        // Zdefiniuj zmienną displaySettings przed jej użyciem (globalna)
        window.displaySettings = {
            volume: 100, // Domyślna głośność 100%
            subtitles: true, // Domyślnie napisy włączone
            subtitleFontSize: 16 // Domyślna wielkość czcionki napisów 16px
        };

        // Zainicjuj ustawienia wyświetlacza z danych PHP
        if (display && display.volume !== undefined) {
            window.displaySettings.volume = display.volume;
        }
        if (display && display.show_subtitles !== undefined) {
            window.displaySettings.subtitles = display.show_subtitles;
        }
        if (display && display.subtitle_font_size !== undefined) {
            window.displaySettings.subtitleFontSize = display.subtitle_font_size;
        }

        console.log('Zainicjowano ustawienia wyświetlacza:', window.displaySettings);

        // Ustaw domyślną wartość zmiennej CSS
        document.documentElement.style.setProperty('--subtitle-font-size', window.displaySettings.subtitleFontSize + 'px');
        console.log('Ustawiono domyślną zmienną CSS --subtitle-font-size na:', window.displaySettings.subtitleFontSize + 'px');

        // Funkcja do zastosowania ustawień czcionki napisów (zdefiniowana tutaj, aby była dostępna)
        window.applySubtitleFontSize = function() {
            console.log('=== applySubtitleFontSize START ===');
            console.log('applySubtitleFontSize: Aktualizuję wielkość czcionki napisów na:', window.displaySettings.subtitleFontSize + 'px');
            console.log('applySubtitleFontSize: displaySettings:', window.displaySettings);

            // Ustaw zmienną CSS --subtitle-font-size w :root
            document.documentElement.style.setProperty('--subtitle-font-size', window.displaySettings.subtitleFontSize + 'px');
            console.log('applySubtitleFontSize: Ustawiono zmienną CSS --subtitle-font-size na:', window.displaySettings.subtitleFontSize + 'px');

            // Dodatkowe style dla YouTube (które nie używają ::cue)
            let subtitleStyles = document.getElementById('dynamic-subtitle-styles');
            if (!subtitleStyles) {
                subtitleStyles = document.createElement('style');
                subtitleStyles.id = 'dynamic-subtitle-styles';
                subtitleStyles.type = 'text/css';
                document.head.appendChild(subtitleStyles);
            }

            // Style dla YouTube napisów
            subtitleStyles.textContent = `
                .ytp-caption-segment {
                    font-size: ${window.displaySettings.subtitleFontSize}px !important;
                }
                .caption-window {
                    font-size: ${window.displaySettings.subtitleFontSize}px !important;
                }
            `;

            console.log('applySubtitleFontSize: Zaktualizowano zmienną CSS i style YouTube');
            console.log('=== applySubtitleFontSize END ===');
        }

        // Nie stosuj ustawień czcionki napisów tutaj - będą zastosowane po załadowaniu z API
        // applySubtitleFontSize();
    </script>
    <script src="/display/assets/js/data-helper.js"></script>
    <script src="/display/assets/js/display.js"></script>
</body>

</html>