<?php

class PublicDisplayController {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getPdo();
    }

    /**
     * Wyświetl publiczny interfejs wyświetlacza
     */
    public function show($displayCode) {
        // Jeś<PERSON> brak kodu, poka<PERSON> instrukcję parowania dla nowego wyświetlacza
        if ($displayCode === null || $displayCode === '') {
            $this->showPairingInstructions();
            return;
        }

        // Kod powinien być zawsze małymi literami
        $displayCode = strtolower($displayCode);

        // Pobierz informacje o wyświetlaczu
        $stmt = $this->db->prepare("
            SELECT d.*, d.volume, d.show_subtitles, d.subtitle_font_size
            FROM client_displays d
            WHERE d.display_code = ?
        ");
        $stmt->execute([$displayCode]);
        $display = $stmt->fetch();

        if (!$display) {
            // Je<PERSON><PERSON> wyświetlacz nie istnieje, przekieruj do ekranu parowania
            $this->showPairingInstructions();
            return;
        }

        // Sprawdź czy to żądanie sprawdzenia statusu parowania
        if (isset($_GET['check_pairing'])) {
            header('Content-Type: application/json');
            echo json_encode(['status' => $display['pairing_status']]);
            return;
        }

        // Sprawdź status parowania
        if ($display['pairing_status'] !== 'paired') {
            // Wyświetlacz nie jest sparowany - pokaż ekran parowania
            $this->showPairingScreen($display);
            return;
        }

        // Zaktualizuj heartbeat
        $stmt = $this->db->prepare("
            UPDATE client_displays
            SET last_heartbeat = datetime('now')
            WHERE id = ?
        ");
        $stmt->execute([$display['id']]);

        // Pobierz 5 materiałów z najmniejszą liczbą wyświetleń
        $stmt = $this->db->prepare("
            SELECT a.id, a.name, a.media_type, a.media_url, a.youtube_id,
                   a.duration, a.description, COALESCE(a.ads_views, 0) as views_count,
                   u.username as advertiser_name, 0 as views_last_hour
            FROM ads a
            LEFT JOIN users u ON a.advertiser_id = u.id
            WHERE a.approval_status = 'approved'
            ORDER BY COALESCE(a.ads_views, 0) ASC, a.id ASC
            LIMIT 5
        ");
        $stmt->execute();
        $topCampaigns = $stmt->fetchAll();

        // Losuj spośród 5 materiałów z najmniejszą liczbą wyświetleń
        if (!empty($topCampaigns)) {
            $randomIndex = array_rand($topCampaigns);
            $selectedCampaign = $topCampaigns[$randomIndex];

            // Loguj informacje o wyborze
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log(sprintf(
                    'Display Fair ad selection: Selected ad ID %d (views: %d) from %d top ads with lowest views',
                    $selectedCampaign['id'],
                    $selectedCampaign['views_count'],
                    count($topCampaigns)
                ));
            }

            $allCampaigns = [$selectedCampaign];
        } else {
            $allCampaigns = [];
        }

        // Dodaj informacje o napisach VTT dla każdej kampanii video
        foreach ($allCampaigns as &$campaign) {
            if ($campaign['media_type'] === 'video' && !empty($campaign['media_url'])) {
                $campaign['subtitles_url'] = $this->getSubtitlesUrl($campaign['media_url']);
                $campaign['has_subtitles'] = $this->checkSubtitlesExist($campaign['subtitles_url']);
            } else {
                $campaign['subtitles_url'] = null;
                $campaign['has_subtitles'] = false;
            }

            // Usuń pole views_count z odpowiedzi (tylko do wewnętrznego użytku)
            unset($campaign['views_count']);
        }

        // Użyj wszystkich kampanii bez filtrowania
        $campaigns = $allCampaigns;

        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Display: Liczba dostępnych kampanii: " . count($campaigns));
        }

        // System kolejkowy zawsze włączony dla wyświetlaczy
        $queueEnabled = true;

        // Renderuj widok bez layoutu
        $this->renderDisplay($display, $campaigns, $queueEnabled);
    }

    /**
     * Renderuj interfejs wyświetlacza (oryginalny widok)
     */
    private function renderDisplay($display, $campaigns, $queueEnabled) {
        // Załaduj oryginalny widok z admin
        extract([
            'display' => $display,
            'campaigns' => $campaigns,
            'queueEnabled' => $queueEnabled
        ]);

        include __DIR__ . '/../views/display_public.php';
    }

    /**
     * Wyświetl ekran parowania wyświetlacza
     */
    private function showPairingScreen($display) {
        // Sprawdź czy kod parowania jest aktualny
        $needNewCode = false;
        if (
            !$display['pairing_code'] ||
            ($display['pairing_expires_at'] && strtotime($display['pairing_expires_at']) < time())
        ) {
            $needNewCode = true;
        }

        if ($needNewCode) {
            // Wygeneruj nowy kod parowania (małe litery i cyfry)
            $pairingCode = $this->generatePairingCode();
            $expiresAt = date('Y-m-d H:i:s', time() + 900); // 15 minut

            $stmt = $this->db->prepare("
                UPDATE client_displays
                SET pairing_code = ?, pairing_status = 'pending', pairing_expires_at = ?
                WHERE id = ?
            ");
            $stmt->execute([$pairingCode, $expiresAt, $display['id']]);

            $display['pairing_code'] = $pairingCode;
            $display['pairing_expires_at'] = $expiresAt;
        }

        include __DIR__ . '/../views/pairing_screen.php';
    }

    /**
     * Generuj 6-znakowy kod parowania (małe litery i cyfry)
     */
    private function generatePairingCode() {
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $code = '';
        for ($i = 0; $i < 6; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $code;
    }

    /**
     * Wyświetl błąd
     */
    private function showError($message) {
?>
        <!DOCTYPE html>
        <html lang="pl">

        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Błąd wyświetlacza</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        </head>

        <body>
            <div class="container mt-5">
                <div class="alert alert-danger text-center">
                    <h4>Błąd wyświetlacza</h4>
                    <p><?php echo htmlspecialchars($message); ?></p>
                </div>
            </div>
        </body>

        </html>
    <?php
    }

    /**
     * Wyświetl instrukcję parowania dla nowego wyświetlacza
     */
    private function showPairingInstructions() {
        // Sprawdź czy istnieje aktywny kod parowania dla tego IP
        $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $display = $this->getOrCreatePendingDisplay($clientIp);

        // Sprawdź czy to żądanie sprawdzenia statusu parowania
        if (isset($_GET['check_pairing'])) {
            header('Content-Type: application/json');
            echo json_encode(['status' => $display['pairing_status']]);
            return;
        }

        // Sprawdź czy wyświetlacz został sparowany
        if ($display['pairing_status'] === 'paired') {
            // Przekieruj na normalny interfejs wyświetlacza
            header('Location: /display/' . $display['display_code']);
            exit;
        }

        // Wyświetl instrukcję parowania
        $this->renderPairingInstructions($display['pairing_code'], $display['pairing_expires_at']);
    }



    /**
     * Pobierz lub utwórz wyświetlacz oczekujący na parowanie dla danego IP
     */
    private function getOrCreatePendingDisplay($clientIp) {
        // Sprawdź czy istnieje aktywny kod parowania dla tego IP
        $stmt = $this->db->prepare("
            SELECT * FROM client_displays
            WHERE client_ip = ?
            AND (pairing_status = 'pending' OR pairing_status = 'paired')
            AND (pairing_expires_at IS NULL OR pairing_expires_at > datetime('now'))
            ORDER BY created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$clientIp]);
        $display = $stmt->fetch();

        if ($display) {
            return $display;
        }

        // Utwórz nowy wyświetlacz
        $pairingCode = $this->generatePairingCode();
        $displayId = $this->createNewDisplay($pairingCode, $clientIp);

        // Pobierz utworzony wyświetlacz
        $stmt = $this->db->prepare("SELECT * FROM client_displays WHERE id = ?");
        $stmt->execute([$displayId]);
        return $stmt->fetch();
    }

    /**
     * Utwórz nowy wyświetlacz w bazie danych
     */
    private function createNewDisplay($pairingCode, $clientIp = null) {
        $stmt = $this->db->prepare("
            INSERT INTO client_displays (
                display_code,
                display_name,
                pairing_code,
                pairing_status,
                pairing_expires_at,
                client_ip,
                created_at
            ) VALUES (?, ?, ?, 'pending', datetime('now', '+15 minutes'), ?, datetime('now'))
        ");

        // Wygeneruj unikalny kod wyświetlacza (6 losowych znaków)
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $displayCode = '';
        for ($i = 0; $i < 6; $i++) {
            $displayCode .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        // Upewnij się, że kod jest unikalny
        $existing = $this->db->prepare("SELECT id FROM client_displays WHERE display_code = ?");
        $existing->execute([$displayCode]);
        while ($existing->fetch()) {
            // Jeśli kod już istnieje, wygeneruj nowy
            $displayCode = '';
            for ($i = 0; $i < 6; $i++) {
                $displayCode .= $characters[rand(0, strlen($characters) - 1)];
            }
            $existing->execute([$displayCode]);
        }
        $displayName = 'Nowy wyświetlacz';

        $stmt->execute([$displayCode, $displayName, $pairingCode, $clientIp]);

        return $this->db->lastInsertId();
    }

    /**
     * Wyświetl instrukcję parowania
     */
    private function renderPairingInstructions($pairingCode, $expiresAt) {
        // Oblicz pozostały czas w sekundach
        $remainingSeconds = max(0, strtotime($expiresAt) - time());
        
        // Jeśli czas wygasł, wygeneruj nowy kod
        if ($remainingSeconds <= 0) {
            $pairingCode = $this->generatePairingCode();
            $expiresAt = date('Y-m-d H:i:s', time() + 900); // 15 minut
            
            // Zaktualizuj kod w bazie danych
            $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $stmt = $this->db->prepare("
                UPDATE client_displays
                SET pairing_code = ?, pairing_expires_at = ?
                WHERE id = (
                    SELECT id FROM client_displays
                    WHERE client_ip = ? AND pairing_status = 'pending'
                    ORDER BY created_at DESC
                    LIMIT 1
                )
            ");
            $stmt->execute([$pairingCode, $expiresAt, $clientIp]);
            
            // Ponownie oblicz pozostały czas
            $remainingSeconds = 900; // 15 minut w sekundach
        }
    ?>
        <!DOCTYPE html>
        <html lang="pl">

        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Parowanie wyświetlacza - KtoOstatni.pl</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                body {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                }

                .pairing-card {
                    background: white;
                    border-radius: 20px;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                    padding: 3rem;
                    text-align: center;
                    max-width: 500px;
                    margin: 0 auto;
                }

                .pairing-code {
                    font-size: 4rem;
                    font-weight: bold;
                    color: #667eea;
                    letter-spacing: 0.5rem;
                    margin: 2rem 0;
                    padding: 1rem;
                    background: #f8f9ff;
                    border-radius: 15px;
                    border: 3px dashed #667eea;
                }

                .icon-large {
                    font-size: 4rem;
                    color: #667eea;
                    margin-bottom: 1rem;
                }

                .step {
                    background: #f8f9ff;
                    border-radius: 10px;
                    padding: 1rem;
                    margin: 1rem 0;
                    border-left: 4px solid #667eea;
                }

                .refresh-timer {
                    color: #666;
                    font-size: 0.9rem;
                    margin-top: 1rem;
                }
            </style>
        </head>

        <body>
            <div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
                <div class="pairing-card">
                    <i class="fas fa-tv icon-large"></i>
                    <h1 class="h2 mb-4">Parowanie wyświetlacza</h1>

                    <p class="lead mb-4">
                        Aby sparować ten wyświetlacz z Twoim kontem, wpisz poniższy kod w panelu administracyjnym:
                    </p>

                    <div class="pairing-code" id="pairingCode">
                        <?= strtoupper($pairingCode) ?>
                    </div>

                    <div class="step">
                        <strong>Krok 1:</strong> Zaloguj się do panelu administracyjnego
                    </div>

                    <div class="step">
                        <strong>Krok 2:</strong> Przejdź do zakładki "Wyświetlacze"
                    </div>

                    <div class="step">
                        <strong>Krok 3:</strong> Wpisz kod <strong><?= strtoupper($pairingCode) ?></strong> i kliknij "Sparuj"
                    </div>

                    <div class="refresh-timer">
                        <i class="fas fa-clock"></i>
                        Kod wygaśnie za <span id="countdown">15:00</span> minut
                        <br>
                        <small>Strona odświeży się automatycznie</small>
                    </div>
                </div>
            </div>

            <script>
                // Odliczanie czasu do wygaśnięcia kodu
                let timeLeft = <?= $remainingSeconds ?>; // Rzeczywisty pozostały czas

                function updateCountdown() {
                    const minutes = Math.floor(timeLeft / 60);
                    const seconds = timeLeft % 60;
                    document.getElementById('countdown').textContent =
                        minutes.toString().padStart(2, '0') + ':' +
                        seconds.toString().padStart(2, '0');

                    if (timeLeft <= 0) {
                        location.reload();
                    } else {
                        timeLeft--;
                    }
                }

                // Aktualizuj co sekundę
                setInterval(updateCountdown, 1000);
                updateCountdown();

                // Sprawdzaj co 10 sekund czy wyświetlacz został sparowany
                setInterval(function() {
                    fetch(window.location.href + '?check_pairing=1')
                        .then(response => response.text())
                        .then(data => {
                            if (data.includes('paired')) {
                                location.reload();
                            }
                        })
                        .catch(error => console.log('Sprawdzanie parowania:', error));
                }, 10000);
            </script>
        </body>

        </html>
<?php
    }

    /**
     * Generuj URL do pliku napisów VTT na podstawie URL video
     */
    private function getSubtitlesUrl($videoUrl) {
        // Zamień rozszerzenie video na .vtt
        $pathInfo = pathinfo($videoUrl);
        if (isset($pathInfo['dirname']) && isset($pathInfo['filename'])) {
            return $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.vtt';
        }
        return null;
    }

    /**
     * Sprawdź czy plik napisów VTT istnieje
     */
    private function checkSubtitlesExist($subtitlesUrl) {
        if (!$subtitlesUrl) {
            return false;
        }

        // Konwertuj URL na ścieżkę systemową
        $filePath = $_SERVER['DOCUMENT_ROOT'] . $subtitlesUrl;

        // Sprawdź czy plik istnieje
        return file_exists($filePath) && is_readable($filePath);
    }


}
