/* Reset i podstawowe style */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Zmienne CSS dla wysoko<PERSON>ci sticky elementów */
:root {
    --doctor-info-height: 140px;
    --current-appointment-height: 180px;
}

body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    overflow-x: hidden;
    /* Pozwala na przewijanie w pionie, blokuje w poziomie */
    font-size: 16px;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
    max-width: 480px;
    margin: 0 auto;
    position: relative;
    overflow: visible;
}

.screen {
    display: none;
    width: 100%;
    min-height: 100vh;
    background: #f8f9fa;
}

.screen.active {
    display: flex;
    flex-direction: column;
}

/* Ekran logowania */
#loginScreen {
    align-items: center;
    justify-content: center;
    padding: 0;
    background: #f8f9fa;
}

.login-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-icon {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 3em;
}

.login-title {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #2c3e50;
}

.login-subtitle {
    font-size: 18px;
    color: #6c757d;
    margin-bottom: 30px;
}

.form-control {
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 15px 18px;
    font-size: 18px;
    width: 100%;
}

.form-control:focus {
    outline: none;
    border-color: #2c3e50;
    box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.1);
}

.btn {
    border: none;
    border-radius: 8px;
    padding: 15px 24px;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
}

.btn-primary {
    background-color: #2c3e50;
    color: white;
}

.btn-primary:hover {
    background-color: #1a252f;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-outline-secondary {
    background-color: transparent;
    color: #6c757d;
    border: 2px solid #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    color: white;
}

.btn-outline-light {
    background-color: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 14px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Panel główny */
#mainScreen {
    display: none;
    flex-direction: column;
    background: white;
    min-height: 100vh;
    overflow: visible;
}

#mainScreen.active {
    display: flex;
}

/* Kontener treści */
.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: visible;
}

/* Informacje o lekarzu - kompaktowe i przyklejone */
.doctor-info {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 20px;
    margin-bottom: 0;
    position: sticky;
    top: 0;
    z-index: 100;
    flex-shrink: 0;
}

.doctor-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.doctor-photo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 24px;
    overflow: hidden;
    color: rgba(255, 255, 255, 0.8);
}

.doctor-photo img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.doctor-details {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.doctor-info-text {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.doctor-name {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;
    line-height: 1.2;
}

.doctor-specialization {
    font-size: 16px;
    opacity: 0.8;
    font-weight: 400;
}

/* Przycisk wyloguj jako ikonka */
.logout-icon-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.logout-icon-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

.logout-icon-btn i {
    font-size: 16px;
}

.room-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.room-controls-line {
    display: flex;
    align-items: center;
    gap: 10px;
    height: 38px;
}

.room-name {
    flex: 1;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    line-height: 1.2;
    color: white;
}

.room-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.room-line {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.date-controls .date-input {
    width: 140px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 12px;
    padding: 6px 8px;
}

.date-controls .date-input:focus {
    border-color: rgba(255, 255, 255, 0.5);
    background-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.date-controls .btn,
.room-line .btn,
.room-controls-line .btn {
    height: 38px;
    min-width: 38px;
    padding: 6px 12px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.date-controls .btn i,
.room-line .btn i,
.room-controls-line .btn i {
    margin-right: 5px;
}

.date-controls .btn:has(i:only-child) i,
.room-line .btn:has(i:only-child) i,
.room-controls-line .btn:has(i:only-child) i {
    margin-right: 0;
}

.room-controls-line .date-input {
    height: 38px;
    width: 140px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 12px;
    padding: 6px 8px;
    border-radius: 8px;
    box-sizing: border-box;
    line-height: 1.2;
    vertical-align: middle;
}

.room-controls-line .date-input:focus {
    border-color: rgba(255, 255, 255, 0.5);
    background-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    outline: none;
}

.room-name {
    font-size: 18px;
    font-weight: 500;
    opacity: 0.9;
}

/* Statystyki - przyklejone nad przyciskami nawigacji */
.stats {
    display: flex;
    gap: 20px;
    margin-bottom: 0;
    position: sticky;
    bottom: 0px; /* Nad przyciskami nawigacji */
    z-index: 97;
    background: #f8f9fa;
    padding: 10px 20px;
    border-top: 1px solid #e9ecef;
}

.stat-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 15px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    margin: 0;
}

.stat-number.completed {
    color: #28a745;
}

.stat-number.waiting {
    color: #ffc107;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
    margin: 0;
}



/* Aktualna wizyta - kompaktowa i przyklejona */
.current-appointment {
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
    overflow: visible !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    position: sticky;
    top: 140px; /* fallback */
    top: var(--doctor-info-height, 140px);
    z-index: 99 !important;
    transform: none !important;
    clip: auto !important;
    clip-path: none !important;
    flex-shrink: 0;
}

.appointment-header {
    background: #2c3e50;
    color: white;
    padding: 15px 20px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.appointment-content {
    padding: 15px 20px;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    overflow: visible !important;
}

/* Kompaktowy layout - godzina, ikonka i nazwisko w jednej linii */
.appointment-main {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.appointment-time {
    font-size: 22px;
    font-weight: 700;
    color: #2c3e50;
    flex-shrink: 0;
}

.patient-name {
    font-size: 22px;
    color: #495057;
    font-weight: 700;
    flex: 1;
}

/* Ikonka obecności pacjenta - w linii */
.patient-presence {
    font-size: 18px;
    font-weight: 500;
    color: #007bff;
    flex-shrink: 0;
    margin-left: 10px;
}



/* Przyciski akcji */
.appointment-actions {
    display: flex !important;
    gap: 15px;
    margin-top: 20px;
    visibility: visible !important;
    opacity: 1 !important;
    overflow: visible !important;
    position: relative !important;
    z-index: 11 !important;
}

.appointment-actions .btn {
    flex: 1;
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.appointment-actions .btn:not(:disabled) {
    cursor: pointer;
    opacity: 1;
}

.appointment-actions .btn:disabled {
    cursor: not-allowed;
    opacity: 0.6;
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #fff !important;
}

.appointment-actions .btn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.appointment-actions .btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Style dla przycisków w różnych stanach */
.btn-success:not(:disabled) {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-outline-secondary:not(:disabled) {
    background-color: transparent;
    border-color: #6c757d;
    color: #6c757d;
}

.btn-secondary:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* Lista oczekujących - bez własnego scrollbara */
.waiting-list {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex: none;
    overflow: visible;
    display: flex;
    flex-direction: column;
    margin-top: 0;
    margin-bottom: 80px; /* Miejsce na statystyki na dole */
}

.waiting-header {
    background: #6c757d;
    color: white;
    padding: 15px 20px;
    font-size: 18px;
    font-weight: 600;
    flex-shrink: 0;
    position: sticky;
    top: 320px; /* fallback */
    top: calc(var(--doctor-info-height, 140px) + var(--current-appointment-height, 180px));
    z-index: 98;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Kontener dla listy oczekujących - bez własnego scrollbara */
.waiting-list-content {
    flex: none;
    overflow: visible;
    padding-bottom: 20px;
}

.waiting-item {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: background-color 0.2s;
}

.waiting-item:last-child {
    border-bottom: none;
}

.waiting-time {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    min-width: 80px;
}

.waiting-patient {
    font-size: 18px;
    color: #495057;
    flex: 1;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
}

/* Przyciski obecności pacjenta w liście oczekujących */
.presence-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    font-size: 16px;
    margin-right: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.presence-btn.present {
    background-color: #007bff;
    color: white;
    border: 1px solid #007bff;
}

.presence-btn.absent {
    background-color: transparent;
    color: #6c757d;
    border: 1px solid #6c757d;
}

.presence-btn:hover {
    transform: scale(1.1);
}

.presence-btn.present:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.presence-btn.absent:hover {
    background-color: #f8f9fa;
    border-color: #5a6268;
    color: #5a6268;
}

/* Zachowaj starą klasę dla zgodności */
.waiting-presence {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    color: #007bff;
    font-size: 16px;
    margin-right: 5px;
}



.waiting-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.call-patient-btn {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.call-patient-btn:hover {
    background-color: #0056b3;
    transform: scale(1.1);
}

.call-patient-btn:active {
    transform: scale(0.95);
}

.call-patient-btn i {
    margin-left: 2px; /* Lekkie przesunięcie ikony play w prawo */
}

/* Style dla przycisku zamykania wizyty */
.close-appointment-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.close-appointment-btn:hover {
    background-color: #c82333;
    transform: scale(1.1);
}

.close-appointment-btn:active {
    transform: scale(0.95);
}

/* Style dla przycisku play w zakończonych wizytach */
.waiting-item.closed .call-patient-btn {
    background-color: #6c757d;
    opacity: 0.8;
}

.waiting-item.closed .call-patient-btn:hover {
    background-color: #5a6268;
    opacity: 1;
}

.waiting-item.active {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.waiting-item.closed {
    background-color: #f5f5f5;
    opacity: 0.7;
    border-left: 4px solid #9e9e9e;
}

.waiting-item.closed .waiting-time,
.waiting-item.closed .waiting-patient {
    color: #757575;
}

/* Przyciski nawigacji - przyklejone na dole */
.navigation-buttons {
    display: flex;
    gap: 15px;
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
    position: sticky;
    bottom: 0;
    z-index: 96;
    margin-top: auto;
}

.navigation-buttons .btn {
    flex: 1;
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.navigation-buttons .btn i {
    font-size: 18px;
}

.navigation-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Komunikat o braku wizyt */
.no-appointments {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.no-appointments i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-appointments p {
    font-size: 18px;
    margin: 0;
}

/* Alerty */
.alert {
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 16px;
    border: none;
    position: relative;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-dismissible {
    padding-right: 60px;
}

.btn-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    opacity: 0.5;
}

.btn-close:hover {
    opacity: 1;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: modalSlideIn 0.3s ease-out;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 400px;
    /* Dostosowane do szerokości aplikacji */
    max-height: 80vh;
    overflow: hidden;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.form-select {
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 15px 18px;
    font-size: 18px;
    width: 100%;
    background-color: white;
}

.form-select:focus {
    outline: none;
    border-color: #2c3e50;
    box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.1);
}

/* Loading spinner */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.spinner-border {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f3f3;
    border-top: 6px solid #2c3e50;
    border-radius: 50%;
    animation: spinner-border 1s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* Responsywność - zawsze mobilny wygląd */
@media (max-width: 480px) {
    #app {
        max-width: 100%;
        /* Pełna szerokość na małych ekranach */
        box-shadow: none;
        /* Brak cienia na telefonach */
    }

    .login-card {
        padding: 30px 20px;
        margin: 0;
    }

    .doctor-info {
        padding: 15px;
    }

    .logout-icon-btn {
        width: 35px;
        height: 35px;
    }

    .logout-icon-btn i {
        font-size: 14px;
    }

    .current-appointment {
        margin-bottom: 15px;
    }

    .waiting-list {
        margin-bottom: 15px;
    }

    .navigation-buttons {
        padding: 15px 0;
    }

    .btn {
        padding: 12px 20px;
        font-size: 14px;
    }

    .room-info {
        gap: 10px;
    }

    .room-controls {
        flex-direction: column;
        gap: 10px;
    }

    .date-controls {
        justify-content: center;
    }

    .date-controls .date-input {
        width: 120px;
        font-size: 11px;
    }

    .room-name {
        font-size: 16px;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }
}

/* Style dla większych ekranów - zawsze mobilny wygląd */
@media (min-width: 481px) {
    #app {
        max-width: 480px;
        /* Stała szerokość jak telefon */
        margin: 0 auto;
        /* Wyśrodkowanie bez marginesu */
        border-radius: 0;
        /* Bez zaokrąglonych rogów */
        overflow: visible;
        /* Pozwala na przewijanie całej strony */
    }

    body {
        background: #f8f9fa;
        /* Jednolite tło */
        padding: 0;
    }
}

/* Dodatkowe style dla trybu PWA na większych ekranach */
@media (display-mode: standalone) and (min-width: 513px) {
    body {
        background: #f0f0f0;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        padding: 20px 0;
    }
    
    #app {
        width: 512px;
        min-height: calc(100vh - 40px);
        max-height: calc(100vh - 40px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        overflow: hidden;
    }
}

/* Usunięcie starych media queries które mogły powodować problemy */
@media (max-width: 768px) {
    /* Usunięte - nie potrzebne */
}

@media (max-width: 360px) {
    .doctor-name {
        font-size: 18px;
    }

    .doctor-specialization {
        font-size: 12px;
    }

    .appointment-time {
        font-size: 22px;
    }

    .patient-name {
        font-size: 16px;
    }

    .appointment-main {
        gap: 10px;
    }

    .btn {
        padding: 10px 16px;
        font-size: 12px;
    }
}

/* Animacje */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }

    to {
        transform: translateX(0);
    }
}

/* Scrollbar usunięty - lista bez przewijania */

/* PWA specific styles */
@media (display-mode: standalone) {
    body {
        background: white;
        padding: 0;
        overflow-x: hidden;
        -webkit-user-select: text;
        user-select: text;
    }

    /* Ukryj navigation-buttons w trybie PWA */
    .navigation-buttons {
        display: none !important;
    }

    #app {
        width: 512px;
        min-height: 100vh;
        max-width: 512px;
        margin: 0 auto;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
        border-radius: 0;
        overflow: visible;
        display: flex;
        flex-direction: column;
    }
    
    /* Zapewnia widoczność przycisków w trybie PWA */
        .current-appointment {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            height: auto !important;
        }
        
        .appointment-content {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        .appointment-actions {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        .appointment-actions .btn {
            display: inline-flex !important;
            visibility: visible !important;
            opacity: 1 !important;
        }
        
        /* Lista oczekujących w PWA - bez własnego scrollbara */
        .waiting-list-content {
            overflow: visible !important;
        }
        
        /* Pozycjonowanie w PWA - teraz takie samo jak w wersji webowej */
        .doctor-info {
            position: sticky !important;
            top: 0 !important;
            z-index: 100 !important;
        }

        .current-appointment {
            position: sticky !important;
            top: var(--doctor-info-height, 140px) !important;
            z-index: 99 !important;
        }

        .waiting-header {
            position: sticky !important;
            top: calc(var(--doctor-info-height, 140px) + var(--current-appointment-height, 180px)) !important;
            z-index: 98 !important;
        }

        .stats {
            position: sticky !important;
            bottom: 0 !important;
            z-index: 97 !important;
        }
}

/* Input formatting */
#loginEmail, #loginPassword {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
    font-size: 16px;
}

#facilityIdentifier {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.form-text {
    font-size: 14px;
    color: #6c757d;
    margin-top: 5px;
}

.mb-3 {
    margin-bottom: 1rem !important;
}

/* Scrollbar usunięty */

/* Dodatkowe style dla małych ekranów */
@media (max-width: 480px) {
    .login-card {
        padding: 15px;
        margin: 0;
    }

    .doctor-info {
        padding: 10px;
    }

    .logout-icon-btn {
        width: 30px;
        height: 30px;
    }

    .logout-icon-btn i {
        font-size: 12px;
    }

    .current-appointment {
        padding: 0px;
    }

    .waiting-list {
        padding: 0px;
    }

    .navigation-buttons {
        padding: 10px;
    }

    .btn {
        padding: 10px 12px;
        font-size: 14px;
    }

    .room-info {
        gap: 8px;
    }

    .room-controls {
        flex-direction: column;
        gap: 8px;
    }

    .date-controls .date-input {
        width: 110px;
        font-size: 16px;
    }

    .date-controls .btn {
        padding: 4px 8px;
        font-size: 10px;
    }

    .room-name {
        width: 100%;
    }
}

/* Style dla bardzo małych ekranów */
@media (max-width: 360px) {
    .doctor-name {
        font-size: 13px;
    }

    .doctor-specialization {
        font-size: 11px;
    }

    .appointment-time {
        font-size: 18px;
    }

    .patient-name {
        font-size: 13px;
    }

    .appointment-main {
        gap: 8px;
    }

    .stat-item {
        padding: 10px;
        gap: 8px;
    }

    .stat-number {
        font-size: 18px;
    }

    .stat-label {
        font-size: 12px;
    }

    .btn {
        padding: 8px 10px;
        font-size: 12px;
    }
}

/* Style dla przewijania listy - usunięte, bo lista nie ma własnego scrollbara */

/* Animacje */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* PWA specific styles - usunięto duplikaty */

/* Formatowanie pól formularza logowania */
#loginEmail, #loginPassword {
    text-align: left;
    letter-spacing: 0.5px;
    font-weight: 500;
    font-size: 16px;
}

#facilityIdentifier {
    text-align: left;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Usunięcie duplikatów */
.waiting-list::-webkit-scrollbar {
    display: none;
    /* Ukrywa stary scrollbar */
}

.waiting-list::-webkit-scrollbar-track {
    display: none;
}

.waiting-list::-webkit-scrollbar-thumb {
    display: none;
}

.waiting-list::-webkit-scrollbar-thumb:hover {
    display: none;
}

/* Przycisk instalacji */
#installBtn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    display: none;
}

#installBtn.show {
    display: flex !important;
}

#installBtn i {
    margin-right: 8px;
}

#installBtn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#installBtn:hover::before {
    left: 100%;
}

#installBtn:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(102, 126, 234, 0.4);
}

#installBtn.installed {
    background: #28a745;
    pointer-events: none;
    animation: pulse 2s infinite;
}

#installBtn.installed:hover {
    background: #28a745;
    transform: none;
    box-shadow: none;
}

/* Styl dla przycisku ostrzeżenia Firefox */
#installBtn.firefox-warning {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
    color: white !important;
    cursor: not-allowed !important;
    font-size: 14px !important;
    padding: 15px 20px !important;
    text-align: center !important;
    line-height: 1.3 !important;
}

#installBtn.firefox-warning:hover {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
    transform: none !important;
    box-shadow: none !important;
}

#installBtn.firefox-warning::before {
    display: none !important;
}

/* Styl dla powiadomienia o aktualizacji */
.update-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #2c3e50;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 10000;
    display: flex;
    align-items: center;
    gap: 10px;
    animation: slideIn 0.3s ease-out;
    max-width: 300px;
}

.update-toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}

.update-toast-content i {
    font-size: 16px;
    flex-shrink: 0;
}

.update-toast-content span {
    flex: 1;
    font-size: 14px;
}

.update-toast-content button {
    flex-shrink: 0;
    padding: 6px 12px;
    font-size: 12px;
    background: #28a745;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s;
}

.update-toast-content button:hover {
    background: #218838;
}


/* Animacja dla powiadomienia o aktualizacji */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Przycisk wylogowania */
#logoutBtn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #6c757d;
}

#logoutBtn:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    color: #343a40;
}