// Importuj konfigurację (fallback do wartości domyślnych jeśli nie dostępna)
let APP_CONFIG = null;
try {
    importScripts('/lekarz/config.js');
    APP_CONFIG = self.AppConfig;
    console.log('SW: Załadowano config.js, wersja:', APP_CONFIG?.version);
} catch (e) {
    console.log('SW: <PERSON><PERSON> mo<PERSON> config.js, używam wartości domyślnych:', e.message);
}

// Konfiguracja z fallback do wartości domyślnych
const CONFIG = APP_CONFIG || {
    version: '3.8.6', // Zaktualizowana wersja fallback
    cache: {
        name: 'doctor-panel-v3',
        staticResources: [
            '/lekarz/',
            '/lekarz/index.html',
            '/lekarz/styles.css',
            '/lekarz/app.js',
            '/lekarz/config.js',
            '/lekarz/data-helper.js',
            '/lekarz/api-client.js',
            '/lekarz/cache-client.js',
            '/lekarz/manifest.json',
            '/lekarz/icons/icon-192x192.png',
            '/lekarz/icons/icon-512x512.png',
            '/lekarz/screenshots/desktop.png',
            '/lekarz/screenshots/mobile.png'
        ]
    }
};

const CACHE_NAME = CONFIG.cache.name;
const APP_VERSION = CONFIG.version;
const STATIC_CACHE_URLS = CONFIG.cache.staticResources;

console.log('SW: Inicjalizacja z wersją:', APP_VERSION);

// Instalacja Service Worker
self.addEventListener('install', event => {
    console.log('Service Worker installing version:', APP_VERSION);
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Caching app shell');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(() => {
                console.log('Service Worker installed');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Failed to cache app shell:', error);
            })
    );
});

// Aktywacja Service Worker
self.addEventListener('activate', event => {
    console.log('Service Worker activating version:', APP_VERSION);
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker activated');
            return self.clients.claim();
        })
    );
});

// Obsługa wiadomości od klienta
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        console.log('Service Worker: Received SKIP_WAITING message, activating new version');
        self.skipWaiting();
    }
});

// Interceptowanie żądań - strategia cache first dla statycznych zasobów
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);
    
    // Dla żądań API - zawsze pobieraj z sieci (network first)
    if (url.pathname.startsWith('/api/')) {
        event.respondWith(
            fetch(request)
                .catch(error => {
                    console.log('Network error for API:', error);
                    // Zwróć błąd połączenia
                    return new Response(JSON.stringify({
                        error: 'Brak połączenia z serwerem',
                        offline: true
                    }), {
                        status: 503,
                        headers: { 'Content-Type': 'application/json' }
                    });
                })
        );
        return;
    }

    // Dla statycznych zasobów aplikacji - cache first z network fallback
    if (request.method === 'GET' &&
        (url.pathname.startsWith('/lekarz/') ||
         url.pathname === '/lekarz' ||
         request.url.includes('cdn.jsdelivr.net'))) {
        
        event.respondWith(
            caches.match(request)
                .then(response => {
                    // Cache hit - return response
                    if (response) {
                        return response;
                    }
                    
                    // Network request
                    console.log('SW: Pobieranie z sieci:', request.url);
                    return fetch(request).then(response => {
                        // Check if valid response
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            console.log('SW: Nieprawidłowa odpowiedź dla:', request.url, response.status);
                            return response;
                        }

                        // Clone response before caching
                        const responseToCache = response.clone();

                        caches.open(CACHE_NAME)
                            .then(cache => {
                                cache.put(request, responseToCache);
                                console.log('SW: Zapisano do cache:', request.url);
                            });

                        return response;
                    });
                })
                .catch(error => {
                    console.log('Fetch failed for:', request.url, error);
                    
                    // Dla głównego pliku HTML, zwróć cached fallback
                    if (request.url.includes('/lekarz') || request.url.endsWith('/')) {
                        return caches.match('/lekarz/index.html');
                    }
                })
        );
        return;
    }

    // Dla innych żądań - pobieraj z sieci
    event.respondWith(fetch(request));
});

// Obsługa powiadomień push (opcjonalnie)
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: 1
            },
            actions: [
                {
                    action: 'explore',
                    title: 'Otwórz aplikację'
                },
                {
                    action: 'close',
                    title: 'Zamknij'
                }
            ]
        };

        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Obsługa kliknięć w powiadomienia
self.addEventListener('notificationclick', event => {
    event.notification.close();

    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/lekarz/')
        );
    }
}); 