/**
 * Konfiguracja aplikacji PWA dla lekarzy
 */

const AppConfig = {
    // Wersja aplikacji - musi być synchronizowana z app.js, sw.js i manifest.json
    version: '3.8.7',
    
    // Konfiguracja aktualizacji
    updates: {
        // Czas sprawdzania aktualizacji w minutach
        checkIntervalMinutes: 2,
        
        // Automatyczna instalacja aktualizacji bez powiadomienia użytkownika
        autoInstall: true,
        
        // Timeout dla wymuszenia odświeżenia strony (w sekundach)
        forceRefreshTimeout: 3
    },
    
    // Konfiguracja monitorowania zmian danych
    dataMonitoring: {
        // Czas sprawdzania zmian w wizytach (w sekundach)
        appointmentsCheckInterval: 4
    },
    
    // Konfiguracja cache
    cache: {
        // Nazwa cache dla Service Worker
        name: 'doctor-panel-v3',
        
        // Lista zasobów do cache'owania
        staticResources: [
            '/lekarz/',
            '/lekarz/index.html',
            '/lekarz/styles.css',
            '/lekarz/app.js',
            '/lekarz/data-helper.js',
            '/lekarz/api-client.js',
            '/lekarz/cache-client.js',
            '/lekarz/config.js',
            '/lekarz/manifest.json',
            '/lekarz/icons/icon-192x192.png',
            '/lekarz/icons/icon-512x512.png',
            '/lekarz/screenshots/desktop.png',
            '/lekarz/screenshots/mobile.png'
        ]
    },
    
    // Konfiguracja API
    api: {
        // Bazowy URL dla API v2
        baseUrl: '/api/v2',
        
        // Timeout dla żądań API (w sekundach)
        timeout: 30
    },
    
    // Konfiguracja sesji
    session: {
        // Czas życia sesji do północy
        expiresAtMidnight: true
    },
    
    // Konfiguracja UI
    ui: {
        // Czas wyświetlania powiadomień (w sekundach)
        notificationDuration: 5,
        
        // Czas animacji przejść (w milisekundach)
        transitionDuration: 500
    }
};

// Eksport konfiguracji
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppConfig;
} else {
    window.AppConfig = AppConfig;
}
