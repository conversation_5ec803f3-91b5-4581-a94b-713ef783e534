// Aplikacja PWA dla lekarzy
// Import V2ApiClient
// V2ApiClient jest załadowany w index.html jako osobny skrypt
class DoctorApp {
    /**
     * Ładowanie konfiguracji z localStorage
     */
    loadConfigFromStorage() {
        const storedConfig = localStorage.getItem('appConfig');
        if (storedConfig) {
            try {
                const parsedConfig = JSON.parse(storedConfig);
                // Merge z domyślną konfiguracją
                this.config = { ...this.config, ...parsedConfig };
                this.updateCheckInterval = this.config.updates.checkIntervalMinutes * 60 * 1000;
                console.log('Załadowano konfigurację z localStorage:', this.config);
            } catch (e) {
                console.error('Błąd podczas ładowania konfiguracji z localStorage:', e);
            }
        }
    }
    constructor() {
        this.currentDoctor = null;
        this.currentRoom = null;
        this.availableRooms = []; // Lista dostępnych gabinetów
        this.currentAppointment = null;
        this.waitingAppointments = [];
        this.allAppointments = [];
        this.updateInterval = null;
        this.timeInterval = null;
        this.isOnline = navigator.onLine;
        this.deferredPrompt = null; // Dla instalacji PWA
        this.apiClient = new V2ApiClient('/api/v2'); // Nowy V2 API client
        this.apiBaseUrl = '/api/v2'; // Base URL dla operacji lekarza - teraz wszystko przez v2
        this.selectedDate = new Date().toISOString().split('T')[0]; // Dzisiejsza data
        // Konfiguracja z pliku config.js (jeśli dostępna) lub wartości domyślne
        this.config = window.AppConfig || {
            version: '3.8.3',
            updates: { checkIntervalMinutes: 2, autoInstall: true, forceRefreshTimeout: 3 },
            dataMonitoring: { appointmentsCheckInterval: 2 }
        };

        // Załaduj konfigurację z localStorage (jeśli istnieje)
        this.loadConfigFromStorage();

        this.appVersion = this.config.version; // Wersja aplikacji z konfiguracji
        this.isUpdateAvailable = false; // Flaga dostępności aktualizacji
        this.swRegistration = null; // Rejestracja Service Workera
        this.updateCheckInterval = this.config.updates.checkIntervalMinutes * 60 * 1000; // Konfigurowalny czas sprawdzania aktualizacji
        console.log('Inicjalizacja aplikacji z datą:', this.selectedDate);

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.autoDetectFacilityIdentifier(); // Automatycznie wykryj identyfikator
        this.checkForStoredSession();
        this.setupOnlineOfflineHandling();
        this.initInstallPrompt();
        this.registerServiceWorker();
        this.checkForUpdates();
        this.displayAppVersion();
        
        // Pokaż przycisk instalacji po krótkim opóźnieniu
        setTimeout(() => {
            this.checkBrowserCompatibility();
        }, 1000);
    }

    setupEventListeners() {
        // Formularz logowania
        const loginForm = document.getElementById('loginForm');
        const facilityIdentifierInput = document.getElementById('facilityIdentifier');
        const loginEmailInput = document.getElementById('loginEmail');
        const loginPasswordInput = document.getElementById('loginPassword');

        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Wczytaj zapisany identyfikator placówki
        this.loadSavedFacilityIdentifier();
        
        // Sprawdź czy identyfikator był automatycznie wykryty i ustaw widoczność pola
        this.updateFacilityFieldVisibility();

        // Przyciski akcji
        document.getElementById('nextBtn').addEventListener('click', () => {
            this.callNextAppointment();
        });

        document.getElementById('previousBtn').addEventListener('click', (e) => {
            console.log('=== KLIKNIĘTO PRZYCISK POPRZ ===');
            console.log('Disabled:', e.target.disabled);
            console.log('Current appointment:', this.currentAppointment);
            console.log('Event target:', e.target);

            if (e.target.disabled) {
                console.log('Przycisk jest wyłączony - nie wykonuję akcji');
                return;
            }

            console.log('Wywołuję goToPreviousAppointment...');
            this.goToPreviousAppointment();
        });



        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });

        // Nowe przyciski dla zarządzania gabinetami
        document.getElementById('changeRoomBtn').addEventListener('click', () => {
            this.showRoomSelector();
        });

        document.getElementById('roomSelector').addEventListener('change', (e) => {
            this.changeRoom(e.target.value);
        });

        // Zamykanie modalu wyboru gabinetu
        const roomModal = document.getElementById('roomModal');
        roomModal.addEventListener('click', (e) => {
            if (e.target === roomModal) {
                this.hideRoomSelector();
            }
        });

        document.getElementById('closeRoomModal').addEventListener('click', () => {
            this.hideRoomSelector();
        });

        document.getElementById('closeRoomModalX').addEventListener('click', () => {
            this.hideRoomSelector();
        });

        document.getElementById('confirmRoomChange').addEventListener('click', () => {
            const selector = document.getElementById('roomSelector');
            if (selector.value) {
                this.changeRoom(selector.value);
            }
        });

        // Event listenery dla selektora daty
        document.getElementById('selectedDate').addEventListener('change', (e) => {
            this.changeDate(e.target.value);
        });

        document.getElementById('prevDayBtn').addEventListener('click', () => {
            this.changeDateByDays(-1);
        });

        document.getElementById('nextDayBtn').addEventListener('click', () => {
            this.changeDateByDays(1);
        });

        // Obsługa instalacji PWA
        document.getElementById('installBtn').addEventListener('click', () => {
            this.installApp();
        });
    }

    setupOnlineOfflineHandling() {
        // Nasłuchuj zmian stanu połączenia
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.hideOfflineMessage();
            this.loadAppointments(); // Odśwież dane po powrocie online
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showOfflineMessage();
        });

        // Sprawdź stan połączenia przy starcie
        if (!this.isOnline) {
            this.showOfflineMessage();
        }
    }

    showOfflineMessage() {
        // Usuń istniejące komunikaty
        this.hideOfflineMessage();

        const offlineAlert = document.createElement('div');
        offlineAlert.id = 'offlineAlert';
        offlineAlert.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        offlineAlert.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
        offlineAlert.innerHTML = `
            <i class="fas fa-wifi-slash me-2"></i>
            <strong>Brak połączenia z internetem!</strong> Aplikacja wymaga połączenia z serwerem.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(offlineAlert);
    }

    hideOfflineMessage() {
        const existingAlert = document.getElementById('offlineAlert');
        if (existingAlert) {
            existingAlert.remove();
        }
    }

    loadSavedFacilityIdentifier() {
        const savedIdentifier = localStorage.getItem('facilityIdentifier');
        if (savedIdentifier) {
            document.getElementById('facilityIdentifier').value = savedIdentifier;
        }
    }

    /**
     * Automatycznie wykrywa identyfikator placówki z domeny i zapisuje go
     */
    autoDetectFacilityIdentifier() {
        const hostname = window.location.hostname;
        let facilityIdentifier = '';

        // Sprawdź czy jesteśmy na localhost
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
            facilityIdentifier = 'localhost';
        } else {
            // Wyodrębnij subdomenę z ktoostatni.pl
            const match = hostname.match(/^([^.]+)\.ktoostatni\.pl$/i);
            if (match && match[1]) {
                facilityIdentifier = match[1];
            }
        }

        // Jeśli wykryto identyfikator, zapisz go i ukryj pole
        if (facilityIdentifier) {
            localStorage.setItem('facilityIdentifier', facilityIdentifier);
            localStorage.setItem('facilityIdentifierAutoDetected', 'true');
            
            // Ustaw wartość w polu formularza (nawet jeśli będzie ukryte)
            const facilityInput = document.getElementById('facilityIdentifier');
            if (facilityInput) {
                facilityInput.value = facilityIdentifier;
            }
            
            // Aktualizuj widoczność pola
            this.updateFacilityFieldVisibility();
            
            console.log('Wykryto identyfikator placówki:', facilityIdentifier);
            return facilityIdentifier;
        }
        
        console.log('Nie udało się wykryć identyfikatora placówki z hostname:', hostname);
        return null;
    }

    /**
     * Aktualizuje widoczność pola identyfikatora na podstawie auto-detekcji
     */
    updateFacilityFieldVisibility() {
        const autoDetected = localStorage.getItem('facilityIdentifierAutoDetected') === 'true';
        const facilityInput = document.getElementById('facilityIdentifier');
        const facilityGroup = document.getElementById('facilityIdentifierGroup');
        
        if (autoDetected && facilityInput && facilityInput.value) {
            // Ukryj pole identyfikatora jeśli został automatycznie wykryty
            facilityGroup.style.display = 'none';
            facilityInput.removeAttribute('required');
            console.log('Pole identyfikatora ukryte - wykryto automatycznie:', facilityInput.value);
        } else {
            // Pokaż pole identyfikatora jeśli nie został wykryty automatycznie
            facilityGroup.style.display = 'block';
            if (facilityInput) {
                facilityInput.setAttribute('required', '');
            }
            console.log('Pole identyfikatora widoczne - wymaga ręcznego wpisania');
        }
    }

    saveFacilityIdentifier(identifier) {
        localStorage.setItem('facilityIdentifier', identifier);
    }

    getApiBaseUrl(facilityIdentifier) {
        // Jeśli identyfikator to "localhost", używamy trybu deweloperskiego
        if (facilityIdentifier.toLowerCase() === 'localhost') {
            return '/api/v2';
        }
        
        // W przeciwnym razie używamy identyfikatora do budowania URL
        return `https://${facilityIdentifier}.ktoostatni.pl/api/v2`;
    }

    async handleLogin() {
        console.log('handleLogin wywołane');

        if (!this.isOnline) {
            console.log('Brak połączenia z internetem');
            this.showError('Brak połączenia z internetem. Sprawdź połączenie i spróbuj ponownie.');
            return;
        }

        const facilityIdentifier = document.getElementById('facilityIdentifier').value.trim();
        const loginEmail = document.getElementById('loginEmail').value.trim();
        const loginPassword = document.getElementById('loginPassword').value;

        // Walidacja danych
        if (!facilityIdentifier) {
            this.showError('Identyfikator placówki jest wymagany');
            return;
        }

        if (!loginEmail) {
            this.showError('Login (email) jest wymagany');
            return;
        }

        if (!loginPassword) {
            this.showError('Hasło jest wymagane');
            return;
        }

        // Walidacja formatu email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(loginEmail)) {
            this.showError('Nieprawidłowy format adresu email');
            return;
        }

        // Zapisz identyfikator placówki tylko jeśli nie był automatycznie wykryty
        const autoDetected = localStorage.getItem('facilityIdentifierAutoDetected') === 'true';
        if (!autoDetected) {
            this.saveFacilityIdentifier(facilityIdentifier);
        }

        // Ustaw URL API na podstawie identyfikatora
        this.apiBaseUrl = this.getApiBaseUrl(facilityIdentifier);

        this.showLoading(true);

        try {
            console.log('Wysyłam żądanie logowania do:', `${this.apiBaseUrl}/doctor/login`);
            console.log('Dane logowania:', { login: loginEmail, facility: facilityIdentifier });

            const response = await fetch(`${this.apiBaseUrl}/doctor/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    login: loginEmail,
                    password: loginPassword
                })
            });

            console.log('Status odpowiedzi:', response.status);
            console.log('Headers odpowiedzi:', response.headers);

            const data = await response.json();

            console.log('Odpowiedź z API logowania:', data);

            if (data.success) {
                this.currentDoctor = data.data.doctor;
                this.currentRoom = data.data.default_room; // Domyślny gabinet lekarza
                this.availableRooms = data.data.available_rooms || []; // Lista dostępnych gabinetów

                // Zapisz sesję do północy
                const now = new Date();
                const midnight = new Date(now);
                midnight.setHours(24, 0, 0, 0);

                localStorage.setItem('doctorSession', JSON.stringify({
                    doctor: this.currentDoctor,
                    room: this.currentRoom,
                    availableRooms: this.availableRooms,
                    facilityIdentifier: facilityIdentifier,
                    expiresAt: midnight.getTime()
                }));

                this.showMainScreen();
                this.loadAppointments();
                this.startAutoUpdate();

                // Sprawdź czy można pokazać przycisk instalacji
                if (this.deferredPrompt) {
                    this.showInstallButton();
                }
            } else {
                this.showError(data.message || 'Nieprawidłowy login, hasło lub identyfikator placówki');
            }
        } catch (error) {
            console.error('Błąd logowania:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    checkForStoredSession() {
        const stored = localStorage.getItem('doctorSession');
        console.log('Sprawdzam zapisaną sesję:', stored);

        if (stored) {
            try {
                const session = JSON.parse(stored);
                const now = Date.now();

                console.log('Sesja z localStorage:', session);
                console.log('Czas teraz:', now, 'Wygasa:', session.expiresAt);

                // Sprawdź czy sesja nie wygasła (do północy) i czy zawiera wszystkie wymagane dane
                if (session.expiresAt && now < session.expiresAt && session.doctor && session.room && session.facilityIdentifier) {
                    console.log('Sesja jest ważna, przywracam dane');
                    this.currentDoctor = session.doctor;
                    this.currentRoom = session.room;
                    this.availableRooms = session.availableRooms || [];
                    
                    // Ustaw identyfikator placówki i URL API
                    document.getElementById('facilityIdentifier').value = session.facilityIdentifier;
                    this.apiBaseUrl = this.getApiBaseUrl(session.facilityIdentifier);
                    
                    this.showMainScreen();
                    this.loadAppointments();
                    this.startAutoUpdate();
                } else {
                    console.log('Sesja wygasła lub niepełna, usuwam');
                    localStorage.removeItem('doctorSession');
                }
            } catch (error) {
                console.log('Błąd parsowania sesji:', error);
                localStorage.removeItem('doctorSession');
            }
        } else {
            console.log('Brak zapisanej sesji');
        }
    }

    showMainScreen() {
        document.getElementById('loginScreen').classList.remove('active');
        document.getElementById('mainScreen').classList.add('active');

        this.updateDoctorInfo();
        this.updateRoomSelector();
        this.initializeDateSelector();

        // Wymuś pokazanie sekcji aktualnej wizyty przy starcie
        this.forceShowCurrentAppointment();

        // Upewnij się, że przyciski są widoczne w trybie PWA
        this.ensureButtonsVisibility();

        // Dodaj test ładowania danych
        console.log('Pokazuję główny ekran z danymi:', {
            doctor: this.currentDoctor,
            room: this.currentRoom,
            availableRooms: this.availableRooms
        });

        // Załaduj wizyty po pokazaniu ekranu
        setTimeout(() => {
            this.loadAppointments();
        }, 100);
    }

    updateDoctorInfo() {
        if (!this.currentDoctor || !this.currentRoom) return;

        // Informacje o lekarzu
        document.getElementById('doctorName').textContent =
            `Dr. ${DataHelper.normalizeFullName(this.currentDoctor.first_name, this.currentDoctor.last_name)}`;

        // Specjalizacja lekarza
        const specialization = this.currentDoctor.specialization || this.currentDoctor.specialty || 'Lekarz';
        document.getElementById('doctorSpecialization').textContent = specialization;

        document.getElementById('roomName').textContent = DataHelper.normalizeRoomName(this.currentRoom.name);

        // Obsługa zdjęcia lekarza
        const photoImg = document.getElementById('doctorPhotoImg');
        const photoIcon = document.getElementById('doctorPhotoIcon');

        // Sprawdź różne możliwe nazwy pól ze zdjęciem
        const photoUrl = this.currentDoctor.photo || this.currentDoctor.photo_url || this.currentDoctor.image;

        console.log('Dane lekarza:', this.currentDoctor);
        console.log('URL zdjęcia:', photoUrl);

        if (photoUrl && photoUrl.trim() !== '') {
            photoImg.src = photoUrl;
            photoImg.style.display = 'block';
            photoIcon.style.display = 'none';
            
            // Dodaj obsługę błędów ładowania obrazka
            photoImg.onerror = function() {
                this.onerror = null;
                this.style.display = 'none';
                photoIcon.style.display = 'block';
                console.log('Błąd ładowania zdjęcia lekarza, wyświetlam ikonę');
            };
            
            console.log('Wyświetlam zdjęcie lekarza:', photoUrl);
        } else {
            photoImg.style.display = 'none';
            photoIcon.style.display = 'block';
            console.log('Brak zdjęcia lekarza, wyświetlam ikonę');
        }

        // Aktualizuj wysokości sticky elementów po zmianie danych lekarza
        setTimeout(() => {
            if (typeof updateStickyHeights === 'function') {
                updateStickyHeights();
            }
        }, 50);
    }

    initializeDateSelector() {
        const dateInput = document.getElementById('selectedDate');

        // Upewnij się, że selectedDate jest w poprawnym formacie
        if (!this.selectedDate) {
            this.selectedDate = new Date().toISOString().split('T')[0];
        }

        dateInput.value = this.selectedDate;

        // Ustaw minimalną datę na dzisiaj
        const today = new Date().toISOString().split('T')[0];
        dateInput.min = today;

        console.log('Inicjalizacja selektora daty:', this.selectedDate);
    }

    changeDate(newDate) {
        console.log('changeDate wywołane z newDate:', newDate);

        // Sprawdź czy data nie jest w przeszłości (tylko ostrzeżenie, nie blokuj)
        const today = new Date().toISOString().split('T')[0];
        if (newDate < today) {
            console.log('Ostrzeżenie: wybrano datę z przeszłości:', newDate);
        }

        this.selectedDate = newDate;
        console.log('Zmiana daty na:', newDate);

        // Zawsze upewnij się, że sekcja aktualnej wizyty jest widoczna przy zmianie daty
        this.forceShowCurrentAppointment();

        // Odśwież wizyty dla nowej daty
        this.loadAppointments();
    }

    changeDateByDays(days) {
        console.log('changeDateByDays wywołane z days:', days);
        console.log('Aktualna selectedDate:', this.selectedDate);

        // Poprawne parsowanie daty - dodaj separator jeśli go brakuje
        let dateString = this.selectedDate;
        if (!dateString.includes('-')) {
            // Jeśli data jest w formacie YYYYMMDD, dodaj separatory
            dateString = dateString.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3');
        }

        const currentDate = new Date(dateString);
        console.log('Parsowana data:', currentDate);

        // Dodaj dni
        currentDate.setDate(currentDate.getDate() + days);
        console.log('Data po dodaniu dni:', currentDate);

        // Sprawdź czy data nie jest w przeszłości (tylko dla przycisku "w przód")
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Jeśli próbujemy iść w przód i data jest w przeszłości, ustaw na dzisiaj
        if (days > 0 && currentDate < today) {
            console.log('Data w przeszłości, ustawiam na dzisiaj');
            currentDate.setTime(today.getTime());
        }

        const newDateString = currentDate.toISOString().split('T')[0];
        console.log('Nowa data string:', newDateString);

        // Zaktualizuj input i wywołaj changeDate
        document.getElementById('selectedDate').value = newDateString;
        this.changeDate(newDateString);
    }

    updateRoomSelector() {
        const selector = document.getElementById('roomSelector');
        selector.innerHTML = '';

        // Dodaj opcję "Wybierz gabinet"
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'Wybierz gabinet...';
        selector.appendChild(defaultOption);

        // Dodaj dostępne gabinety
        this.availableRooms.forEach(room => {
            const option = document.createElement('option');
            option.value = room.id;
            option.textContent = room.name;
            if (this.currentRoom && room.id === this.currentRoom.id) {
                option.selected = true;
            }
            selector.appendChild(option);
        });
    }

    showRoomSelector() {
        const modal = document.getElementById('roomModal');
        modal.style.display = 'flex';

        // Zaktualizuj listę gabinetów
        this.updateRoomSelector();
    }

    hideRoomSelector() {
        const modal = document.getElementById('roomModal');
        modal.style.display = 'none';
    }

    async changeRoom(roomId) {
        if (!roomId) return;

        const selectedRoom = this.availableRooms.find(room => room.id == roomId);
        if (!selectedRoom) return;

        this.showLoading(true);

        try {
            // Sprawdź czy gabinet jest dostępny dla tego lekarza w tym dniu
            const response = await fetch(`${this.apiBaseUrl}/doctor/check-room-availability`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    doctor_id: this.currentDoctor.id,
                    room_id: roomId,
                    date: new Date().toISOString().split('T')[0] // Dzisiejsza data
                })
            });

            const data = await response.json();

            if (data.success) {
                this.currentRoom = selectedRoom;

                // Zaktualizuj sesję
                const now = new Date();
                const midnight = new Date(now);
                midnight.setHours(24, 0, 0, 0);

                localStorage.setItem('doctorSession', JSON.stringify({
                    doctor: this.currentDoctor,
                    room: this.currentRoom,
                    availableRooms: this.availableRooms,
                    expiresAt: midnight.getTime()
                }));

                this.updateDoctorInfo();
                this.hideRoomSelector();

                // Odśwież wizyty dla nowego gabinetu
                await this.loadAppointments();

                this.showSuccess('Gabinety zmieniony pomyślnie');
            } else {
                this.showError(data.message || 'Nie można zmienić gabinetu');
            }
        } catch (error) {
            console.error('Błąd zmiany gabinetu:', error);
            this.showError('Błąd połączenia z serwerem');
        } finally {
            this.showLoading(false);
        }
    }

    async loadAppointments() {
        if (!this.currentRoom || !this.currentDoctor) {
            console.log('Brak danych o gabinecie lub lekarzu - pomijam ładowanie wizyt');
            return;
        }

        try {
            console.log('Ładowanie wizyt dla:', {
                roomId: this.currentRoom.id,
                doctorId: this.currentDoctor.id,
                date: this.selectedDate
            });

            // Sprawdź wizyty dla wybranej daty (bez automatycznego przełączania)
            const response = await fetch(`${this.apiBaseUrl}/doctor/appointments?doctor_id=${this.currentDoctor.id}&date=${this.selectedDate}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            const data = await response.json();
            console.log(`Sprawdzanie wizyt dla daty ${this.selectedDate}:`, data);

            // Zawsze upewnij się, że sekcja aktualnej wizyty jest widoczna, niezależnie od wyniku
            this.forceShowCurrentAppointment();

            if (data.success && data.data && data.data.all_appointments && data.data.all_appointments.length > 0) {
                // Znaleziono wizyty - przetwórz je
                this.allAppointments = data.data.all_appointments.map(apt => ({
                    ...apt,
                    isActive: apt.status === 'current'
                }));

                // Znajdź aktualną wizytę (ta z statusem 'current')
                this.currentAppointment = this.allAppointments.find(apt => apt.isActive) || null;

                // Poprawnie przypisz oczekujące wizyty (tylko te ze statusem 'waiting')
                this.waitingAppointments = this.allAppointments.filter(apt => apt.status === 'waiting');

                // Sortuj wszystkie wizyty według czasu
                this.allAppointments.sort((a, b) => a.appointment_time.localeCompare(b.appointment_time));

                console.log('Zaktualizowane dane:', {
                    currentAppointment: this.currentAppointment,
                    waitingCount: this.waitingAppointments.length,
                    allAppointments: this.allAppointments.length,
                    selectedDate: this.selectedDate,
                    statusCounts: {
                        waiting: this.allAppointments.filter(a => a.status === 'waiting').length,
                        current: this.allAppointments.filter(a => a.status === 'current').length,
                        completed: this.allAppointments.filter(a => a.status === 'completed' || a.status === 'closed').length
                    }
                });

                this.updateAppointmentDisplay();
                this.updateWaitingList();
                await this.updateStats();
            } else {
                // Brak wizyt na wybraną datę - pokaż pustą listę
                console.log(`Brak wizyt na datę ${this.selectedDate}`);
                this.currentAppointment = null;
                this.waitingAppointments = [];
                this.allAppointments = [];
                this.updateAppointmentDisplay();
                this.updateWaitingList();
                await this.updateStats();
            }

            // Zawsze upewnij się, że sekcja aktualnej wizyty jest widoczna po załadowaniu danych
            setTimeout(() => {
                this.ensureButtonsVisibility();
            }, 50);

        } catch (error) {
            console.error('Błąd połączenia podczas ładowania wizyt:', error);
            // Nawet w przypadku błędu upewnij się, że sekcja jest widoczna
            this.forceShowCurrentAppointment();
        }
    }

    updateAppointmentDisplay() {
        const timeElement = document.getElementById('currentAppointmentTime');
        const nameElement = document.getElementById('currentPatientName');
        const nextBtn = document.getElementById('nextBtn');
        const previousBtn = document.getElementById('previousBtn');

        // Zawsze upewnij się, że sekcja jest widoczna przed aktualizacją
        this.forceShowCurrentAppointment();

        console.log('updateAppointmentDisplay:', {
            hasCurrentAppointment: !!this.currentAppointment,
            currentAppointment: this.currentAppointment,
            waitingCount: this.waitingAppointments.length
        });

        if (this.currentAppointment) {
            timeElement.textContent = DataHelper.formatAppointmentTime(this.currentAppointment.appointment_time);
            nameElement.textContent = DataHelper.normalizeName(this.currentAppointment.patient_name) || 'Pacjent';

            // Pokaż/ukryj ikonkę obecności pacjenta
            const presenceElement = document.getElementById('currentPatientPresence');
            if (presenceElement) {
                if (this.currentAppointment.is_patient_present === 1) {
                    presenceElement.style.display = 'block';
                } else {
                    presenceElement.style.display = 'none';
                }
            }

            // Przycisk "Następna" - nawigacja do następnej wizyty
            nextBtn.disabled = false;
            nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Następna';
            nextBtn.title = 'Przejdź do następnej wizyty';

            // Przycisk "Poprzednia" - nawigacja do poprzedniej wizyty
            previousBtn.disabled = false;
            previousBtn.title = 'Przejdź do poprzedniej wizyty';

            console.log('Przycisk Poprz. ustawiony jako aktywny');
        } else {
            timeElement.textContent = '--:--';
            nameElement.textContent = 'Brak aktualnej wizyty';

            // Ukryj ikonkę obecności gdy nie ma aktualnej wizyty
            const presenceElement = document.getElementById('currentPatientPresence');
            if (presenceElement) {
                presenceElement.style.display = 'none';
            }

            // Przycisk "Następna" - aktywny gdy są oczekujące wizyty
            nextBtn.disabled = this.waitingAppointments.length === 0;
            nextBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Następna';
            if (this.waitingAppointments.length === 0) {
                nextBtn.title = 'Brak oczekujących wizyt';
            } else {
                nextBtn.title = 'Wywołaj pierwszą wizytę';
            }

            // Przycisk "Poprzednia" - wyłączony gdy nie ma aktualnej wizyty
            previousBtn.disabled = true;
            previousBtn.title = 'Brak aktualnej wizyty';

            console.log('Przycisk Poprz. ustawiony jako nieaktywny');
        }

        // Dodaj wizualne wskazówki o stanie przycisków
        if (nextBtn.disabled) {
            nextBtn.classList.add('btn-secondary');
            nextBtn.classList.remove('btn-success');
        } else {
            nextBtn.classList.remove('btn-secondary');
            nextBtn.classList.add('btn-success');
        }

        if (previousBtn.disabled) {
            previousBtn.classList.add('btn-secondary');
            previousBtn.classList.remove('btn-outline-secondary');
        } else {
            previousBtn.classList.remove('btn-secondary');
            previousBtn.classList.add('btn-outline-secondary');
        }

        // Zapewnia widoczność przycisków w trybie PWA
        this.ensureButtonsVisibility();

        console.log('Stan przycisków po aktualizacji:', {
            nextBtnDisabled: nextBtn.disabled,
            previousBtnDisabled: previousBtn.disabled,
            previousBtnClasses: previousBtn.className
        });

        // Aktualizuj wysokości sticky elementów po zmianie wyświetlania wizyt
        setTimeout(() => {
            if (typeof updateStickyHeights === 'function') {
                updateStickyHeights();
            }
        }, 50);
    }

    updateWaitingList() {
        const container = document.getElementById('waitingList');

        if (!this.allAppointments || this.allAppointments.length === 0) {
            container.innerHTML = `
                <div class="no-appointments">
                    <i class="fas fa-inbox"></i>
                    <p>Brak wizyt</p>
                </div>
            `;
            // Upewnij się, że sekcja aktualnej wizyty jest widoczna nawet gdy nie ma wizyt
            this.ensureButtonsVisibility();
            return;
        }

        container.innerHTML = this.allAppointments.map((appointment) => {
            let cssClass = '';
            if (appointment.isActive) {
                cssClass = 'active';
            } else if (appointment.status === 'closed' || appointment.status === 'completed') {
                cssClass = 'closed';
            }

            // Ikonka obecności pacjenta - wyświetlana po lewej stronie przycisków akcji
            // Teraz jako klikalny przycisk do przełączania statusu
            const presenceIcon = appointment.is_patient_present === 1 ?
                `<button class="btn btn-sm presence-btn present" data-id="${appointment.id}" title="Pacjent obecny (kliknij aby zmienić)">
                    <i class="fas fa-user"></i>
                </button>` :
                `<button class="btn btn-sm presence-btn absent" data-id="${appointment.id}" title="Pacjent nieobecny (kliknij aby zmienić)">
                    <i class="far fa-user"></i>
                </button>`;

            // Przycisk zamykania wizyty dla wszystkich wizyt oprócz aktualnie wywołanej
            const closeButton = !appointment.isActive && (appointment.status === 'waiting' || appointment.status === 'current') ?
                `<button class="btn btn-sm btn-danger close-appointment-btn" data-id="${appointment.id}" title="Zamknij wizytę">
                    <i class="fas fa-times"></i>
                </button>` : '';

            // Przycisk play dla wszystkich wizyt oprócz aktualnie wywołanej
            const playButton = !appointment.isActive ?
                `<button class="btn btn-sm btn-primary call-patient-btn" data-id="${appointment.id}" title="Wywołaj pacjenta">
                    <i class="fas fa-play"></i>
                </button>` : '';

            return `
                <div class="waiting-item ${cssClass}" data-id="${appointment.id}">
                    <div class="waiting-time">${DataHelper.formatAppointmentTime(appointment.appointment_time)}</div>
                    <div class="waiting-patient">${DataHelper.normalizeName(appointment.patient_name) || 'Pacjent'}</div>
                    <div class="waiting-actions">
                        ${presenceIcon}
                        ${closeButton}
                        ${playButton}
                    </div>
                </div>
            `;
        }).join('');

        // Dodaj event listenery dla przycisków wywołania
        this.setupCallPatientButtons();
        
        // Dodaj event listenery dla przycisków zamykania wizyt
        this.setupCloseAppointmentButtons();
        
        // Dodaj event listenery dla przycisków obecności pacjenta
        this.setupPresenceButtons();

        // Upewnij się, że sekcja aktualnej wizyty jest widoczna gdy są wizyty
        this.ensureButtonsVisibility();

        // Aktualizuj wysokości sticky elementów po zmianie listy oczekujących
        setTimeout(() => {
            if (typeof updateStickyHeights === 'function') {
                updateStickyHeights();
            }
        }, 50);
    }

    setupCallPatientButtons() {
        // Usuń poprzednie event listenery
        const existingButtons = document.querySelectorAll('.call-patient-btn');
        existingButtons.forEach(btn => {
            btn.removeEventListener('click', this.handleCallPatient);
        });

        // Dodaj nowe event listenery
        const callButtons = document.querySelectorAll('.call-patient-btn');
        callButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleCallPatient(e));
        });
    }

    setupCloseAppointmentButtons() {
        // Usuń poprzednie event listenery
        const existingButtons = document.querySelectorAll('.close-appointment-btn');
        existingButtons.forEach(btn => {
            btn.removeEventListener('click', this.handleCloseAppointment);
        });

        // Dodaj nowe event listenery
        const closeButtons = document.querySelectorAll('.close-appointment-btn');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleCloseAppointment(e));
        });
    }
    
    setupPresenceButtons() {
        // Usuń poprzednie event listenery
        const existingButtons = document.querySelectorAll('.presence-btn');
        existingButtons.forEach(btn => {
            btn.removeEventListener('click', this.handleTogglePresence);
        });

        // Dodaj nowe event listenery
        const presenceButtons = document.querySelectorAll('.presence-btn');
        presenceButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleTogglePresence(e));
        });
    }
    
    async handleTogglePresence(event) {
        const appointmentId = event.target.closest('.presence-btn').dataset.id;
        const isPresent = event.target.closest('.presence-btn').classList.contains('present');

        if (!appointmentId) {
            console.error('Brak ID wizyty');
            return;
        }

        if (!this.isOnline) {
            this.showError('Brak połączenia z internetem. Sprawdź połączenie i spróbuj ponownie.');
            return;
        }

        if (!this.currentRoom || !this.currentDoctor) {
            this.showError('Brak danych o gabinecie lub lekarzu. Spróbuj się zalogować ponownie.');
            return;
        }

        this.showLoading(true);

        try {
            console.log('Przełączam status obecności pacjenta:', appointmentId, 'obecny:', !isPresent);

            const response = await fetch(`${this.apiBaseUrl}/doctor/toggle-patient-presence`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    doctor_id: this.currentDoctor.id,
                    appointment_id: appointmentId,
                    is_present: !isPresent
                })
            });

            const data = await response.json();

            if (data.success) {
                const message = !isPresent ? 'Oznaczono pacjenta jako obecnego' : 'Oznaczono pacjenta jako nieobecnego';
                this.showSuccess(message);
                await this.loadAppointments();
                await this.updateStats();
                this.animateTransition();
            } else {
                this.showError(data.message || 'Błąd podczas zmiany statusu obecności');
            }
        } catch (error) {
            console.error('Błąd zmiany statusu obecności:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    async handleCallPatient(event) {
        const appointmentId = event.target.closest('.call-patient-btn').dataset.id;

        if (!appointmentId) {
            console.error('Brak ID wizyty');
            return;
        }

        if (!this.isOnline) {
            this.showError('Brak połączenia z internetem. Sprawdź połączenie i spróbuj ponownie.');
            return;
        }

        if (!this.currentRoom || !this.currentDoctor) {
            this.showError('Brak danych o gabinecie lub lekarzu. Spróbuj się zalogować ponownie.');
            return;
        }

        this.showLoading(true);

        try {
            console.log('Wywołuję konkretną wizytę:', appointmentId);

            const data = await this.apiClient.callSpecificAppointment(
                this.currentDoctor.id,
                appointmentId
            );

            if (data.success) {
                this.showSuccess('Pacjent został wywołany!');
                await this.loadAppointments();
                await this.updateStats();
                this.animateTransition();
            } else {
                this.showError(data.error || 'Błąd podczas wywołania pacjenta');
            }
        } catch (error) {
            console.error('Błąd wywołania pacjenta:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    async handleCloseAppointment(event) {
        const appointmentId = event.target.closest('.close-appointment-btn').dataset.id;

        if (!appointmentId) {
            console.error('Brak ID wizyty');
            return;
        }

        if (!this.isOnline) {
            this.showError('Brak połączenia z internetem. Sprawdź połączenie i spróbuj ponownie.');
            return;
        }

        if (!this.currentRoom || !this.currentDoctor) {
            this.showError('Brak danych o gabinecie lub lekarzu. Spróbuj się zalogować ponownie.');
            return;
        }

        // Potwierdzenie zamknięcia wizyty
        if (!confirm('Czy na pewno chcesz zamknąć tę wizytę?')) {
            return;
        }

        this.showLoading(true);

        try {
            console.log('Zamykam wizytę:', appointmentId);

            const response = await fetch(`${this.apiBaseUrl}/doctor/close-appointment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    doctor_id: this.currentDoctor.id,
                    appointment_id: appointmentId
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('Wizyta została zamknięta!');
                await this.loadAppointments();
                await this.updateStats();
                this.animateTransition();
            } else {
                this.showError(data.message || 'Błąd podczas zamykania wizyty');
            }
        } catch (error) {
            console.error('Błąd zamykania wizyty:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    async updateStats() {
        const completedCount = document.getElementById('completedCount');
        const waitingCount = document.getElementById('waitingCount');

        if (!this.allAppointments || this.allAppointments.length === 0) {
            completedCount.textContent = '0';
            waitingCount.textContent = '0';
            return;
        }

        // Poprawne zliczanie na podstawie statusu wizyt
        let completed = 0;
        let waiting = 0;
        let current = 0;

        for (const appointment of this.allAppointments) {
            // Zliczaj na podstawie statusu wizyty, nie pozycji
            if (appointment.status === 'closed' || appointment.status === 'completed') {
                completed++;
            } else if (appointment.status === 'current' || appointment.isActive) {
                current++;
            } else if (appointment.status === 'waiting') {
                waiting++;
            }
        }

        completedCount.textContent = completed;
        waitingCount.textContent = waiting;

        console.log('Zaktualizowane statystyki:', {
            completed,
            waiting,
            current,
            total: this.allAppointments.length,
            details: this.allAppointments.map(a => ({
                patient: a.patient_name,
                status: a.status,
                isActive: a.isActive
            }))
        });
    }

    async callNextAppointment() {
        console.log('Wywołanie następnego pacjenta...', {
            isOnline: this.isOnline,
            currentRoom: this.currentRoom,
            currentDoctor: this.currentDoctor,
            waitingCount: this.waitingAppointments.length,
            hasCurrentAppointment: !!this.currentAppointment
        });

        if (!this.isOnline) {
            this.showError('Brak połączenia z internetem. Sprawdź połączenie i spróbuj ponownie.');
            return;
        }

        if (!this.currentRoom || !this.currentDoctor) {
            this.showError('Brak danych o gabinecie lub lekarzu. Spróbuj się zalogować ponownie.');
            return;
        }

        // Sprawdź czy są jakiekolwiek wizyty do wywołania
        if (this.waitingAppointments.length === 0 && !this.currentAppointment) {
            this.showError('Brak wizyt do wywołania.');
            return;
        }

        this.showLoading(true);

        try {
            // Znajdź następną wizytę do wywołania
            let nextAppointmentToCall = null;
            
            if (this.currentAppointment) {
                // Jeśli jest aktualna wizyta, znajdź pierwszą oczekującą
                // Używamy wszystkich wizyt, aby znaleźć następną po aktualnej
                console.log('Szukam następnej wizyty po aktualnej:', this.currentAppointment.patient_name);
                console.log('Wszystkie wizyty:', this.allAppointments.map(a => `${a.patient_name} (${a.appointment_time}) - ${a.status}`));

                if (this.allAppointments && this.allAppointments.length > 0) {
                    const currentIndex = this.allAppointments.findIndex(apt =>
                        apt.id === this.currentAppointment.id
                    );

                    console.log('Indeks aktualnej wizyty:', currentIndex);

                    if (currentIndex !== -1) {
                        // Szukaj następnej wizyty po aktualnej (nawet jeśli niektóre zostały pominięte)
                        for (let i = currentIndex + 1; i < this.allAppointments.length; i++) {
                            console.log(`Sprawdzam wizytę ${i}: ${this.allAppointments[i].patient_name} - status: ${this.allAppointments[i].status}`);
                            if (this.allAppointments[i].status === 'waiting') {
                                nextAppointmentToCall = this.allAppointments[i];
                                console.log('Znaleziono następną wizytę:', nextAppointmentToCall.patient_name);
                                break;
                            }
                        }
                    }
                }

                // Fallback: jeśli nie znaleziono w posortowanej liście, użyj pierwszej oczekującej
                if (!nextAppointmentToCall && this.waitingAppointments.length > 0) {
                    console.log('Używam fallback - pierwszą oczekującą:', this.waitingAppointments[0].patient_name);
                    nextAppointmentToCall = this.waitingAppointments[0];
                }
            } else {
                // Jeśli nie ma aktualnej wizyty, wywołaj pierwszą oczekującą
                nextAppointmentToCall = this.waitingAppointments.length > 0 ? this.waitingAppointments[0] : null;
            }

            if (!nextAppointmentToCall) {
                this.showError('Brak kolejnych wizyt do wywołania.');
                return;
            }

            console.log('Wywołuję wizytę:', nextAppointmentToCall.id);
            
            // Wywołaj konkretną wizytę (zamknie aktualną i wywoła nową w jednej operacji)
            const data = await this.apiClient.callSpecificAppointment(
                this.currentDoctor.id,
                nextAppointmentToCall.id
            );

            if (data.success) {
                this.showSuccess('Kolejny pacjent został wywołany!');
            } else {
                this.showError(data.error || 'Błąd podczas wywołania pacjenta');
            }

            // Odśwież dane
            await this.loadAppointments();
            // Odśwież statystyki
            await this.updateStats();
            // Animacja przejścia
            this.animateTransition();
        } catch (error) {
            console.error('Błąd wywołania pacjenta:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    async completeCurrentAppointment() {
        if (!this.currentAppointment) {
            console.log('Brak aktualnej wizyty do zakończenia');
            return;
        }

        this.showLoading(true);

        try {
            console.log('Kończenie aktualnej wizyty przez skip-current...');

            const response = await fetch(`${this.apiBaseUrl}/doctor/skip-current`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    doctor_id: this.currentDoctor.id
                })
            });

            const data = await response.json();
            console.log('Odpowiedź zakończenia wizyty:', data);

            if (data.success) {
                this.showSuccess('Aktualna wizyta została zakończona. Kliknij ponownie aby wywołać następną.');
                // Odśwież dane
                await this.loadAppointments();
                // Odśwież statystyki
                await this.updateStats();
            } else {
                this.showError(data.message || 'Błąd podczas kończenia wizyty');
            }
        } catch (error) {
            console.error('Błąd kończenia wizyty:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    // Funkcja pomocnicza do cichego kończenia wizyty (bez komunikatów)
    async completeCurrentAppointmentSilent() {
        if (!this.currentAppointment) {
            return;
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/doctor/skip-current`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    doctor_id: this.currentDoctor.id
                })
            });

            const data = await response.json();

            if (!data.success) {
                console.error('Błąd podczas cichego kończenia wizyty:', data.message);
            }
        } catch (error) {
            console.error('Błąd cichego kończenia wizyty:', error);
        }
    }

    async goToPreviousAppointment() {
        console.log('Nawigacja do poprzedniej wizyty...', {
            isOnline: this.isOnline,
            currentRoom: this.currentRoom,
            currentDoctor: this.currentDoctor,
            hasCurrentAppointment: !!this.currentAppointment
        });

        if (!this.isOnline) {
            this.showError('Brak połączenia z internetem. Sprawdź połączenie i spróbuj ponownie.');
            return;
        }

        if (!this.currentRoom || !this.currentDoctor) {
            this.showError('Brak danych o gabinecie lub lekarzu. Spróbuj się zalogować ponownie.');
            return;
        }

        if (!this.currentAppointment) {
            this.showError('Brak aktualnej wizyty.');
            return;
        }

        // Znajdź poprzednią wizytę na podstawie listy wszystkich wizyt
        let previousAppointment = null;

        if (this.allAppointments && this.allAppointments.length > 0) {
            const currentIndex = this.allAppointments.findIndex(app => app.id === this.currentAppointment.id);

            if (currentIndex !== -1) {
                // Szukaj poprzedniej wizyty przed aktualną (która nie jest zamknięta)
                for (let i = currentIndex - 1; i >= 0; i--) {
                    const appointment = this.allAppointments[i];
                    if (appointment.status === 'waiting' || appointment.status === 'completed' || appointment.status === 'closed') {
                        previousAppointment = appointment;
                        break;
                    }
                }
            }
        }

        if (!previousAppointment) {
            this.showError('Brak poprzedniej wizyty do wywołania.');
            return;
        }

        this.showLoading(true);

        try {
            console.log('Wywołuję poprzednią wizytę:', previousAppointment.id, previousAppointment.patient_name);

            // Wywołaj konkretną wizytę (zamknie aktualną i wywoła poprzednią w jednej operacji)
            const data = await this.apiClient.callSpecificAppointment(
                this.currentDoctor.id,
                previousAppointment.id
            );

            if (data.success) {
                this.showSuccess('Przeszedłeś do poprzedniej wizyty');
                await this.loadAppointments();
                await this.updateStats();
                this.animateTransition();
            } else {
                this.showError(data.message || 'Błąd podczas nawigacji do poprzedniej wizyty');
            }
        } catch (error) {
            console.error('Błąd nawigacji do poprzedniej wizyty:', error);
            this.showError('Błąd połączenia z serwerem. Sprawdź połączenie internetowe.');
        } finally {
            this.showLoading(false);
        }
    }

    animateTransition() {
        const appointmentDisplay = document.querySelector('.current-appointment');
        if (appointmentDisplay) {
            appointmentDisplay.classList.add('fade-in');

            setTimeout(() => {
                appointmentDisplay.classList.remove('fade-in');
            }, 500);
        }
    }

    startAutoUpdate() {
        // Uruchom uproszczone monitorowanie zmian
        if (this.currentDoctor && this.currentRoom) {
            // Przechowuj timestamp ostatniej zmiany
            this.lastAppointmentTimestamp = 0;
            
            // Sprawdzaj zmiany w konfigurowalnym interwale
            const checkIntervalMs = this.config.dataMonitoring.appointmentsCheckInterval * 1000;
            this.changeMonitoringInterval = setInterval(async () => {
                try {
                    // Wywołaj uproszczony endpoint
                    const response = await this.apiClient.checkForChanges('appointments');

                    if (response.success && response.data) {
                        const serverTimestamp = response.data.lastUpdate;

                        // Porównaj timestampy
                        if (serverTimestamp > this.lastAppointmentTimestamp) {
                            console.log('Wykryto zmiany w wizytach, odświeżam dane...');
                            this.lastAppointmentTimestamp = serverTimestamp;
                            await this.loadAppointments();
                        }
                    }
                } catch (error) {
                    console.error('Błąd podczas sprawdzania zmian:', error);
                }
            }, checkIntervalMs);

            console.log(`Monitorowanie zmian w wizytach skonfigurowane na ${this.config.dataMonitoring.appointmentsCheckInterval} sekund`);
        }
    }

    logout() {
        // Zatrzymaj monitorowanie zmian
        if (this.changeMonitoringInterval) {
            clearInterval(this.changeMonitoringInterval);
            this.changeMonitoringInterval = null;
        }

        // Wyczyść localStorage
        localStorage.removeItem('accessCode');
        localStorage.removeItem('doctorSession');

        // Wyczyść dane
        this.currentDoctor = null;
        this.currentRoom = null;
        this.availableRooms = [];
        this.currentAppointment = null;
        this.waitingAppointments = [];
        this.lastAppointmentTimestamp = 0;

        // Ukryj przycisk instalacji
        this.hideInstallButton();

        // Wyczyść formularz logowania
        document.getElementById('loginEmail').value = '';
        document.getElementById('loginPassword').value = '';
        // Nie czyścimy identyfikatora placówki - ma być zapisany

        // Pokaż ekran logowania
        document.getElementById('mainScreen').classList.remove('active');
        document.getElementById('loginScreen').classList.add('active');
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        spinner.style.display = show ? 'flex' : 'none';
    }

    showError(message) {
        const errorElement = document.getElementById('loginError');
        const messageElement = document.getElementById('errorMessage');

        messageElement.textContent = message;
        errorElement.style.display = 'block';

        // Ukryj po 5 sekundach
        setTimeout(() => {
            this.hideError();
        }, 5000);
    }

    showSuccess(message) {
        // Utwórz tymczasowy komunikat sukcesu
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        successAlert.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
        successAlert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            <strong>Sukces!</strong> ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        document.body.appendChild(successAlert);

        // Usuń po 3 sekundach
        setTimeout(() => {
            if (successAlert.parentElement) {
                successAlert.remove();
            }
        }, 3000);
    }

    hideError() {
        const errorElement = document.getElementById('loginError');
        errorElement.style.display = 'none';
    }

    initInstallPrompt() {
        // Sprawdź czy przeglądarka obsługuje instalację PWA
        if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
            console.log('PWA: Przeglądarka nie obsługuje PWA');
            return;
        }

        // Poproś o uprawnienia do powiadomień
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }

        // Nasłuchuj na event beforeinstallprompt
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: beforeinstallprompt event fired');
            // Zapobiegaj domyślnemu wyświetleniu promptu
            e.preventDefault();
            // Zapisz event do późniejszego użycia
            this.deferredPrompt = e;
            // Pokaż przycisk instalacji tylko na ekranie głównym
            if (document.getElementById('mainScreen').classList.contains('active')) {
                this.showInstallButton();
            }
        });

        // Nasłuchuj na event appinstalled
        window.addEventListener('appinstalled', (evt) => {
            console.log('PWA: Aplikacja została zainstalowana');
            this.hideInstallButton();
            this.deferredPrompt = null;

            // Pokaż powiadomienie o sukcesie
            this.showNotification('Aplikacja została zainstalowana! Możesz teraz uruchomić ją z pulpitu.', 'success');

            // Opcjonalnie: przekieruj do zainstalowanej aplikacji
            setTimeout(() => {
                if (window.matchMedia('(display-mode: standalone)').matches) {
                    window.location.reload();
                }
            }, 2000);
        });

        // Sprawdź czy aplikacja jest już zainstalowana
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true ||
            document.referrer.includes('android-app://')) {
            console.log('PWA: Aplikacja już zainstalowana');
            this.hideInstallButton();
            return;
        }

        // Sprawdź typ przeglądarki i pokaż odpowiedni przycisk
        this.checkBrowserCompatibility();
    }

    checkBrowserCompatibility() {
        // Detekcja przeglądarki Firefox
        const isFirefox = typeof InstallTrigger !== 'undefined' || navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
        
        if (isFirefox) {
            console.log('Wykryto przeglądarkę Firefox - pokazuję komunikat o braku wsparcia');
            this.showFirefoxWarning();
            return;
        }

        // Detekcja przeglądarek opartych na Chromium
        const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
        const isEdge = /Edg/.test(navigator.userAgent);
        const isOpera = /OPR/.test(navigator.userAgent);
        const isVivaldi = /Vivaldi/.test(navigator.userAgent);
        
        if (isChrome || isEdge || isOpera || isVivaldi) {
            console.log('Wykryto przeglądarkę opartą na Chromium - pokazuję przycisk instalacji');
            this.showInstallButton();
        } else {
            console.log('PWA: Przeglądarka nie jest wspierana');
            this.hideInstallButton();
        }
    }

    showFirefoxWarning() {
        const installBtn = document.getElementById('installBtn');
        if (installBtn) {
            installBtn.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <span>Do zainstalowania program wymaga przeglądarki:<br> Chrome, Opera, Vivaldi lub Edge</span>
            `;
            installBtn.classList.add('firefox-warning');
            installBtn.classList.add('show');
            installBtn.disabled = true;
            
            // Zmień styl przycisku dla ostrzeżenia
            installBtn.style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a24)';
            installBtn.style.cursor = 'not-allowed';
        }
    }

    showInstallButton() {
        // Sprawdź czy aplikacja nie jest już zainstalowana
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true ||
            document.referrer.includes('android-app://')) {
            console.log('PWA: Aplikacja już zainstalowana - ukrywam przycisk');
            this.hideInstallButton();
            return;
        }

        // Sprawdź czy to Firefox
        const isFirefox = typeof InstallTrigger !== 'undefined' || navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
        if (isFirefox) {
            console.log('PWA: Firefox - nie pokazuję przycisku instalacji');
            return;
        }

        const installBtn = document.getElementById('installBtn');
        if (installBtn) {
            installBtn.classList.add('show');
            installBtn.style.display = 'flex';
            console.log('PWA: Pokazuję przycisk instalacji');
        }
    }

    hideInstallButton() {
        const installBtn = document.getElementById('installBtn');
        const navigationButtons = document.querySelector('.navigation-buttons');

        if (installBtn) {
            installBtn.classList.remove('show');
            console.log('PWA: Ukrywam przycisk instalacji');
        }

        // Ukryj całą sekcję navigation-buttons jeśli nie ma przycisków do pokazania
        if (navigationButtons) {
            const visibleButtons = navigationButtons.querySelectorAll('.btn.show, .btn:not(.show)');
            const hasVisibleButtons = Array.from(visibleButtons).some(btn =>
                btn.style.display !== 'none' && !btn.classList.contains('show') === false
            );

            if (!hasVisibleButtons || window.matchMedia('(display-mode: standalone)').matches) {
                navigationButtons.style.display = 'none';
                console.log('PWA: Ukrywam navigation-buttons');
            }
        }
    }

    async installApp() {
        // Sprawdź czy to Firefox - nie obsługujemy instalacji
        const isFirefox = typeof InstallTrigger !== 'undefined' || navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
        if (isFirefox) {
            this.showNotification('Program wymaga przeglądarki opartej na Chromium (Chrome, Opera, Vivaldi, Edge)', 'warning');
            return;
        }

        if (!this.deferredPrompt) {
            console.log('PWA: Brak promptu instalacji');

            // Sprawdź czy to Chrome/Edge/Opera/Vivaldi
            const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
            const isEdge = /Edg/.test(navigator.userAgent);
            const isOpera = /OPR/.test(navigator.userAgent);
            const isVivaldi = /Vivaldi/.test(navigator.userAgent);

            if (isChrome || isEdge || isOpera || isVivaldi) {
                this.showNotification('Kliknij ikonę instalacji w pasku adresu przeglądarki', 'info');
            } else {
                this.showNotification('Aplikacja nie może być zainstalowana w tej przeglądarce. Użyj Chrome, Edge, Opery lub Vivaldi.', 'warning');
            }
            return;
        }

        try {
            // Pokaż prompt instalacji
            this.deferredPrompt.prompt();

            // Czekaj na odpowiedź użytkownika
            const { outcome } = await this.deferredPrompt.userChoice;

            console.log(`PWA: Użytkownik ${outcome === 'accepted' ? 'zaakceptował' : 'odrzucił'} instalację`);

            if (outcome === 'accepted') {
                this.showNotification('Instalacja w toku...', 'info');
            } else {
                this.showNotification('Instalacja została anulowana', 'info');
            }

            // Wyczyść prompt
            this.deferredPrompt = null;

            // Ukryj przycisk
            this.hideInstallButton();

        } catch (error) {
            console.error('PWA: Błąd podczas instalacji:', error);
            this.showNotification('Wystąpił błąd podczas instalacji', 'error');
        }
    }

    registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/lekarz/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                        this.swRegistration = registration;

                        // Sprawdź czy Service Worker jest aktywny
                        if (registration.active) {
                            console.log('SW is active');
                        }

                        // Nasłuchuj na aktualizacje Service Worker
                        registration.addEventListener('updatefound', () => {
                            console.log('SW update found');
                            const newWorker = registration.installing;
                            
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // Nowy SW jest zainstalowany i czeka na aktywację
                                    console.log('New SW waiting to activate');
                                    this.isUpdateAvailable = true;
                                    this.autoApplyUpdate();
                                }
                            });
                        });

                        // Sprawdź czy jest oczekujący update
                        if (registration.waiting) {
                            console.log('SW waiting to activate');
                            this.isUpdateAvailable = true;
                            this.autoApplyUpdate();
                        }
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        } else {
            console.log('PWA: Service Worker nie jest obsługiwany');
        }
    }

    checkForUpdates() {
        // Sprawdzaj aktualizacje w konfigurowalnym interwale (domyślnie 2 minuty)
        setInterval(() => {
            if (this.swRegistration) {
                this.swRegistration.update();
            }
        }, this.updateCheckInterval);

        console.log(`Sprawdzanie aktualizacji skonfigurowane na ${this.updateCheckInterval / 1000 / 60} minut`);
    }

    showUpdateNotification() {
        // Pokaż powiadomienie o dostępnej aktualizacji
        const updateToast = document.createElement('div');
        updateToast.className = 'update-toast';
        updateToast.innerHTML = `
            <div class="update-toast-content">
                <i class="fas fa-download"></i>
                <span>Dostępna aktualizacja aplikacji</span>
                <button id="updateBtn" class="btn btn-sm btn-primary">Aktualizuj</button>
            </div>
        `;
        
        // Dodaj style dla toast
        updateToast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 10px;
            animation: slideIn 0.3s ease-out;
        `;
        
        document.body.appendChild(updateToast);
        
        // Dodaj event listener do przycisku aktualizacji
        document.getElementById('updateBtn').addEventListener('click', () => {
            this.applyUpdate();
            updateToast.remove();
        });
        
        // Automatycznie ukryj po 10 sekundach
        setTimeout(() => {
            if (updateToast.parentElement) {
                updateToast.remove();
            }
        }, 10000);
    }

    applyUpdate() {
        if (this.swRegistration && this.swRegistration.waiting) {
            // Pokaż komunikat o aktualizacji
            this.showNotification('Aplikacja jest aktualizowana...', 'info');

            // Wyślij wiadomość do Service Workera aby aktywował nową wersję
            this.swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });

            // Ustaw timeout na wypadek problemów z aktywacją SW
            const refreshTimeout = setTimeout(() => {
                console.log('Force refresh after timeout');
                window.location.reload();
            }, 3000);

            // Odśwież stronę po aktywacji nowego SW
            navigator.serviceWorker.addEventListener('controllerchange', () => {
                clearTimeout(refreshTimeout);
                console.log('Controller changed, refreshing page');
                window.location.reload();
            });
        }
    }

    autoApplyUpdate() {
        if (this.swRegistration && this.swRegistration.waiting) {
            console.log('Automatyczna instalacja aktualizacji...');

            // Wyślij wiadomość do Service Workera aby aktywował nową wersję
            this.swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });

            // Ustaw timeout na wypadek problemów z aktywacją SW (z konfiguracji)
            const timeoutMs = this.config.updates.forceRefreshTimeout * 1000;
            const refreshTimeout = setTimeout(() => {
                console.log('Force refresh after timeout during auto-update');
                window.location.reload();
            }, timeoutMs);

            // Odśwież stronę po aktywacji nowego SW
            navigator.serviceWorker.addEventListener('controllerchange', () => {
                clearTimeout(refreshTimeout);
                console.log('Controller changed during auto-update, refreshing page');
                window.location.reload();
            });
        }
    }

    displayAppVersion() {
        // Wyświetl wersję aplikacji w belce tytułowej
        this.updateTitleWithVersion();
    }

    /**
     * Zmiana konfiguracji czasu sprawdzania aktualizacji
     * @param {number} minutes - Nowy czas w minutach
     */
    setUpdateCheckInterval(minutes) {
        if (minutes < 1) {
            console.warn('Czas sprawdzania aktualizacji nie może być mniejszy niż 1 minuta');
            return;
        }

        this.config.updates.checkIntervalMinutes = minutes;
        this.updateCheckInterval = minutes * 60 * 1000;

        console.log(`Czas sprawdzania aktualizacji zmieniony na ${minutes} minut`);

        // Zapisz nową konfigurację do localStorage
        localStorage.setItem('appConfig', JSON.stringify(this.config));
    }



    updateTitleWithVersion() {
        // Dodaj wersję do tytułu strony
        const originalTitle = document.title;
        if (!originalTitle.includes(`v${this.appVersion}`)) {
            document.title = `${originalTitle} v${this.appVersion}`;
        }
    }

    showNotification(message, type = 'info') {
        // Sprawdź czy przeglądarka obsługuje powiadomienia
        if (!('Notification' in window)) {
            console.log('PWA: Przeglądarka nie obsługuje powiadomień');
            return;
        }

        // Ustaw ikonę na podstawie typu
        let icon = '/lekarz/icons/icon-192x192.png';
        let title = 'KtoOstatni.pl';

        switch (type) {
            case 'success':
                title = '✅ ' + title;
                break;
            case 'error':
                title = '❌ ' + title;
                break;
            case 'warning':
                title = '⚠️ ' + title;
                break;
            default:
                title = 'ℹ️ ' + title;
        }

        // Sprawdź uprawnienia
        if (Notification.permission === 'granted') {
            new Notification(title, {
                body: message,
                icon: icon,
                badge: icon,
                tag: 'doctor-panel-notification',
                requireInteraction: type === 'error' || type === 'warning'
            });
        } else if (Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    new Notification(title, {
                        body: message,
                        icon: icon,
                        badge: icon,
                        tag: 'doctor-panel-notification',
                        requireInteraction: type === 'error' || type === 'warning'
                    });
                }
            });
        }
    }

    /**
     * Zapewnia widoczność przycisków w trybie PWA
     */
    ensureButtonsVisibility() {
        // Sprawdź czy aplikacja działa w trybie standalone - zawsze wykonuj, nie tylko w PWA
        const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
        
        // Upewnij się, że sekcja aktualnej wizyty jest widoczna
        const currentAppointment = document.querySelector('.current-appointment');
        if (currentAppointment) {
            currentAppointment.style.display = 'block';
            currentAppointment.style.visibility = 'visible';
            currentAppointment.style.opacity = '1';
            currentAppointment.style.height = 'auto';
        }
        
        // Upewnij się, że przyciski są widoczne
        const appointmentActions = document.querySelector('.appointment-actions');
        if (appointmentActions) {
            appointmentActions.style.display = 'flex';
            appointmentActions.style.visibility = 'visible';
            appointmentActions.style.opacity = '1';
        }
        
        // Upewnij się, że przyciski poprzedni/następny są widoczne
        const nextBtn = document.getElementById('nextBtn');
        const previousBtn = document.getElementById('previousBtn');
        
        if (nextBtn) {
            nextBtn.style.display = 'inline-flex';
            nextBtn.style.visibility = 'visible';
            nextBtn.style.opacity = '1';
        }
        
        if (previousBtn) {
            previousBtn.style.display = 'inline-flex';
            previousBtn.style.visibility = 'visible';
            previousBtn.style.opacity = '1';
        }
        
        // Dodatkowo upewnij się, że kontent jest widoczny
        const appointmentContent = document.querySelector('.appointment-content');
        if (appointmentContent) {
            appointmentContent.style.display = 'block';
            appointmentContent.style.visibility = 'visible';
            appointmentContent.style.opacity = '1';
        }
        
        console.log(`${isStandalone ? 'PWA' : 'Browser'}: Zapewniono widoczność przycisków i sekcji aktualnej wizyty`);
    }

    /**
     * Wymusza pokazanie sekcji aktualnej wizyty - używane w sytuacjach krytycznych
     */
    forceShowCurrentAppointment() {
        const currentAppointment = document.querySelector('.current-appointment');
        const appointmentContent = document.querySelector('.appointment-content');
        const appointmentActions = document.querySelector('.appointment-actions');
        const nextBtn = document.getElementById('nextBtn');
        const previousBtn = document.getElementById('previousBtn');
        
        console.log('Force show current appointment section');
        
        if (currentAppointment) {
            currentAppointment.style.display = 'block !important';
            currentAppointment.style.visibility = 'visible !important';
            currentAppointment.style.opacity = '1 !important';
            currentAppointment.style.height = 'auto !important';
            currentAppointment.style.overflow = 'visible !important';
            currentAppointment.style.position = 'relative !important';
            currentAppointment.style.zIndex = '1 !important';
        }
        
        if (appointmentContent) {
            appointmentContent.style.display = 'block !important';
            appointmentContent.style.visibility = 'visible !important';
            appointmentContent.style.opacity = '1 !important';
        }
        
        if (appointmentActions) {
            appointmentActions.style.display = 'flex !important';
            appointmentActions.style.visibility = 'visible !important';
            appointmentActions.style.opacity = '1 !important';
        }
        
        if (nextBtn) {
            nextBtn.style.display = 'inline-flex !important';
            nextBtn.style.visibility = 'visible !important';
            nextBtn.style.opacity = '1 !important';
        }
        
        if (previousBtn) {
            previousBtn.style.display = 'inline-flex !important';
            previousBtn.style.visibility = 'visible !important';
            previousBtn.style.opacity = '1 !important';
        }
    }
}

// Funkcja do ustawiania wysokości elementów sticky
function updateStickyHeights() {
    const doctorInfo = document.querySelector('.doctor-info');
    const currentAppointment = document.querySelector('.current-appointment');

    if (doctorInfo) {
        const doctorInfoHeight = doctorInfo.offsetHeight;
        document.documentElement.style.setProperty('--doctor-info-height', `${doctorInfoHeight}px`);

        if (currentAppointment) {
            const currentAppointmentHeight = currentAppointment.offsetHeight;
            document.documentElement.style.setProperty('--current-appointment-height', `${currentAppointmentHeight}px`);
        }
    }
}

// Inicjalizacja aplikacji
document.addEventListener('DOMContentLoaded', () => {
    window.doctorApp = new DoctorApp();

    // Ustaw wysokości po załadowaniu DOM
    setTimeout(updateStickyHeights, 100);

    // Aktualizuj wysokości przy zmianie rozmiaru okna
    window.addEventListener('resize', updateStickyHeights);
});

// Service Worker registration
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/lekarz/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// Funkcje pomocnicze dla konsoli deweloperskiej
window.setUpdateCheckInterval = function(minutes) {
    if (window.doctorApp) {
        window.doctorApp.setUpdateCheckInterval(minutes);
    } else {
        console.error('Aplikacja nie jest jeszcze załadowana');
    }
};

window.getAppConfig = function() {
    if (window.doctorApp) {
        console.log('Aktualna konfiguracja:', window.doctorApp.config);
        return window.doctorApp.config;
    } else {
        console.error('Aplikacja nie jest jeszcze załadowana');
    }
};

window.forceUpdateCheck = function() {
    if (window.doctorApp && window.doctorApp.swRegistration) {
        console.log('Wymuszam sprawdzenie aktualizacji...');
        window.doctorApp.swRegistration.update();
    } else {
        console.error('Service Worker nie jest zarejestrowany');
    }
};

window.forceReload = function() {
    console.log('Wymuszam przeładowanie aplikacji...');
    window.location.reload();
};

window.debugUpdate = function() {
    if (window.doctorApp) {
        console.log('=== DEBUG AKTUALIZACJI ===');
        console.log('Wersja aplikacji:', window.doctorApp.appVersion);
        console.log('Konfiguracja:', window.doctorApp.config);
        console.log('SW Registration:', window.doctorApp.swRegistration);
        console.log('Update available:', window.doctorApp.isUpdateAvailable);
        console.log('Update check interval:', window.doctorApp.updateCheckInterval / 1000 / 60, 'minut');

        if (window.doctorApp.swRegistration) {
            console.log('SW state:', window.doctorApp.swRegistration.active?.state);
            console.log('SW waiting:', window.doctorApp.swRegistration.waiting);
            console.log('SW installing:', window.doctorApp.swRegistration.installing);
        }
    } else {
        console.error('Aplikacja nie jest załadowana');
    }
};