<?php

/**
 * Router główny aplikacji KtoOstatni.pl v4
 * Obsługuje wszystkie requesty do aplikacji
 */

define('KTOOSTATNI_APP', true);
require_once __DIR__ . '/config.php';

// Obsługa plików statycznych dla PHP dev server
if (php_sapi_name() === 'cli-server') {
    $requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $requestedFile = __DIR__ . $requestUri;

    // Jeśli plik istnieje i nie jest index.php, pozwól serwerowi go obsłużyć
    if (is_file($requestedFile) && basename($requestedFile) !== 'index.php') {
        return false; // Pozwól serwerowi obsłużyć plik statyczny
    }
}

// Pobierz ścieżkę z URL
$requestUri = $_SERVER['REQUEST_URI'];

// Usuń parametry GET z URL
$path = parse_url($requestUri, PHP_URL_PATH);

// Usuń początkowy i końcowy slash
$path = trim($path, '/');

// Dla PHP development server - sprawdź czy to bezpośrednie wywołanie index.php
if (php_sapi_name() === 'cli-server' && $path === 'index.php') {
    // Przekieruj na admin jeśli wywołano bezpośrednio index.php
    redirect('/admin');
}

// Jeśli ścieżka jest pusta, przekieruj na admin
if (empty($path)) {
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log("Index: Przekierowanie z pustej ścieżki na /admin");
    }
    redirect('/admin');
}

// Obsługa panelu admina
if (strpos($path, 'admin') === 0) {
    require_once __DIR__ . '/admin/index.php';
    exit;
}

// Obsługa API v2
if (strpos($path, 'api/v2') === 0) {
    require_once __DIR__ . '/api/v2/index.php';
    exit;
}

// Obsługa aplikacji PWA dla lekarzy
if (strpos($path, 'lekarz') === 0) {
    // Sprawdź czy to żądanie pliku statycznego w folderze lekarz
    $requestedFile = __DIR__ . '/' . $path;
    if (is_file($requestedFile)) {
        return false; // Pozwól serwerowi obsłużyć plik statyczny
    }

    // Dla wszystkich innych żądań w /lekarz przekieruj na index.html
    require_once __DIR__ . '/lekarz/index.html';
    exit;
}

// Obsługa wyświetlaczy publicznych
if (strpos($path, 'display/') === 0 || $path === 'display') {
    require_once __DIR__ . '/display/index.php';
    exit;
}

// Jeśli ścieżka zaczyna się od "display" ale nie pasuje do powyższych reguł,
// przekieruj na ekran parowania
if (strpos($path, 'display') === 0) {
    require_once __DIR__ . '/display/index.php';
    exit;
}

// Jeśli dotarliśmy tutaj, to nie znaleziono pasującej ścieżki
http_response_code(404);
die('Strona nie została znaleziona');
