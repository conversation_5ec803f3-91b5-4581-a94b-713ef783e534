{"timestamp": "2025-10-21 14:59:06", "syncCode": "sonokard", "source": "default", "data": {"source": "dr<PERSON><PERSON><PERSON>", "syncCode": "sonokard", "syncData": {"days": [{"date": "2025-10-21", "doctors": [{"doctorId": "**********", "doctorName": "dermatolog dr <PERSON>-Wójcik", "appointments": []}, {"doctorId": "**********", "doctorName": "dermatolog dr <PERSON>", "appointments": []}, {"doctorId": "**********", "doctorName": "<PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "112040", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Surma", "appointmentStart": "09:40", "appointmentDuration": 20, "phone_number": "+48 531 594 026", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "108984", "patientFirstName": "Katarzyna", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "10:00", "appointmentDuration": 20, "phone_number": "+48 663 595 649", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "110494", "patientFirstName": "<PERSON>", "patientLastName": "Orleańska", "appointmentStart": "10:20", "appointmentDuration": 20, "phone_number": "+48 664 521 992", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "111069", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Sobuck<PERSON>", "appointmentStart": "10:40", "appointmentDuration": 20, "phone_number": "+48 502 928 125", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "110838", "patientFirstName": "Daria", "patientLastName": "Rzeczycka-Bujak", "appointmentStart": "11:00", "appointmentDuration": 20, "phone_number": "+48 793 610 034", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "111396", "patientFirstName": "Katarzyna", "patientLastName": "Czubat", "appointmentStart": "11:20", "appointmentDuration": 20, "phone_number": "+48 669 362 448", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "112409", "patientFirstName": "Natalia", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "12:00", "appointmentDuration": 20, "phone_number": "+48 531 146 086", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "110644", "patientFirstName": "<PERSON>", "patientLastName": "Sojat-Dziadosz", "appointmentStart": "12:20", "appointmentDuration": 20, "phone_number": "+48 503 178 466", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "111399", "patientFirstName": "Katarzyna", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "14:20", "appointmentDuration": 20, "phone_number": "+48 607 379 305", "status": "current", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "111718", "patientFirstName": "Magdalena", "patientLastName": "Zawirska", "appointmentStart": "11:40", "appointmentDuration": 20, "phone_number": "+48 691 693 740", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "111109", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "14:00", "appointmentDuration": 20, "phone_number": "+48 608 374 390", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}]}, {"doctorId": "43", "doctorName": "genetyk dr n.med. <PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "112252", "patientFirstName": "IWONA", "patientLastName": "MINIACH", "appointmentStart": "16:20", "appointmentDuration": 20, "phone_number": "+48 695 555 001", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112117", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Cuker", "appointmentStart": "16:40", "appointmentDuration": 20, "phone_number": "+48 665 939 969", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "111853", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "17:00", "appointmentDuration": 20, "phone_number": "+48 606 456 811", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112118", "patientFirstName": "Katarzyna", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "17:20", "appointmentDuration": 20, "phone_number": "+48 663 667 126", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}]}, {"doctorId": "105", "doctorName": "ginekolog dr <PERSON><PERSON>", "appointments": []}, {"doctorId": "114", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "129", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "72", "doctorName": "ginekolog dr <PERSON>-<PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "10", "doctorName": "ginekolog dr n.med. Małgorzata Ole<PERSON>k-And<PERSON>sz<PERSON>ak", "appointments": [{"appointmentId": "112500", "patientFirstName": "<PERSON>", "patientLastName": "Cisek-Lachowicz", "appointmentStart": "10:40", "appointmentDuration": 20, "phone_number": "+48 606 948 659", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "110430", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Surma", "appointmentStart": "11:00", "appointmentDuration": 20, "phone_number": "+48 531 594 026", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "110485", "patientFirstName": "<PERSON>", "patientLastName": "Bogus<PERSON><PERSON>", "appointmentStart": "11:20", "appointmentDuration": 20, "phone_number": "+48 691 548 157", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "110504", "patientFirstName": "Paul<PERSON>", "patientLastName": "Dobrzańska", "appointmentStart": "11:40", "appointmentDuration": 20, "phone_number": "+48 661 052 240", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "112433", "patientFirstName": "Magdalena", "patientLastName": "Stryjewska", "appointmentStart": "12:00", "appointmentDuration": 20, "phone_number": "+48 735 919 818", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "110557", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "12:20", "appointmentDuration": 20, "phone_number": "+48 604 970 037", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "110641", "patientFirstName": "<PERSON>", "patientLastName": "Sojat-Dziadosz", "appointmentStart": "12:40", "appointmentDuration": 20, "phone_number": "+48 503 178 466", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "112359", "patientFirstName": "Wiktoria", "patientLastName": "Pełka", "appointmentStart": "13:00", "appointmentDuration": 20, "phone_number": "+48 789 466 647", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "111707", "patientFirstName": "<PERSON>", "patientLastName": "Szynczewska", "appointmentStart": "13:20", "appointmentDuration": 20, "phone_number": "+48 883 100 323", "status": "current", "is_confirmed": 0, "is_patient_present": 1, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "110715", "patientFirstName": "<PERSON>", "patientLastName": "Pająk", "appointmentStart": "13:40", "appointmentDuration": 20, "phone_number": "+48 533 149 777", "status": "waiting", "is_confirmed": 1, "is_patient_present": 1, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "110727", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Śliwińska", "appointmentStart": "14:00", "appointmentDuration": 20, "phone_number": "+48 510 279 611", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "111107", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "14:20", "appointmentDuration": 20, "phone_number": "+48 608 374 390", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "110432", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Dębska-Grzelak", "appointmentStart": "14:40", "appointmentDuration": 20, "phone_number": "+48 571 378 333", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "110950", "patientFirstName": "Katarzyna", "patientLastName": "Starzecka", "appointmentStart": "15:00", "appointmentDuration": 20, "phone_number": "+48 724 105 862", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "109303", "patientFirstName": "<PERSON>", "patientLastName": "Brzozowska", "appointmentStart": "15:20", "appointmentDuration": 20, "phone_number": "+48 577 274 980", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "108900", "patientFirstName": "Dominika", "patientLastName": "Kowcza-Wasilewska", "appointmentStart": "15:40", "appointmentDuration": 20, "phone_number": "+48 796 838 883", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "111431", "patientFirstName": "Iga", "patientLastName": "Głowacka", "appointmentStart": "16:00", "appointmentDuration": 20, "phone_number": "+48 723 133 705", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "111967", "patientFirstName": "<PERSON>", "patientLastName": "Krzypkowska", "appointmentStart": "16:20", "appointmentDuration": 20, "phone_number": "+48 781 651 702", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "111984", "patientFirstName": "<PERSON>", "patientLastName": "Szyca-Śmieszniak", "appointmentStart": "16:40", "appointmentDuration": 20, "phone_number": "+48 530 556 679", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112415", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "17:00", "appointmentDuration": 20, "phone_number": "+48 510 310 722", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112335", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "18:00", "appointmentDuration": 10, "phone_number": "+48 663 920 758", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112373", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "Muszyńska-Pioruńska", "appointmentStart": "18:10", "appointmentDuration": 5, "phone_number": "+48 501 154 321", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112478", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "18:15", "appointmentDuration": 5, "phone_number": "+48 727 681 303", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "111963", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "18:50", "appointmentDuration": 5, "phone_number": "+48 694 362 399", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}]}, {"doctorId": "20", "doctorName": "ginekolog dr <PERSON>", "appointments": [{"appointmentId": "107483", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "Grabczyk-Rozwadowska", "appointmentStart": "09:00", "appointmentDuration": 20, "phone_number": "+48 694 349 458", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "107506", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "09:20", "appointmentDuration": 20, "phone_number": "+48 508 093 880", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "111235", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "09:40", "appointmentDuration": 20, "phone_number": "+48 731 568 776", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "108031", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>g", "appointmentStart": "10:20", "appointmentDuration": 20, "phone_number": "+48 537 863 545", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "108035", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Tyc", "appointmentStart": "10:40", "appointmentDuration": 20, "phone_number": "+48 606 614 864", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "108065", "patientFirstName": "Paul<PERSON>", "patientLastName": "Rozborska", "appointmentStart": "11:00", "appointmentDuration": 20, "phone_number": "+48 790 337 881", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "108113", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Świderska", "appointmentStart": "11:20", "appointmentDuration": 20, "phone_number": "+48 606 363 971", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "108144", "patientFirstName": "Magdalena", "patientLastName": "Strózik-Krzysztofiak", "appointmentStart": "11:40", "appointmentDuration": 20, "phone_number": "+48 604 068 907", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "111568", "patientFirstName": "Paul<PERSON>", "patientLastName": "Mayer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "12:20", "appointmentDuration": 20, "phone_number": "+48 509 178 499", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "112378", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Frontczak", "appointmentStart": "13:00", "appointmentDuration": 20, "phone_number": "+48 668 383 318", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "112470", "patientFirstName": "Yulia", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "13:40", "appointmentDuration": 20, "phone_number": "+48 690 252 225", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "112380", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Mak", "appointmentStart": "14:00", "appointmentDuration": 20, "phone_number": "+48 887 607 273", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "112381", "patientFirstName": "Dominika", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "14:20", "appointmentDuration": 20, "phone_number": "+48 531 111 534", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "112481", "patientFirstName": "Paul<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "15:10", "appointmentDuration": 5, "phone_number": "+48 609 271 810", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "112508", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "15:20", "appointmentDuration": 5, "phone_number": "+48 661 403 543", "status": "current", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "108148", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Zielińska-Daniel", "appointmentStart": "12:00", "appointmentDuration": 20, "phone_number": "+48 531 771 006", "status": "waiting", "is_confirmed": 1, "is_patient_present": 1, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112375", "patientFirstName": "Natalia", "patientLastName": "Wasilewska", "appointmentStart": "12:40", "appointmentDuration": 20, "phone_number": "+48 883 099 193", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112379", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "13:20", "appointmentDuration": 20, "phone_number": "+48 792 194 026", "status": "waiting", "is_confirmed": 1, "is_patient_present": 1, "is_completed": 0, "is_sms_sent": 0}]}, {"doctorId": "83", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON><PERSON>", "appointments": [{"appointmentId": "110439", "patientFirstName": "Magdalena", "patientLastName": "MANIEWSKA", "appointmentStart": "15:00", "appointmentDuration": 20, "phone_number": "+48 785 955 226", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "110441", "patientFirstName": "ROKSANA", "patientLastName": "KASZEWSKA", "appointmentStart": "15:20", "appointmentDuration": 20, "phone_number": "+48 787 097 418", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "110450", "patientFirstName": "Paul<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "15:40", "appointmentDuration": 20, "phone_number": "+48 605 606 165", "status": "closed", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 1, "is_sms_sent": 0}, {"appointmentId": "111477", "patientFirstName": "A<PERSON><PERSON>z<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "18:40", "appointmentDuration": 30, "phone_number": "+48 503 434 471", "status": "current", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112221", "patientFirstName": "<PERSON><PERSON><PERSON>", "patientLastName": "Główka", "appointmentStart": "16:20", "appointmentDuration": 20, "phone_number": "+48 609 877 368", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112263", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "16:40", "appointmentDuration": 20, "phone_number": "+48 507 467 551", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "110460", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "Kobylańska", "appointmentStart": "17:00", "appointmentDuration": 20, "phone_number": "+48 577 114 004", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "111685", "patientFirstName": "Agata", "patientLastName": "Kowalska", "appointmentStart": "17:20", "appointmentDuration": 20, "phone_number": "+48 793 009 592", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "110464", "patientFirstName": "Dominika", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "17:40", "appointmentDuration": 20, "phone_number": "+48 695 278 807", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "110465", "patientFirstName": "<PERSON>", "patientLastName": "<PERSON>", "appointmentStart": "18:00", "appointmentDuration": 20, "phone_number": "+48 604 599 193", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112431", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "18:20", "appointmentDuration": 20, "phone_number": "+48 507 467 551", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112008", "patientFirstName": "<PERSON><PERSON><PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON>", "appointmentStart": "19:10", "appointmentDuration": 20, "phone_number": "+48 536 037 871", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112448", "patientFirstName": "Dominika", "patientLastName": "<PERSON><PERSON>", "appointmentStart": "19:30", "appointmentDuration": 20, "phone_number": "+48 795 570 075", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112548", "patientFirstName": "<PERSON>", "patientLastName": "Łysiak-Zapolska", "appointmentStart": "19:50", "appointmentDuration": 20, "phone_number": "+48 533 024 556", "status": "waiting", "is_confirmed": 1, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112550", "patientFirstName": "<PERSON><PERSON>", "patientLastName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appointmentStart": "20:30", "appointmentDuration": 5, "phone_number": "+48 507 872 420", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}, {"appointmentId": "112549", "patientFirstName": "Emilia", "patientLastName": "Siembab", "appointmentStart": "20:35", "appointmentDuration": 5, "phone_number": "+48 723 062 228", "status": "waiting", "is_confirmed": 0, "is_patient_present": 0, "is_completed": 0, "is_sms_sent": 0}]}, {"doctorId": "19", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "82", "doctorName": "ginekolog dr <PERSON><PERSON><PERSON>", "appointments": []}, {"doctorId": "110", "doctorName": "ginekolog dziecięcy dr Agnieszka Tyszko-Tymińska", "appointments": []}, {"doctorId": "**********", "doctorName": "hepa<PERSON><PERSON>, specjalista chorób zakaźnych dr <PERSON><PERSON>", "appointments": []}]}]}}}