<?php

class ApiRouter {
    private $routes = [];

    public function get($path, $callback) {
        $this->routes['GET'][$path] = $callback;
    }

    public function post($path, $callback) {
        $this->routes['POST'][$path] = $callback;
    }

    public function put($path, $callback) {
        $this->routes['PUT'][$path] = $callback;
    }

    public function delete($path, $callback) {
        $this->routes['DELETE'][$path] = $callback;
    }

    public function run() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $path = rtrim($path, '/') ?: '/';

        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("ApiRouter::run - Metoda: " . $method . ", Ścieżka: " . $path);
            error_log("ApiRouter::run - Dost<PERSON>pne trasy dla " . $method . ": " . implode(', ', array_keys($this->routes[$method] ?? [])));
        }

        // Sprawdź dokładne dopasowanie
        if (isset($this->routes[$method][$path])) {
            $this->executeCallback($this->routes[$method][$path], []);
            return;
        }

        // Sprawdź dopasowanie z parametrami
        foreach ($this->routes[$method] ?? [] as $route => $callback) {
            $params = $this->matchRoute($route, $path);
            if ($params !== false) {
                $this->executeCallback($callback, $params);
                return;
            }
        }

        // Nie znaleziono trasy
        $this->sendError(404, 'Endpoint not found');
    }

    private function matchRoute($route, $path) {
        // Zamień {param} na wyrażenie regularne
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $route);
        $pattern = '#^' . $pattern . '$#';

        if (preg_match($pattern, $path, $matches)) {
            array_shift($matches); // Usuń pełne dopasowanie

            // Pobierz nazwy parametrów
            preg_match_all('/\{([^}]+)\}/', $route, $paramNames);
            $paramNames = $paramNames[1];

            $params = [];
            foreach ($paramNames as $index => $name) {
                $params[$name] = $matches[$index] ?? null;
            }

            return $params;
        }

        return false;
    }

    private function executeCallback($callback, $params) {
        try {
            if (is_array($callback)) {
                $controller = new $callback[0]();
                $action = $callback[1];

                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("ApiRouter::executeCallback - Kontroler: " . $callback[0] . ", Akcja: " . $action);
                }

                // Przekaż parametry jako argumenty metody
                if (empty($params)) {
                    $controller->$action();
                } else {
                    $controller->$action(...array_values($params));
                }
            } else {
                call_user_func($callback, $params);
            }
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ApiRouter::executeCallback - Błąd: " . $e->getMessage());
                $this->sendError(500, 'Internal server error: ' . $e->getMessage());
            } else {
                error_log("API Error: " . $e->getMessage());
                $this->sendError(500, 'Internal server error');
            }
        }
    }

    private function sendError($code, $message) {
        http_response_code($code);
        echo json_encode([
            'error' => true,
            'code' => $code,
            'message' => $message,
            'timestamp' => date('c')
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}
