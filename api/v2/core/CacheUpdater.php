<?php

/**
 * CacheUpdater - Klasa pomocnicza do aktualizacji cache w różnych miejscach aplikacji
 */

require_once __DIR__ . '/EnhancedCacheManager.php';

class CacheUpdater {
    private static $cacheManager = null;

    /**
     * Inicjalizacja cache managera
     */
    private static function init() {
        if (self::$cacheManager === null) {
            self::$cacheManager = new EnhancedCacheManager();
        }
    }

    /**
     * Oznaczenie zmian w bazie danych
     * 
     * @param string $entityType Typ danych (np. appointments, doctors)
     */
    public static function markChanges($entityType) {
        self::init();
        self::$cacheManager->markChanges($entityType);
    }

    /**
     * Oznaczenie zmian po aktualizacji wizyty
     * 
     * @param int $appointmentId ID wizyty
     */
    public static function markAppointmentUpdate($appointmentId) {
        self::markChanges('appointments');
        self::markChanges('queue');
    }

    /**
     * Oznaczenie zmian po aktualizacji lekarza
     * 
     * @param int $doctorId ID lekarza
     */
    public static function markDoctorUpdate($doctorId) {
        self::markChanges('doctors');
    }

    /**
     * Oznaczenie zmian po aktualizacji klienta
     * 
     * @param int $clientId ID klienta
     */
    public static function markClientUpdate($clientId) {
        self::markChanges('clients');
    }

    /**
     * Oznaczenie zmian po aktualizacji gabinetu
     * 
     * @param int $roomId ID gabinetu
     */
    public static function markRoomUpdate($roomId) {
        self::markChanges('rooms');
    }

    /**
     * Oznaczenie zmian po aktualizacji kategorii reklam
     * UWAGA: Tabela categories została przemianowana na ads_categories
     *
     * @param int $categoryId ID kategorii
     */
    public static function markAdsCategoryUpdate($categoryId) {
        self::markChanges('ads_categories');
    }

    /**
     * Oznaczenie zmian po aktualizacji kampanii reklamowych
     * UWAGA: Tabela campaigns została przemianowana na ads
     *
     * @param int $adsId ID kampanii
     */
    public static function markAdsUpdate($adsId) {
        self::markChanges('ads');
    }

    /**
     * Oznaczenie zmian po aktualizacji użytkownika
     * 
     * @param int $userId ID użytkownika
     */
    public static function markUserUpdate($userId) {
        self::markChanges('users');
    }
}
