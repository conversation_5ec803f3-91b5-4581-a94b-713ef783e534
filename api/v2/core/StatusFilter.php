<?php

/**
 * Klasa StatusFilter - filtruje statusy wizyt na podstawie ustawień
 * Usuwa wyłączone statusy z odpowiedzi API dla aplikacji /lekarz
 */
class StatusFilter {
    
    /**
     * Filtruje dane wizyt na podstawie ustawień statusów
     * 
     * @param array $appointments Tablica wizyt do przefiltrowania
     * @param array $statusSettings Ustawienia statusów z cache
     * @return array Przefiltrowane wizyty
     */
    public static function filterAppointments($appointments, $statusSettings = null) {
        // Pobierz ustawienia z globalnej stałej jeśli nie przekazano
        if ($statusSettings === null) {
            $statusSettings = defined('API_STATUS_SETTINGS') ? API_STATUS_SETTINGS : [
                'enable_appointment_confirmation' => true,
                'enable_attendance_confirmation' => true,
                'enable_sms_sending' => true
            ];
        }
        
        // Jeśli to nie jest tablica, zwr<PERSON>ć bez zmian
        if (!is_array($appointments)) {
            return $appointments;
        }
        
        // Filtruj każdą wizytę
        foreach ($appointments as &$appointment) {
            $appointment = self::filterSingleAppointment($appointment, $statusSettings);
        }
        
        return $appointments;
    }
    
    /**
     * Filtruje pojedynczą wizytę
     * 
     * @param array $appointment Dane wizyty
     * @param array $statusSettings Ustawienia statusów
     * @return array Przefiltrowana wizyta
     */
    public static function filterSingleAppointment($appointment, $statusSettings) {
        if (!is_array($appointment)) {
            return $appointment;
        }
        
        // Usuń statusy które są wyłączone w ustawieniach
        if (!$statusSettings['enable_appointment_confirmation']) {
            unset($appointment['is_confirmed']);
            unset($appointment['res_confirmed']); // Alternatywna nazwa z niektórych kontrolerów
            unset($appointment['final_is_confirmed']); // Z logiki visit/locked
        }
        
        if (!$statusSettings['enable_attendance_confirmation']) {
            unset($appointment['is_patient_present']);
            unset($appointment['final_is_patient_present']); // Z logiki visit/locked
        }
        
        if (!$statusSettings['enable_sms_sending']) {
            unset($appointment['is_sms_sent']);
            unset($appointment['final_is_sms_sent']); // Z logiki visit/locked
        }
        
        return $appointment;
    }
    
    /**
     * Pobiera aktualne ustawienia statusów z bazy danych
     * 
     * @return array Ustawienia statusów
     */
    public static function getStatusSettings() {
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("SELECT key, value FROM settings WHERE key IN (?, ?, ?)");
            $stmt->execute(['enable_appointment_confirmation', 'enable_attendance_confirmation', 'enable_sms_sending']);
            $settingsRows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $statusSettings = [
                'enable_appointment_confirmation' => false,
                'enable_attendance_confirmation' => false,
                'enable_sms_sending' => false
            ];
            
            foreach ($settingsRows as $setting) {
                $statusSettings[$setting['key']] = (bool)$setting['value'];
            }
            
            return $statusSettings;
        } catch (Exception $e) {
            error_log("StatusFilter - Błąd pobierania ustawień: " . $e->getMessage());
            
            // Zwróć domyślne ustawienia (wszystko włączone) w przypadku błędu
            return [
                'enable_appointment_confirmation' => true,
                'enable_attendance_confirmation' => true,
                'enable_sms_sending' => true
            ];
        }
    }
    
    /**
     * Loguje informacje o filtrowaniu dla debugowania
     * 
     * @param array $statusSettings Ustawienia statusów
     * @param int $appointmentsCount Liczba przefiltrowanych wizyt
     */
    public static function logFilteringInfo($statusSettings, $appointmentsCount = 0) {
        $disabledStatuses = [];
        
        if (!$statusSettings['enable_appointment_confirmation']) {
            $disabledStatuses[] = 'is_confirmed';
        }
        if (!$statusSettings['enable_attendance_confirmation']) {
            $disabledStatuses[] = 'is_patient_present';
        }
        if (!$statusSettings['enable_sms_sending']) {
            $disabledStatuses[] = 'is_sms_sent';
        }
        
        if (!empty($disabledStatuses)) {
            error_log("StatusFilter - Usunięto wyłączone statusy z {$appointmentsCount} wizyt: " . implode(', ', $disabledStatuses));
        }
    }
}
