<?php

class EnhancedCacheManager {
    private $cacheFile;
    private $cacheDir;

    public function __construct() {
        $this->cacheDir = __DIR__ . '/../cache';
        $this->cacheFile = $this->cacheDir . '/cache_status.json';

        // Utworzenie katalogu cache jeśli nie istnieje
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }

        // Inicjalizacja pliku cache jeśli nie istnieje
        if (!file_exists($this->cacheFile)) {
            $this->initializeCache();
        }

        // Migracja starego formatu do nowego jeśli potrzeba
        $this->migrateIfNeeded();
    }

    /**
     * Inicjalizacja pliku cache w nowym formacie
     */
    private function initializeCache() {
        $currentTime = time();
        $currentTimeStr = date('Y-m-d H:i:s', $currentTime);

        $initialData = [
            'entities' => [
                'appointments' => [
                    'lastUpdate' => $currentTime,
                    'timestamp' => $currentTimeStr
                ],
                'doctors' => [
                    'lastUpdate' => $currentTime,
                    'timestamp' => $currentTimeStr
                ],
                'clients' => [
                    'lastUpdate' => $currentTime,
                    'timestamp' => $currentTimeStr
                ],
                'rooms' => [
                    'lastUpdate' => $currentTime,
                    'timestamp' => $currentTimeStr
                ],
                'queue' => [
                    'lastUpdate' => $currentTime,
                    'timestamp' => $currentTimeStr
                ],
                'ads_categories' => [
                    'lastUpdate' => $currentTime,
                    'timestamp' => $currentTimeStr
                ],
                'ads' => [
                    'lastUpdate' => $currentTime,
                    'timestamp' => $currentTimeStr
                ],
                'users' => [
                    'lastUpdate' => $currentTime,
                    'timestamp' => $currentTimeStr
                ],
                'settings' => [
                    'lastUpdate' => $currentTime,
                    'timestamp' => $currentTimeStr
                ]
            ],
            'version' => 2,
            'globalLastUpdate' => $currentTime,
            'globalTimestamp' => $currentTimeStr
        ];

        file_put_contents($this->cacheFile, json_encode($initialData, JSON_PRETTY_PRINT));
    }

    /**
     * Migracja starego formatu do nowego
     */
    private function migrateIfNeeded() {
        $content = file_get_contents($this->cacheFile);
        $data = json_decode($content, true);

        if (!isset($data['version']) || $data['version'] < 2) {
            error_log("EnhancedCacheManager: Migracja formatu cache z wersji 1 do 2");

            $currentTime = time();
            $currentTimeStr = date('Y-m-d H:i:s', $currentTime);

            $newData = [
                'entities' => [
                    'appointments' => [
                        'lastUpdate' => $currentTime,
                        'timestamp' => $currentTimeStr
                    ],
                    'doctors' => [
                        'lastUpdate' => $currentTime,
                        'timestamp' => $currentTimeStr
                    ],
                    'clients' => [
                        'lastUpdate' => $currentTime,
                        'timestamp' => $currentTimeStr
                    ],
                    'rooms' => [
                        'lastUpdate' => $currentTime,
                        'timestamp' => $currentTimeStr
                    ],
                    'queue' => [
                        'lastUpdate' => $currentTime,
                        'timestamp' => $currentTimeStr
                    ],
                    'ads_categories' => [
                        'lastUpdate' => $currentTime,
                        'timestamp' => $currentTimeStr
                    ],
                    'ads' => [
                        'lastUpdate' => $currentTime,
                        'timestamp' => $currentTimeStr
                    ],
                    'users' => [
                        'lastUpdate' => $currentTime,
                        'timestamp' => $currentTimeStr
                    ]
                ],
                'version' => 2,
                'globalLastUpdate' => isset($data['lastUpdate']) ? $data['lastUpdate'] : $currentTime,
                'globalTimestamp' => isset($data['timestamp']) ? $data['timestamp'] : $currentTimeStr
            ];

            $this->writeCache($newData);
        }
    }

    /**
     * Sprawdzenie zmian dla klienta z uwzględnieniem timestampów
     *
     * @param string $id ID (lekarza, wyświetlacza, etc.)
     * @param array $lastSyncTimestamps Timestampy ostatniej synchronizacji dla poszczególnych typów danych
     * @return array Informacje o zmianach
     */
    public function checkChanges($id, $lastSyncTimestamps = []) {
        try {
            $cacheData = $this->readCache();
            $changes = [];
            $hasChanges = false;
            $currentTime = time();

            // Optymalizacja: sprawdź tylko kluczowe typy danych dla wyświetlaczy
            $criticalEntityTypes = ['appointments', 'queue', 'doctors'];
            
            foreach ($cacheData['entities'] as $entityType => $entityData) {
                // Pomiń nieistotne typy danych dla wyświetlaczy
                if (!in_array($entityType, $criticalEntityTypes)) {
                    continue;
                }
                
                $lastClientSync = isset($lastSyncTimestamps[$entityType]) ? $lastSyncTimestamps[$entityType] : 0;
                $hasEntityChanges = $entityData['lastUpdate'] > $lastClientSync;

                $changes[$entityType] = [
                    'hasChanges' => $hasEntityChanges,
                    'lastUpdate' => $entityData['lastUpdate'],
                    'timestamp' => $entityData['timestamp']
                ];

                if ($hasEntityChanges) {
                    $hasChanges = true;
                }
            }

            return [
                'hasChanges' => $hasChanges,
                'changes' => $changes,
                'globalLastUpdate' => $cacheData['globalLastUpdate'],
                'globalTimestamp' => $cacheData['globalTimestamp'],
                'checkTime' => $currentTime
            ];
        } catch (Exception $e) {
            error_log("EnhancedCacheManager checkChanges error: " . $e->getMessage());
            return [
                'hasChanges' => false,
                'changes' => [],
                'globalLastUpdate' => time(),
                'globalTimestamp' => date('Y-m-d H:i:s'),
                'checkTime' => time()
            ];
        }
    }

    /**
     * Oznaczenie zmian w cache dla konkretnego typu danych
     * 
     * @param string $entityType Typ danych (np. appointments, doctors)
     */
    public function markChanges($entityType) {
        try {
            $cacheData = $this->readCache();

            if (isset($cacheData['entities'][$entityType])) {
                $currentTime = time();
                $currentTimeStr = date('Y-m-d H:i:s', $currentTime);

                $cacheData['entities'][$entityType]['lastUpdate'] = $currentTime;
                $cacheData['entities'][$entityType]['timestamp'] = $currentTimeStr;

                // Aktualizacja globalnego timestampa
                $cacheData['globalLastUpdate'] = $currentTime;
                $cacheData['globalTimestamp'] = $currentTimeStr;

                $this->writeCache($cacheData);

                // Logowanie tylko dla kluczowych typów danych
                if (in_array($entityType, ['appointments', 'queue', 'doctors', 'settings'])) {
                    error_log("EnhancedCacheManager: Oznaczono zmiany dla {$entityType} w czasie {$currentTimeStr}");
                }
            }
        } catch (Exception $e) {
            error_log("EnhancedCacheManager markChanges error: " . $e->getMessage());
        }
    }

    /**
     * Oznaczenie zmian w cache dla konkretnego typu danych z podanym timestampem
     *
     * @param string $entityType Typ danych (np. appointments, doctors)
     * @param int $timestamp Timestamp do ustawienia
     */
    public function markChangesWithTimestamp($entityType, $timestamp) {
        try {
            $cacheData = $this->readCache();

            if (isset($cacheData['entities'][$entityType])) {
                $timestampStr = date('Y-m-d H:i:s', $timestamp);

                $cacheData['entities'][$entityType]['lastUpdate'] = $timestamp;
                $cacheData['entities'][$entityType]['timestamp'] = $timestampStr;

                // Aktualizacja globalnego timestampa
                $cacheData['globalLastUpdate'] = $timestamp;
                $cacheData['globalTimestamp'] = $timestampStr;

                $this->writeCache($cacheData);

                // Logowanie tylko dla kluczowych typów danych
                if (in_array($entityType, ['appointments', 'queue', 'doctors', 'settings'])) {
                    error_log("EnhancedCacheManager: Oznaczono zmiany dla {$entityType} z timestampem {$timestampStr}");
                }
            }
        } catch (Exception $e) {
            error_log("EnhancedCacheManager markChangesWithTimestamp error: " . $e->getMessage());
        }
    }

    /**
     * Pobranie timestampów zmian dla wszystkich typów danych
     *
     * @return array Timestampy zmian
     */
    public function getChangeTimestamps() {
        try {
            $cacheData = $this->readCache();
            $timestamps = [];

            foreach ($cacheData['entities'] as $entityType => $entityData) {
                $timestamps[$entityType] = [
                    'lastUpdate' => $entityData['lastUpdate'],
                    'timestamp' => $entityData['timestamp']
                ];
            }

            return [
                'entities' => $timestamps,
                'globalLastUpdate' => $cacheData['globalLastUpdate'],
                'globalTimestamp' => $cacheData['globalTimestamp']
            ];
        } catch (Exception $e) {
            error_log("EnhancedCacheManager getChangeTimestamps error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Odczyt danych z cache
     */
    private function readCache() {
        if (!file_exists($this->cacheFile)) {
            $this->initializeCache();
        }

        $content = file_get_contents($this->cacheFile);
        $data = json_decode($content, true);

        if (!$data) {
            $this->initializeCache();
            $content = file_get_contents($this->cacheFile);
            $data = json_decode($content, true);
        }

        return $data;
    }

    /**
     * Zapis danych do cache
     */
    private function writeCache($data) {
        $json = json_encode($data, JSON_PRETTY_PRINT);
        file_put_contents($this->cacheFile, $json);
    }

    /**
     * Pobranie statystyk cache
     */
    public function getCacheStats() {
        try {
            $cacheData = $this->readCache();
            $fileSize = file_exists($this->cacheFile) ? filesize($this->cacheFile) : 0;

            return [
                'file_size' => $fileSize,
                'file_path' => $this->cacheFile,
                'version' => $cacheData['version'],
                'globalLastUpdate' => $cacheData['globalLastUpdate'],
                'globalTimestamp' => $cacheData['globalTimestamp'],
                'entities' => $cacheData['entities']
            ];
        } catch (Exception $e) {
            error_log("EnhancedCacheManager getCacheStats error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Oznaczenie zmian dla konkretnego klienta (kompatybilność z CacheUpdater)
     */
    public function markClientChanges($clientId, $entityType, $value = true) {
        // W nowym systemie nie rozróżniamy klientów - wszystkie zmiany są globalne
        $this->markChanges($entityType);
    }

    /**
     * Pobranie flag zmian (kompatybilność z CacheUpdater)
     */
    public function getChangeFlags($clientId = null) {
        return $this->getChangeTimestamps();
    }

    /**
     * Reset flag zmian (kompatybilność z CacheUpdater)
     */
    public function resetChanges($changeTypes = null) {
        try {
            $cacheData = $this->readCache();
            $currentTime = time();
            $currentTimeStr = date('Y-m-d H:i:s', $currentTime);

            if ($changeTypes === null) {
                // Reset wszystkich typów
                foreach ($cacheData['entities'] as $entityType => &$entityData) {
                    $entityData['lastUpdate'] = $currentTime;
                    $entityData['timestamp'] = $currentTimeStr;
                }
            } elseif (is_array($changeTypes)) {
                // Reset konkretnych typów
                foreach ($changeTypes as $entityType) {
                    if (isset($cacheData['entities'][$entityType])) {
                        $cacheData['entities'][$entityType]['lastUpdate'] = $currentTime;
                        $cacheData['entities'][$entityType]['timestamp'] = $currentTimeStr;
                    }
                }
            } else {
                // Reset konkretnego typu
                if (isset($cacheData['entities'][$changeTypes])) {
                    $cacheData['entities'][$changeTypes]['lastUpdate'] = $currentTime;
                    $cacheData['entities'][$changeTypes]['timestamp'] = $currentTimeStr;
                }
            }

            $cacheData['globalLastUpdate'] = $currentTime;
            $cacheData['globalTimestamp'] = $currentTimeStr;

            $this->writeCache($cacheData);
        } catch (Exception $e) {
            error_log("EnhancedCacheManager resetChanges error: " . $e->getMessage());
        }
    }

    /**
     * Pobierz timestamp ostatniej zmiany dla danego typu encji
     * Uproszzczona wersja dla aplikacji PWA
     *
     * @param string $entityType Typ encji (appointments, doctors, etc.)
     * @return int Timestamp ostatniej zmiany
     */
    public function getEntityTimestamp($entityType) {
        try {
            $cacheData = $this->readCache();
            
            // Sprawdź czy typ encji istnieje w cache
            if (isset($cacheData['entities'][$entityType])) {
                return $cacheData['entities'][$entityType]['lastUpdate'];
            }
            
            // Jeśli typ encji nie istnieje, zwróć aktualny czas
            $currentTime = time();
            $this->markChanges($entityType); // Utwórz wpis w cache
            return $currentTime;
        } catch (Exception $e) {
            error_log("EnhancedCacheManager getEntityTimestamp error: " . $e->getMessage());
            return time(); // Zwróć aktualny czas w przypadku błędu
        }
    }
}
