# Filtrowanie statusów wizyt w API v2

## Opis funkcjonalności

API v2 zostało rozszerzone o funkcjonalność filtrowania statusów wizyt na podstawie ustawień w tabeli `settings`. Przed przekazaniem danych o wizytach do aplikacji `/lekarz`, API sprawdza które statusy są włączone w ustawieniach i usuwa wyłączone statusy z odpowiedzi.

## Ustawienia kontrolujące filtrowanie

W tabeli `settings` znajdują się następujące klucze kontrolujące filtrowanie:

| Klucz | Opis | Kontroluje pole |
|-------|------|-----------------|
| `enable_appointment_confirmation` | Włącza/wyłącza status potwierdzenia wizyty | `is_confirmed`, `res_confirmed`, `final_is_confirmed` |
| `enable_attendance_confirmation` | Włącza/wyłącza status obecności pacjenta | `is_patient_present`, `final_is_patient_present` |
| `enable_sms_sending` | Włącza/wyłącza status wysyłki SMS | `is_sms_sent`, `final_is_sms_sent` |

**Wartości:**
- `1` = status włączony (pole jest zwracane w API)
- `0` = status wyłączony (pole jest usuwane z odpowiedzi API)

## Implementacja

### 1. Buforowanie ustawień

W pliku `api/v2/index.php` na początku wykonywania API:

```php
// Pobierz i zbuforuj ustawienia statusów na początku wykonywania API
$db = Database::getInstance();
$stmt = $db->prepare("SELECT key, value FROM settings WHERE key IN (?, ?, ?)");
$stmt->execute(['enable_appointment_confirmation', 'enable_attendance_confirmation', 'enable_sms_sending']);
$settingsRows = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Przygotuj cache ustawień statusów
$statusSettings = [
    'enable_appointment_confirmation' => false,
    'enable_attendance_confirmation' => false,
    'enable_sms_sending' => false
];

foreach ($settingsRows as $setting) {
    $statusSettings[$setting['key']] = (bool)$setting['value'];
}

// Udostępnij ustawienia globalnie dla kontrolerów API
define('API_STATUS_SETTINGS', $statusSettings);
```

### 2. Klasa StatusFilter

Utworzono klasę `api/v2/core/StatusFilter.php` odpowiedzialną za filtrowanie:

```php
class StatusFilter {
    /**
     * Filtruje dane wizyt na podstawie ustawień statusów
     */
    public static function filterAppointments($appointments, $statusSettings = null)
    
    /**
     * Filtruje pojedynczą wizytę
     */
    public static function filterSingleAppointment($appointment, $statusSettings)
    
    /**
     * Pobiera aktualne ustawienia statusów z bazy danych
     */
    public static function getStatusSettings()
    
    /**
     * Loguje informacje o filtrowaniu dla debugowania
     */
    public static function logFilteringInfo($statusSettings, $appointmentsCount = 0)
}
```

### 3. Zmodyfikowane kontrolery

Następujące kontrolery zostały zmodyfikowane aby używać filtrowania:

- **DoctorApiController** - wszystkie metody zwracające dane wizyt
- **V2ApiController** - metoda `getAppointments()`
- **DisplayApiController** - metoda `getQueueData()`
- **IgabinetImportController** - dodano import klasy (przygotowanie)
- **ImportController** - dodano import klasy (przygotowanie)

## Przykłady użycia

### Wszystkie statusy wyłączone
```sql
UPDATE settings SET value = '0' WHERE key IN ('enable_appointment_confirmation', 'enable_attendance_confirmation', 'enable_sms_sending');
```

Odpowiedź API:
```json
{
  "id": 796,
  "patient_name": "Maria Kowalczyk",
  "status": "waiting",
  "phone_number": "**********"
  // Brak pól: is_confirmed, is_patient_present, is_sms_sent
}
```

### Tylko potwierdzenie wizyt włączone
```sql
UPDATE settings SET value = '1' WHERE key = 'enable_appointment_confirmation';
UPDATE settings SET value = '0' WHERE key IN ('enable_attendance_confirmation', 'enable_sms_sending');
```

Odpowiedź API:
```json
{
  "id": 796,
  "patient_name": "Maria Kowalczyk",
  "status": "waiting",
  "phone_number": "**********",
  "is_confirmed": 0
  // Brak pól: is_patient_present, is_sms_sent
}
```

### Wszystkie statusy włączone
```sql
UPDATE settings SET value = '1' WHERE key IN ('enable_appointment_confirmation', 'enable_attendance_confirmation', 'enable_sms_sending');
```

Odpowiedź API:
```json
{
  "id": 796,
  "patient_name": "Maria Kowalczyk",
  "status": "waiting",
  "phone_number": "**********",
  "is_confirmed": 0,
  "is_patient_present": 0,
  "is_sms_sent": 0
}
```

## Logowanie

System loguje informacje o filtrowaniu:

```
StatusFilter - Usunięto wyłączone statusy z 20 wizyt: is_confirmed, is_patient_present
```

## Endpointy objęte filtrowaniem

- `GET /api/v2/doctor/appointments` - wizyty lekarza
- `GET /api/v2/appointments/{doctorId}` - wizyty dla PWA
- `GET /api/v2/display/{code}` - dane dla wyświetlaczy
- `POST /api/v2/doctor/call-next` - wywołanie następnej wizyty
- `POST /api/v2/doctor/call-specific` - wywołanie konkretnej wizyty
- `POST /api/v2/doctor/close-appointment` - zamknięcie wizyty
- Wszystkie inne endpointy zwracające dane wizyt

## Kompatybilność

Filtrowanie jest w pełni kompatybilne wstecz:
- Gdy wszystkie statusy są włączone, API zwraca pełne dane jak wcześniej
- Aplikacje klienckie powinny sprawdzać istnienie pól przed ich użyciem
- Brak pola oznacza że dana funkcjonalność jest wyłączona w ustawieniach

## Wydajność

- Ustawienia są pobierane i buforowane tylko raz na początku wykonywania API
- Filtrowanie odbywa się w pamięci bez dodatkowych zapytań do bazy
- Minimalne obciążenie wydajnościowe
