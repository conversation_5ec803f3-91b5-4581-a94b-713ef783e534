<?php

/**
 * API V2 - Główny punkt wejścia
 * Wszystkie endpointy API v2 są obsługiwane przez ten plik
 */

// Ustawienia PHP
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Nagłówki CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Content-Type: application/json; charset=utf-8');

// Obsługa OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Załaduj konfigurację
if (!defined('KTOOSTATNI_APP')) {
    define('KTOOSTATNI_APP', true);
}
require_once __DIR__ . '/../../config.php';

// Autoloader dla klas API v2
spl_autoload_register(function ($class) {
    $directories = [
        __DIR__ . '/core/',
        __DIR__ . '/controllers/',
        __DIR__ . '/../core/', // Fallback do starego core
        __DIR__ . '/../../admin/core/', // Fallback do admin core
        __DIR__ . '/../../admin/models/' // Fallback do admin models
    ];

    foreach ($directories as $directory) {
        $file = $directory . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

try {
    // Inicjalizacja bazy danych - UWAGA: Metoda init() nie istnieje
    // Database jest inicjalizowana automatycznie przez getInstance()

    // Pobierz i zbuforuj ustawienia statusów na początku wykonywania API
    $db = Database::getInstance();
    $stmt = $db->prepare("SELECT key, value FROM settings WHERE key IN (?, ?, ?)");
    $stmt->execute(['enable_appointment_confirmation', 'enable_attendance_confirmation', 'enable_sms_sending']);
    $settingsRows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Przygotuj cache ustawień statusów
    $statusSettings = [
        'enable_appointment_confirmation' => false,
        'enable_attendance_confirmation' => false,
        'enable_sms_sending' => false
    ];

    foreach ($settingsRows as $setting) {
        $statusSettings[$setting['key']] = (bool)$setting['value'];
    }

    // Udostępnij ustawienia globalnie dla kontrolerów API
    define('API_STATUS_SETTINGS', $statusSettings);

    // Usuń prefix /api/v2 z ścieżki jeśli istnieje (dla PHP built-in server)
    $requestUri = $_SERVER['REQUEST_URI'];
    $requestPath = parse_url($requestUri, PHP_URL_PATH);

    if (strpos($requestPath, '/api/v2') === 0) {
        // Usuń prefix /api/v2
        $cleanPath = substr($requestPath, 7); // Usuń '/api/v2'
        $cleanPath = $cleanPath ?: '/';

        // Zachowaj query string jeśli istnieje
        $queryString = parse_url($requestUri, PHP_URL_QUERY);
        $newUri = $cleanPath . ($queryString ? '?' . $queryString : '');

        $_SERVER['REQUEST_URI'] = $newUri;
    }

    // Debug routing
    if (isset($_GET['debug'])) {
        error_log("V2 API - REQUEST_URI: " . $_SERVER['REQUEST_URI']);
        error_log("V2 API - REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
    }

    // Inicjalizacja routera
    $router = new ApiRouter();

    // === ENDPOINTY V2 API ===

    // Test endpoint
    $router->get('/test', function () {
        echo json_encode(['success' => true, 'message' => 'V2 API działa!']);
    });

    // Import danych z systemu iGabinet
    $router->post('/import/igabinet', [IgabinetImportController::class, 'importData']);

    // Domyślny import danych (drEryk i inne systemy)
    $router->post('/import', [ImportController::class, 'importData']);



    // Wizyty dla PWA
    $router->get('/appointments/{doctorId}', [V2ApiController::class, 'getAppointments']);
    $router->post('/appointments/{appointmentId}/update', [V2ApiController::class, 'updateAppointment']);
    $router->post('/appointments/{appointmentId}/call', [V2ApiController::class, 'callAppointment']);

    // Kolejka dla wyświetlaczy
    $router->get('/queue/{displayId}', [V2ApiController::class, 'getQueueStatus']);

    // Sprawdzanie zmian - uproszczone dla PWA
    $router->get('/changes/{entityType}', [V2ApiController::class, 'checkChanges']);

    // Ustawienia wyświetlacza
    $router->get('/settings/display/{displayId}', [V2ApiController::class, 'getDisplaySettings']);

    // === ENDPOINTY CACHE ===

    // Pobranie wszystkich timestampów
    $router->get('/cache/timestamps', [CacheController::class, 'getTimestamps']);

    // Oznaczanie zmian
    $router->post('/cache/mark/{entityType}', [CacheController::class, 'markChanges']);

    // Statystyki cache
    $router->get('/cache/stats', [CacheController::class, 'getStats']);

    // === ENDPOINTY LEKARZA (PWA) ===

    // Logowanie lekarza
    $router->post('/doctor/login', [DoctorApiController::class, 'login']);

    // Wizyty lekarza (bez roomId)
    $router->get('/doctor/appointments', [DoctorApiController::class, 'doctorAppointments']);

    // Operacje na wizytach (bez roomId)
    $router->post('/doctor/call-next', [DoctorApiController::class, 'doctorCallNext']);
    $router->post('/doctor/previous', [DoctorApiController::class, 'doctorPrevious']);
    $router->post('/doctor/skip-current', [DoctorApiController::class, 'doctorSkipCurrent']);
    $router->post('/doctor/call-specific', [DoctorApiController::class, 'doctorCallSpecific']);
    $router->post('/doctor/close-appointment', [DoctorApiController::class, 'doctorCloseAppointment']);
    $router->post('/doctor/toggle-patient-presence', [DoctorApiController::class, 'togglePatientPresence']);

    // Nawigacja między wizytami została usunięta - aplikacja używa call-specific

    // Sprawdzanie dostępności gabinetu
    $router->post('/doctor/check-room-availability', [DoctorApiController::class, 'checkRoomAvailability']);

    // Statystyki (bez roomId)
    $router->get('/doctor/stats', [DoctorApiController::class, 'doctorStats']);


    // === ENDPOINTY WYŚWIETLACZY ===

    // Dane dla wyświetlaczy
    $router->get('/display/{code}', [DisplayApiController::class, 'getDisplayData']);
    $router->post('/display/{code}/heartbeat', [DisplayApiController::class, 'heartbeat']);
    $router->post('/display/{code}/video-view', [DisplayApiController::class, 'recordVideoView']);

    // === ENDPOINTY REKLAM ===

    // Dane reklam z algorytmem sprawiedliwego losowania
    $router->get('/ads/{clientId}', [AdsApiController::class, 'getAds']);
    $router->post('/ads/view', [AdsApiController::class, 'recordView']);

    // === ENDPOINTY MAPOWANIA LEKARZY ===
    // Mapowanie lekarzy odbywa się w backend podczas importu

    // === OBSŁUGA ROUTINGU ===

    $router->run();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Błąd serwera: ' . $e->getMessage(),
        'timestamp' => date('c')
    ]);
}
