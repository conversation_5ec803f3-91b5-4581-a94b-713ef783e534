<?php
/**
 * Router API v2
 * Obsługuje routing dla konkretnej wersji API
 */

// Pobierz ś<PERSON>żkę żądania (już przetworzoną przez główny router)
// Główny router już usunął prefix /api i przekazał ścieżkę zaczynającą się od /v2
$requestUri = $_SERVER['REQUEST_URI'];
$requestPath = parse_url($requestUri, PHP_URL_PATH);

// Debug
error_log("API v2 Router - Request Path: " . $requestPath);

// Sprawdź czy ścieżka zaczyna się od /v2 (bez /api, bo już zostało usunięte)
if (strpos($requestPath, '/v2') !== 0) {
    http_response_code(404);
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Invalid API v2 path']);
    exit();
}

// Usuń prefix /v2 z ścieżki dla routera
$cleanPath = substr($requestPath, 3); // Usuń '/v2'
$cleanPath = $cleanPath ?: '/';

// Ustaw oczyszczoną ścieżkę w $_SERVER dla routera
$_SERVER['REQUEST_URI'] = $cleanPath;

// Debug
error_log("API v2 Router - Clean Path: " . $cleanPath);

// Przekieruj na główny index.php API v2
require_once __DIR__ . '/index.php';
