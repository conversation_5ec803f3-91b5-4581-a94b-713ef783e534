<?php
require_once __DIR__ . '/../core/ApiController.php';
require_once __DIR__ . '/../core/EnhancedCacheManager.php';

class CacheController extends ApiController {
    private $cacheManager;
    
    public function __construct() {
        parent::__construct();
        $this->cacheManager = new EnhancedCacheManager();
    }
    
    
    /**
     * Pobranie wszystkich timestampów zmian
     * 
     * @return array Odpowiedź API
     */
    public function getTimestamps() {
        try {
            $timestamps = $this->cacheManager->getChangeTimestamps();
            
            return $this->success([
                'timestamps' => $timestamps,
                'serverTime' => time(),
                'serverTimeFormatted' => date('Y-m-d H:i:s')
            ]);
            
        } catch (Exception $e) {
            error_log("CacheController getTimestamps error: " . $e->getMessage());
            return $this->error('Błąd podczas pobierania timestampów: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Oznaczenie zmian dla konkretnego typu danych
     * 
     * @param string $entityType Typ danych (np. appointments, doctors)
     * @return array Odpowiedź API
     */
    public function markChanges($entityType) {
        try {
            if (!$entityType) {
                return $this->error('Brak typu danych', 400);
            }
            
            $this->cacheManager->markChanges($entityType);
            
            return $this->success([
                'entityType' => $entityType,
                'marked' => true,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            
        } catch (Exception $e) {
            error_log("CacheController markChanges error: " . $e->getMessage());
            return $this->error('Błąd podczas oznaczania zmian: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Pobranie statystyk cache
     * 
     * @return array Odpowiedź API
     */
    public function getStats() {
        try {
            $stats = $this->cacheManager->getCacheStats();
            
            return $this->success([
                'stats' => $stats,
                'serverTime' => time(),
                'serverTimeFormatted' => date('Y-m-d H:i:s')
            ]);
            
        } catch (Exception $e) {
            error_log("CacheController getStats error: " . $e->getMessage());
            return $this->error('Błąd podczas pobierania statystyk cache: ' . $e->getMessage(), 500);
        }
    }
}
