<?php
require_once __DIR__ . '/../core/ApiController.php';
require_once __DIR__ . '/../core/EnhancedCacheManager.php';
require_once __DIR__ . '/../core/CacheUpdater.php';
require_once __DIR__ . '/../core/StatusFilter.php';

/**
 * Domyślny kontroler do importu danych
 */
class ImportController extends ApiController {
    private $cacheManager;
    private $importSettings;

    public function __construct() {
        parent::__construct();
        $this->cacheManager = new EnhancedCacheManager();
        $this->importSettings = new ImportSettings();
    }

    /**
     * Import danych (domyślny endpoint)
     */
    public function importData() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);

            // Logowanie przychodzących danych
            error_log('[Import] Import request received. Raw data: ' . json_encode($input));
            error_log('[Import] Import request keys: ' . implode(', ', array_keys($input ?? [])));

            if (!$input) {
                return $this->error('Brak danych do importu', 400);
            }

            // Sprawdź czy import jest włączony
            if (!$this->importSettings->isImportEnabled()) {
                return $this->error('Import jest wyłączony w ustawieniach systemu', 403);
            }

            $source = $input['source'] ?? 'default';
            $syncCode = $input['syncCode'] ?? null;

            error_log('[Import] Source: ' . $source . ', syncCode: ' . $syncCode);

            // Sprawdź czy dane są w poprawnym formacie (syncData)
            if (!isset($input['syncData']) || !isset($input['syncData']['days'])) {
                error_log('[Import] Invalid format - expected syncData.days structure');
                return $this->error('Nieprawidłowy format danych - oczekiwano struktury syncData.days', 400);
            }

            error_log('[Import] Processing structured import data');
            return $this->importStructuredData($input, $source, $syncCode);
        } catch (Exception $e) {
            error_log('[Import] Error: ' . $e->getMessage());
            return $this->error('Błąd podczas importu: ' . $e->getMessage(), 500);
        }
    }



    /**
     * Import wizyt z nowym strukturalnym formatem danych
     */
    private function importStructuredData($input, $source, $syncCode) {
        $syncData = $input['syncData'];
        $days = $syncData['days'] ?? [];

        if (empty($days)) {
            return $this->error('Brak dni do importu', 400);
        }

        // Krok 1: Przekształć strukturalne dane na płaską listę wizyt
        $appointments = [];
        foreach ($days as $day) {
            $date = $day['date'] ?? date('Y-m-d');
            $doctors = $day['doctors'] ?? [];

            foreach ($doctors as $doctor) {
                $doctorId = $doctor['doctorId'] ?? null;
                $doctorName = $doctor['doctorName'] ?? 'Nieznany lekarz';
                $doctorAppointments = $doctor['appointments'] ?? [];

                foreach ($doctorAppointments as $appointment) {
                    // Generuj MD5 hash jeśli appointmentId jest pusty
                    $appointmentId = $appointment['appointmentId'] ?? null;
                    if (empty($appointmentId)) {
                        $patientName = trim(($appointment['patientFirstName'] ?? '') . ' ' . ($appointment['patientLastName'] ?? ''));
                        $hashData = $patientName . '_' . $date . '_' . $doctorName;
                        $appointmentId = md5($hashData);
                    }

                    $appointmentData = [
                        'doctor_id' => $doctorId,
                        'doctor_name' => $doctorName,
                        'patient_name' => trim(($appointment['patientFirstName'] ?? '') . ' ' . ($appointment['patientLastName'] ?? '')),
                        'appointment_time' => $appointment['appointmentStart'] ?? null,
                        'appointment_date' => $date,
                        'appointment_duration' => $appointment['appointmentDuration'] ?? 20,
                        'phone_number' => $appointment['phone_number'] ?? null,
                        'external_id' => $appointmentId
                    ];

                    // Dodaj pola statusu tylko jeśli są przekazane
                    if (isset($appointment['status'])) {
                        $appointmentData['status'] = $appointment['status'];
                    }
                    if (isset($appointment['is_confirmed'])) {
                        $appointmentData['is_confirmed'] = $appointment['is_confirmed'];
                    }
                    if (isset($appointment['is_patient_present'])) {
                        $appointmentData['is_patient_present'] = $appointment['is_patient_present'];
                    }
                    if (isset($appointment['is_completed'])) {
                        $appointmentData['is_completed'] = $appointment['is_completed'];
                    }
                    if (isset($appointment['is_sms_sent'])) {
                        $appointmentData['is_sms_sent'] = $appointment['is_sms_sent'];
                    }

                    $appointments[] = $appointmentData;
                }
            }
        }

        if (empty($appointments)) {
            return $this->error('Brak wizyt do importu', 400);
        }

        // Krok 2: Pobierz wszystkie mapowania lekarzy do bufora (optymalizacja)
        $doctorMappings = $this->loadDoctorMappingsToBuffer();
        error_log("[Import] Załadowano " . count($doctorMappings) . " mapowań lekarzy do bufora");

        // Krok 3: Zbierz unikalnych lekarzy z importu i sprawdź mapowania
        $externalDoctors = [];
        $unmappedDoctors = [];

        foreach ($appointments as $appointment) {
            $doctorId = $appointment['doctor_id'] ?? null;
            $doctorName = $appointment['doctor_name'] ?? 'Nieznany lekarz';

            if ($doctorId && !isset($externalDoctors[$doctorId])) {
                $externalDoctors[$doctorId] = [
                    'external_id' => $doctorId,
                    'name' => $doctorName
                ];

                // Sprawdź czy lekarz jest zmapowany (dokładne dopasowanie)
                if (!isset($doctorMappings[$doctorId])) {
                    $unmappedDoctors[] = [
                        'external_id' => $doctorId,
                        'name' => $doctorName
                    ];
                }
            }
        }

        // Krok 4: Jeśli są niezmapowani lekarze, przerwij import
        if (!empty($unmappedDoctors)) {
            // Utwórz plik z niezmapowanymi lekarzami
            $filename = $this->importSettings->createUnmappedDoctorsFile(
                $unmappedDoctors,
                $syncCode,
                $source
            );

            // Zapisz dane do pliku log
            $this->saveSyncLog($input, $syncCode);

            error_log("[Import] Import odrzucony - " . count($unmappedDoctors) . " niezmapowanych lekarzy");

            return $this->error('Nie można przeprowadzić importu - znaleziono niezmapowanych lekarzy', 400, [
                'unmapped_count' => count($unmappedDoctors),
                'unmapped_doctors_file' => $filename,
                'unmapped_doctors' => $unmappedDoctors
            ]);
        }

        // Krok 5: Zapisz dane do pliku log
        $this->saveSyncLog($input, $syncCode);

        $errors = [];
        $importedCount = 0;

        try {
            // Rozpocznij transakcję
            $this->db->beginTransaction();

            foreach ($appointments as $appointment) {
                // Walidacja podstawowych pól
                if (empty($appointment['doctor_id'])) {
                    $errors[] = "Brak ID lekarza";
                    continue;
                }

                if (empty($appointment['patient_name'])) {
                    $errors[] = "Brak nazwy pacjenta";
                    continue;
                }

                // Pobierz zmapowane ID lekarza z bufora (optymalne - O(1))
                $doctorId = $appointment['doctor_id'];
                $mappedDoctorId = $doctorMappings[$doctorId] ?? null;

                if (!$mappedDoctorId) {
                    $errors[] = "Lekarz '{$doctorId}' nie jest zmapowany";
                    continue;
                }

                // Przygotuj dane wizyty
                $appointmentData = [
                    'doctor_id' => $mappedDoctorId, // Użyj zmapowanego ID
                    'patient_name' => $appointment['patient_name'],
                    'appointment_time' => $appointment['appointment_time'] ?? null,
                    'appointment_duration' => $appointment['appointment_duration'] ?? 20,
                    'appointment_date' => $appointment['appointment_date'] ?? date('Y-m-d'),
                    'phone_number' => $appointment['phone_number'] ?? null,
                    'external_id' => $appointment['external_id'] ?? null,
                    'tracking_code' => $this->generateUniqueTrackingCode()
                ];

                // Dodaj pola statusu tylko jeśli są przekazane
                if (isset($appointment['status'])) {
                    $appointmentData['status'] = $appointment['status'];
                }
                if (isset($appointment['is_confirmed'])) {
                    $appointmentData['is_confirmed'] = (int)$appointment['is_confirmed'];
                }
                if (isset($appointment['is_patient_present'])) {
                    $appointmentData['is_patient_present'] = (int)$appointment['is_patient_present'];
                }
                if (isset($appointment['is_completed'])) {
                    $appointmentData['is_completed'] = (int)$appointment['is_completed'];
                }
                if (isset($appointment['is_sms_sent'])) {
                    $appointmentData['is_sms_sent'] = (int)$appointment['is_sms_sent'];
                }

                // Sprawdź czy wizyta już istnieje
                $existingAppointment = null;
                if (!empty($appointmentData['external_id'])) {
                    $checkStmt = $this->db->prepare("
                        SELECT id, tracking_code FROM queue_appointments
                        WHERE external_id = ? AND doctor_id = ? AND appointment_date = ?
                    ");
                    $checkStmt->execute([
                        $appointmentData['external_id'],
                        $mappedDoctorId,  // Użyj zmapowanego ID lekarza, a nie appointmentData['doctor_id']
                        $appointmentData['appointment_date']  // Dodaj datę do warunku sprawdzania
                    ]);
                    $existingAppointment = $checkStmt->fetch(PDO::FETCH_ASSOC);
                } else {
                    // Jeśli external_id jest pusty, ustaw go na NULL aby uniknąć konfliktów z unikalnym indeksem
                    $appointmentData['external_id'] = null;
                }

                if ($existingAppointment) {
                    // Aktualizuj istniejącą wizytę - buduj zapytanie dynamicznie
                    $updateFields = [
                        'patient_name = ?',
                        'appointment_time = ?',
                        'appointment_duration = ?',
                        'appointment_date = ?',
                        'phone_number = ?'
                    ];
                    $updateValues = [
                        $appointmentData['patient_name'],
                        $appointmentData['appointment_time'],
                        $appointmentData['appointment_duration'],
                        $appointmentData['appointment_date'],
                        $appointmentData['phone_number']
                    ];

                    // Dodaj pola statusu tylko jeśli są dostępne
                    if (isset($appointmentData['status'])) {
                        $updateFields[] = 'status = ?';
                        $updateValues[] = $appointmentData['status'];
                    }
                    if (isset($appointmentData['is_confirmed'])) {
                        $updateFields[] = 'is_confirmed = ?';
                        $updateValues[] = $appointmentData['is_confirmed'];
                    }
                    if (isset($appointmentData['is_patient_present'])) {
                        $updateFields[] = 'is_patient_present = ?';
                        $updateValues[] = $appointmentData['is_patient_present'];
                    }
                    if (isset($appointmentData['is_completed'])) {
                        $updateFields[] = 'is_completed = ?';
                        $updateValues[] = $appointmentData['is_completed'];
                    }
                    if (isset($appointmentData['is_sms_sent'])) {
                        $updateFields[] = 'is_sms_sent = ?';
                        $updateValues[] = $appointmentData['is_sms_sent'];
                    }

                    // Dodaj warunki WHERE
                    $updateValues[] = $appointmentData['external_id'];
                    $updateValues[] = $mappedDoctorId;
                    $updateValues[] = $appointmentData['appointment_date'];

                    $updateSql = "UPDATE queue_appointments SET " . implode(', ', $updateFields) .
                                " WHERE external_id = ? AND doctor_id = ? AND appointment_date = ?";

                    $stmt = $this->db->prepare($updateSql);
                    $result = $stmt->execute($updateValues);

                    if ($result) {
                        $importedCount++;
                        error_log("[Import] Zaktualizowano wizytę: external_id={$appointmentData['external_id']}");
                    }
                } else {
                    // Dodaj nową wizytę - buduj zapytanie dynamicznie
                    $insertFields = [
                        'doctor_id',
                        'patient_name',
                        'appointment_time',
                        'appointment_duration',
                        'appointment_date',
                        'phone_number',
                        'external_id',
                        'tracking_code',
                        'created_at'
                    ];
                    $insertValues = [
                        $mappedDoctorId,
                        $appointmentData['patient_name'],
                        $appointmentData['appointment_time'],
                        $appointmentData['appointment_duration'],
                        $appointmentData['appointment_date'],
                        $appointmentData['phone_number'],
                        $appointmentData['external_id'],
                        $appointmentData['tracking_code']
                    ];
                    $placeholders = ['?', '?', '?', '?', '?', '?', '?', '?', 'datetime(\'now\', \'localtime\')'];

                    // Dodaj pola statusu tylko jeśli są dostępne
                    if (isset($appointmentData['status'])) {
                        $insertFields[] = 'status';
                        $insertValues[] = $appointmentData['status'];
                        $placeholders[] = '?';
                    }
                    if (isset($appointmentData['is_confirmed'])) {
                        $insertFields[] = 'is_confirmed';
                        $insertValues[] = $appointmentData['is_confirmed'];
                        $placeholders[] = '?';
                    }
                    if (isset($appointmentData['is_patient_present'])) {
                        $insertFields[] = 'is_patient_present';
                        $insertValues[] = $appointmentData['is_patient_present'];
                        $placeholders[] = '?';
                    }
                    if (isset($appointmentData['is_completed'])) {
                        $insertFields[] = 'is_completed';
                        $insertValues[] = $appointmentData['is_completed'];
                        $placeholders[] = '?';
                    }
                    if (isset($appointmentData['is_sms_sent'])) {
                        $insertFields[] = 'is_sms_sent';
                        $insertValues[] = $appointmentData['is_sms_sent'];
                        $placeholders[] = '?';
                    }

                    // Użyj wszystkich wartości - datetime jest już w placeholders
                    $executeValues = $insertValues;

                    $insertSql = "INSERT INTO queue_appointments (" . implode(', ', $insertFields) .
                                ") VALUES (" . implode(', ', $placeholders) . ")";

                    $stmt = $this->db->prepare($insertSql);
                    $result = $stmt->execute($executeValues);

                    if ($result) {
                        $importedCount++;
                        error_log("[Import] Dodano wizytę: external_id={$appointmentData['external_id']}");
                    }
                }
            }

            // Zatwierdź transakcję
            $this->db->commit();

            // Wyczyść cache
            $this->cacheManager->markChanges('appointments');

            return $this->success([
                'imported' => $importedCount,
                'total' => count($appointments),
                'errors' => $errors,
                'source' => $source,
                'syncCode' => $syncCode
            ]);
        } catch (Exception $e) {
            // Cofnij transakcję w przypadku błędu
            $this->db->rollBack();
            error_log("[Import] Error in import: " . $e->getMessage());
            return $this->error('Błąd podczas importu: ' . $e->getMessage(), 500);
        }
    }



    /**
     * Generuje unikalny 16-znakowy kod śledzenia
     */
    private function generateUniqueTrackingCode() {
        do {
            // Generuj kod: 4 cyfry + 12 znaków alfanumerycznych
            $code = sprintf('%04d%s', rand(1000, 9999), substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 12));

            // Sprawdź czy kod już istnieje
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM queue_appointments WHERE tracking_code = ?");
            $stmt->execute([$code]);
            $exists = $stmt->fetchColumn() > 0;
        } while ($exists);

        return $code;
    }

    /**
     * Zapisz dane synchronizacji do pliku log
     */
    private function saveSyncLog($input, $syncCode) {
        try {
            $logDir = __DIR__ . '/../data_sync_log';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            $timestamp = date('Y-m-d_H-i-s');
            $filename = "import_sync_{$syncCode}_{$timestamp}.json";
            $filepath = $logDir . '/' . $filename;

            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'syncCode' => $syncCode,
                'source' => 'default',
                'data' => $input
            ];

            file_put_contents($filepath, json_encode($logData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            error_log("[Import] Saved sync log: $filename");
        } catch (Exception $e) {
            error_log("[Import] Error saving sync log: " . $e->getMessage());
        }
    }

    /**
     * Ładuje wszystkie mapowania lekarzy do bufora dla optymalnego dostępu
     * Zwraca tablicę asocjacyjną gdzie kluczem jest external_doctor_id, a wartością system doctor_id
     */
    private function loadDoctorMappingsToBuffer() {
        try {
            $sql = "
                SELECT id, external_doctor_id, first_name, last_name
                FROM queue_doctors
                WHERE external_doctor_id IS NOT NULL AND external_doctor_id != ''
            ";

            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $doctors = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Utwórz tablicę asocjacyjną dla optymalnego dostępu O(1)
            // Traktuj external_doctor_id jako dokładny identyfikator
            $mappings = [];
            foreach ($doctors as $doctor) {
                $mappings[$doctor['external_doctor_id']] = $doctor['id'];
            }

            error_log("[Import] Załadowano " . count($mappings) . " dokładnych mapowań lekarzy");

            return $mappings;
        } catch (Exception $e) {
            error_log("[Import] Błąd ładowania mapowań lekarzy: " . $e->getMessage());
            return [];
        }
    }


}