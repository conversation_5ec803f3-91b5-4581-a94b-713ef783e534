<?php

require_once __DIR__ . '/../core/StatusFilter.php';

class DisplayApiController extends ApiController {

    /**
     * Pobierz dane dla wyświetlacza
     */
    public function getDisplayData($code) {
        try {
            // <PERSON>najdź wyświetlacz po kodzie (uproszczone - bez company_name i queue_config)
            $stmt = $this->db->prepare("
                SELECT d.id, d.display_name as name, d.display_code,
                       d.is_online, d.last_heartbeat, d.created_at,
                       d.volume, d.show_subtitles, d.subtitle_font_size
                FROM client_displays d
                WHERE d.display_code = ? AND d.is_online >= 0
            ");
            $stmt->execute([$code]);
            $display = $stmt->fetch();

            if (!$display) {
                $this->error('Display not found or inactive', 404);
            }

            $response = [
                'display' => [
                    'id' => $display['id'],
                    'name' => $display['name'],
                    'code' => $display['display_code'],
                    'settings' => [
                        'volume' => $display['volume'] ?? 100,
                        'show_subtitles' => (bool)($display['show_subtitles'] ?? 1),
                        'subtitle_font_size' => $display['subtitle_font_size'] ?? 16
                    ]
                ],
                'queue' => null,
                'ads' => [],
                'timestamp' => date('c')
            ];

            // Pobierz dane kolejki - zawsze włączone (uproszczone)
            $response['queue'] = $this->getQueueData($display['id']);

            // Pobierz reklamy - zawsze włączone (Config::isModuleEnabled usunięte)
            $response['ads'] = $this->getAdsData();

            $this->success($response);
        } catch (Exception $e) {
            $this->log('Error getting display data: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to get display data', 500);
        }
    }

    /**
     * Heartbeat wyświetlacza
     */
    public function heartbeat($code) {
        try {
            // Znajdź wyświetlacz
            $stmt = $this->db->prepare("
                SELECT id FROM client_displays
                WHERE display_code = ? AND is_online >= 0
            ");
            $stmt->execute([$code]);
            $display = $stmt->fetch();

            if (!$display) {
                $this->error('Display not found', 404);
            }

            // Zaktualizuj ostatni heartbeat i ustaw status online
            $stmt = $this->db->prepare("
                UPDATE client_displays
                SET last_heartbeat = datetime('now'), is_online = 1
                WHERE id = ?
            ");
            $stmt->execute([$display['id']]);

            $this->success([
                'heartbeat_recorded' => true,
                'timestamp' => date('c')
            ]);
        } catch (Exception $e) {
            $this->log('Error recording heartbeat: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to record heartbeat', 500);
        }
    }

    /**
     * Pobierz dane kolejki dla wyświetlacza (uproszczone - bez client_id)
     */
    private function getQueueData($displayId = null) {
        try {
            // Pobierz sale (uproszczone - bez doctor_id)
            $stmt = $this->db->prepare("
                SELECT r.*
                FROM queue_rooms r
                ORDER BY r.name
            ");
            $stmt->execute();
            $rooms = $stmt->fetchAll();

            $roomsData = [];
            foreach ($rooms as $room) {
                // Pobierz aktualną wizytę dla lekarzy przypisanych do tego gabinetu
                // Filtrowanie względem wyświetlacza: lekarz musi być przypisany do tego wyświetlacza lub nie mieć przypisanego żadnego
                $stmt = $this->db->prepare("
                    SELECT qa.* FROM queue_appointments qa
                    JOIN queue_doctors qd ON qa.doctor_id = qd.id
                    WHERE qd.default_room_id = ? AND qa.status = 'current'
                    AND (qd.display_id IS NULL OR qd.display_id = ?)
                    ORDER BY qa.id DESC LIMIT 1
                ");
                $stmt->execute([$room['id'], $displayId]);
                $current = $stmt->fetch();

                // Sprawdź wizyty na kolejne dni jeśli nie ma na dzisiaj
                $waiting = [];
                $dateToCheck = date('Y-m-d'); // Dzisiaj
                $maxDays = 3; // Sprawdź dzisiaj, jutro i pojutrze
                $daysChecked = 0;

                while (empty($waiting) && $daysChecked < $maxDays) {
                    // Pobierz kolejne numery dla danej daty
                    // Pokaż tylko wizyty ze statusem 'waiting' - bez filtrowania czasowego
                    // Logika czasowa jest teraz obsługiwana przez statusy
                    // Filtrowanie względem wyświetlacza: lekarz musi być przypisany do tego wyświetlacza lub nie mieć przypisanego żadnego
                    $stmt = $this->db->prepare("
                        SELECT qa.* FROM queue_appointments qa
                        JOIN queue_doctors qd ON qa.doctor_id = qd.id
                        WHERE qd.default_room_id = ? AND qa.status = 'waiting'
                        AND date(qa.appointment_date) = ?
                        AND (qd.display_id IS NULL OR qd.display_id = ?)
                        ORDER BY qa.appointment_time ASC LIMIT 3
                    ");
                    $stmt->execute([$room['id'], $dateToCheck, $displayId]);
                    $waiting = $stmt->fetchAll();

                    if (empty($waiting)) {
                        // Sprawdź następny dzień
                        $daysChecked++;
                        $nextDate = new DateTime($dateToCheck);
                        $nextDate->add(new DateInterval('P1D'));
                        $dateToCheck = $nextDate->format('Y-m-d');
                    } else {
                        // Znaleziono wizyty - przerwij pętlę
                        break;
                    }
                }

                $roomsData[] = [
                    'id' => $room['id'],
                    'name' => $room['name'],
                    'room_number' => null, // room_number usunięte z tabeli
                    'doctor' => null, // doctor info usunięte z tabeli rooms
                    'current' => $current ? StatusFilter::filterSingleAppointment([
                        'number' => $current['appointment_time'],
                        'patient_name' => $current['patient_name'],
                        'phone_number' => $current['phone_number'],
                        'is_confirmed' => $current['is_confirmed'],
                        'is_patient_present' => $current['is_patient_present'],
                        'is_sms_sent' => $current['is_sms_sent'],
                        'tracking_code' => $current['tracking_code']
                    ], API_STATUS_SETTINGS) : null,
                    'waiting' => array_map(function ($appointment) {
                        return StatusFilter::filterSingleAppointment([
                            'number' => $appointment['appointment_time'],
                            'patient_name' => $appointment['patient_name'],
                            'phone_number' => $appointment['phone_number'],
                            'is_confirmed' => $appointment['is_confirmed'],
                            'is_patient_present' => $appointment['is_patient_present'],
                            'is_sms_sent' => $appointment['is_sms_sent'],
                            'tracking_code' => $appointment['tracking_code']
                        ], API_STATUS_SETTINGS);
                    }, $waiting)
                ];
            }

            return [
                'enabled' => true,
                'rooms' => $roomsData
            ];
        } catch (Exception $e) {
            $this->log('Error getting queue data for display: ' . $e->getMessage(), 'ERROR');
            return ['enabled' => false, 'error' => 'Failed to load queue data'];
        }
    }

    /**
     * Pobierz dane reklam dla wyświetlacza z algorytmem sprawiedliwego losowania
     * Algorytm zapewnia równomierne odtworzenia wszystkich materiałów
     */
    private function getAdsData() {
        try {
            // Pobierz 5 materiałów z najmniejszą liczbą wyświetleń
            $stmt = $this->db->prepare("
                SELECT c.id, c.name, c.media_type, c.media_url, c.youtube_id,
                       c.duration, c.description, COALESCE(c.ads_views, 0) as views_count
                FROM ads c
                WHERE c.approval_status = 'approved'
                ORDER BY COALESCE(c.ads_views, 0) ASC, c.id ASC
                LIMIT 5
            ");

            $stmt->execute();
            $topAds = $stmt->fetchAll();

            if (empty($topAds)) {
                return [];
            }

            // Losuj spośród 5 materiałów z najmniejszą liczbą wyświetleń
            $randomIndex = array_rand($topAds);
            $selectedAd = $topAds[$randomIndex];

            // Loguj informacje o wyborze
            $this->log(sprintf(
                'Display Fair ad selection: Selected ad ID %d (views: %d) from %d top ads with lowest views',
                $selectedAd['id'],
                $selectedAd['views_count'],
                count($topAds)
            ), 'INFO');

            // Przygotuj URL mediów
            if ($selectedAd['media_type'] === 'image' || $selectedAd['media_type'] === 'video') {
                $selectedAd['media_url'] = '/' . ltrim($selectedAd['media_url'], '/');
            }

            // Sprawdź dostępność pliku VTT dla video
            if ($selectedAd['media_type'] === 'video' && !empty($selectedAd['media_url'])) {
                $selectedAd['subtitles_url'] = $this->getSubtitlesUrl($selectedAd['media_url']);
                $selectedAd['has_subtitles'] = $this->checkSubtitlesExist($selectedAd['subtitles_url']);
            } else {
                $selectedAd['subtitles_url'] = null;
                $selectedAd['has_subtitles'] = false;
            }

            // Usuń pole views_count z odpowiedzi (tylko do wewnętrznego użytku)
            unset($selectedAd['views_count']);

            return [$selectedAd];
        } catch (Exception $e) {
            $this->log('Error getting ads data for display: ' . $e->getMessage(), 'ERROR');
            return [];
        }
    }



    /**
     * Generuj URL do pliku napisów VTT na podstawie URL video
     */
    private function getSubtitlesUrl($videoUrl) {
        // Zamień rozszerzenie video na .vtt
        $pathInfo = pathinfo($videoUrl);
        if (isset($pathInfo['dirname']) && isset($pathInfo['filename'])) {
            return $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.vtt';
        }
        return null;
    }

    /**
     * Sprawdź czy plik napisów VTT istnieje
     */
    private function checkSubtitlesExist($subtitlesUrl) {
        if (!$subtitlesUrl) {
            return false;
        }

        // Konwertuj URL na ścieżkę systemową
        $filePath = $_SERVER['DOCUMENT_ROOT'] . $subtitlesUrl;

        // Sprawdź czy plik istnieje
        return file_exists($filePath) && is_readable($filePath);
    }

    /**
     * Zarejestruj wyświetlenie materiału video
     */
    public function recordVideoView($code) {
        try {
            // Znajdź wyświetlacz po kodzie
            $stmt = $this->db->prepare("
                SELECT id FROM client_displays
                WHERE display_code = ? AND is_online >= 0
            ");
            $stmt->execute([$code]);
            $display = $stmt->fetch();

            if (!$display) {
                $this->error('Display not found', 404);
            }

            // Pobierz dane z żądania
            $input = json_decode(file_get_contents('php://input'), true);
            $videoId = $input['video_id'] ?? null;

            if (!$videoId) {
                $this->error('Video ID is required', 400);
            }

            // Zaktualizuj licznik wyświetleń w tabeli ads
            $stmt = $this->db->prepare("
                UPDATE ads
                SET ads_views = COALESCE(ads_views, 0) + 1
                WHERE id = ?
            ");
            $result = $stmt->execute([$videoId]);

            if ($result) {
                // Pobierz zaktualizowaną liczbę wyświetleń
                $stmt = $this->db->prepare("
                    SELECT ads_views FROM ads WHERE id = ?
                ");
                $stmt->execute([$videoId]);
                $result = $stmt->fetch();
                $totalViews = $result['ads_views'] ?? 0;

                $this->success([
                    'view_recorded' => true,
                    'video_id' => $videoId,
                    'display_id' => $display['id'],
                    'total_views' => $totalViews,
                    'timestamp' => date('c')
                ]);
            } else {
                $this->error('Failed to record view', 500);
            }
        } catch (Exception $e) {
            $this->log('Error recording video view: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to record video view', 500);
        }
    }

}
