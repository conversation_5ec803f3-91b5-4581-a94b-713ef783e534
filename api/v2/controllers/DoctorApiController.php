<?php

require_once __DIR__ . '/../core/EnhancedCacheManager.php';
require_once __DIR__ . '/../core/StatusFilter.php';

class DoctorApiController extends ApiController {
    private $cacheManager;

    public function __construct() {
        parent::__construct();
        $this->cacheManager = new EnhancedCacheManager();
    }

    /**
     * Logowanie lekarza przez kod dostępu
     */
    public function login() {
        try {
            // Zapisz log do pliku
            file_put_contents(__DIR__ . '/../../debug.log', date('Y-m-d H:i:s') . " - Doctor<PERSON>I login wywołane\n", FILE_APPEND);

            $data = $this->getRequestData();
            file_put_contents(__DIR__ . '/../../debug.log', date('Y-m-d H:i:s') . " - Otr<PERSON>mane dane: " . json_encode($data) . "\n", FILE_APPEND);

            $this->validateRequired($data, ['login', 'password']);

            $login = $data['login'];
            $password = $data['password'];
            file_put_contents(__DIR__ . '/../../debug.log', date('Y-m-d H:i:s') . " - Login: " . $login . "\n", FILE_APPEND);

            // Walidacja formatu email
            if (!filter_var($login, FILTER_VALIDATE_EMAIL)) {
                $this->error('Nieprawidłowy format adresu email', 400);
            }

            // Użyj modelu Doctor do weryfikacji loginu
            $doctorModel = new Doctor();
            $doctor = $doctorModel->verifyLogin($login, $password);

            error_log('DoctorAPI login - znaleziony lekarz: ' . json_encode($doctor));

            if (!$doctor) {
                error_log('DoctorAPI login - nie znaleziono lekarza dla loginu: ' . $login);
                $this->error('Nieprawidłowy login, hasło lub lekarz nieaktywny', 401);
            }

            // Przygotuj dane lekarza
            $photoUrl = $doctor['photo_url'];
            // Upewnij się że ścieżka do zdjęcia zaczyna się od /uploads/
            if ($photoUrl && !str_starts_with($photoUrl, '/uploads/')) {
                // Jeśli ścieżka zaczyna się od uploads/ (bez leading slash), dodaj slash na początku
                if (str_starts_with($photoUrl, 'uploads/')) {
                    $photoUrl = '/' . $photoUrl;
                }
                // Jeśli ścieżka nie zawiera uploads/ w ogóle, dodaj pełną ścieżkę
                else if (!str_contains($photoUrl, 'uploads/')) {
                    $photoUrl = '/uploads/' . $photoUrl;
                }
            }

            $doctorData = [
                'id' => $doctor['id'],
                'first_name' => $doctor['first_name'],
                'last_name' => $doctor['last_name'],
                'specialization' => $doctor['specialization'],
                'photo_url' => $photoUrl,
                'client_name' => $doctor['client_name'] ?? 'KtoOstatni'
            ];

            // Pobierz domyślny gabinet
            $defaultRoom = null;
            if ($doctor['default_room_id']) {
                $stmt = $this->db->prepare("
                    SELECT id, name
                    FROM queue_rooms
                    WHERE id = ?
                ");
                $stmt->execute([$doctor['default_room_id']]);
                $defaultRoom = $stmt->fetch();
            }

            // Pobierz wszystkie dostępne gabinety
            // UWAGA: Kolumny room_number, active, client_id zostały usunięte z queue_rooms
            $stmt = $this->db->prepare("
                SELECT r.id, r.name
                FROM queue_rooms r
                ORDER BY r.name
            ");
            $stmt->execute();
            $availableRooms = $stmt->fetchAll();

            $this->success([
                'doctor' => $doctorData,
                'default_room' => $defaultRoom,
                'available_rooms' => $availableRooms
            ]);
        } catch (Exception $e) {
            $this->error('Błąd logowania: ' . $e->getMessage(), 500);
        }
    }
/**
 * Sprawdź dostępność gabinetu dla lekarza w danym dniu
 */
public function checkRoomAvailability() {
    try {
        $data = $this->getRequestData();
        $this->validateRequired($data, ['doctor_id', 'room_id', 'date']);

        $doctorId = $data['doctor_id'];
        $roomId = $data['room_id'];
        $date = $data['date'];

        // Sprawdź czy lekarz istnieje i jest aktywny
        // UWAGA: Kolumny company_name i is_active zostały usunięte z users
        $stmt = $this->db->prepare("
            SELECT d.*, u.username as client_name
            FROM queue_doctors d
            JOIN users u ON 1=1 -- UWAGA: Kolumna client_id została usunięta z queue_doctors
            WHERE d.id = ? AND d.active = 1
        ");
        $stmt->execute([$doctorId]);
        $doctor = $stmt->fetch();

        if (!$doctor) {
            $this->error('Lekarz nie istnieje lub nieaktywny', 404);
        }

        // Sprawdź czy gabinet istnieje
        // UWAGA: Kolumny client_id, active zostały usunięte z queue_rooms
        $stmt = $this->db->prepare("
            SELECT r.*
            FROM queue_rooms r
            WHERE r.id = ?
        ");
        $stmt->execute([$roomId]);
        $room = $stmt->fetch();

        if (!$room) {
            $this->error('Gabinet nie istnieje', 404);
        }

        $this->success([
            'available' => true,
            'room' => $room,
            'doctor' => $doctor,
            'date' => $date
        ]);
    } catch (Exception $e) {
        $this->error('Błąd sprawdzania dostępności gabinetu: ' . $e->getMessage(), 500);
    }
}

    /**
     * Pobierz wizyty dla lekarza (nowy endpoint bez roomId)
     */
    public function doctorAppointments() {
        try {
            $doctorId = $_GET['doctor_id'] ?? null;
            $date = $_GET['date'] ?? date('Y-m-d'); // Domyślnie dzisiejsza data

            if (!$doctorId) {
                $this->error('Brak ID lekarza', 400);
            }

            // Pobierz dane lekarza z domyślnym gabinetem
            $stmt = $this->db->prepare("
                SELECT d.*, r.name as room_name
                FROM queue_doctors d
                LEFT JOIN queue_rooms r ON d.default_room_id = r.id
                WHERE d.id = ? AND d.active = 1
            ");
            $stmt->execute([$doctorId]);
            $doctor = $stmt->fetch();

            if (!$doctor) {
                $this->error('Lekarz nie istnieje lub nieaktywny', 404);
            }

            // Pobierz aktualną wizytę dla lekarza
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ? AND status = 'current'
                ORDER BY appointment_time ASC
                LIMIT 1
            ");
            $stmt->execute([$doctorId, $date]);
            $current = $stmt->fetch();

            // Pobierz oczekujące wizyty dla lekarza
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ? AND status = 'waiting'
                ORDER BY appointment_time ASC
                LIMIT 10
            ");
            $stmt->execute([$doctorId, $date]);
            $waiting = $stmt->fetchAll();

            // Pobierz wszystkie wizyty dla PWA
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ?
                ORDER BY appointment_time ASC
            ");
            $stmt->execute([$doctorId, $date]);
            $allAppointments = $stmt->fetchAll();

            // Filtruj statusy na podstawie ustawień
            $current = StatusFilter::filterSingleAppointment($current, API_STATUS_SETTINGS);
            $waiting = StatusFilter::filterAppointments($waiting, API_STATUS_SETTINGS);
            $allAppointments = StatusFilter::filterAppointments($allAppointments, API_STATUS_SETTINGS);

            // Loguj informacje o filtrowaniu
            StatusFilter::logFilteringInfo(API_STATUS_SETTINGS, count($allAppointments));

            $this->success([
                'doctor' => [
                    'id' => $doctor['id'],
                    'first_name' => $doctor['first_name'],
                    'last_name' => $doctor['last_name'],
                    'room_name' => $doctor['room_name']
                ],
                'current' => $current,
                'waiting' => $waiting,
                'all_appointments' => $allAppointments
            ]);
        } catch (Exception $e) {
            $this->error('Błąd pobierania wizyt: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Wywołaj następną wizytę (nowy endpoint bez roomId)
     */
    public function doctorCallNext() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id']);

            $doctorId = $data['doctor_id'];

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Zakończ aktualną wizytę lekarza
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now', 'localtime'), is_completed = 1
                WHERE doctor_id = ? AND status = 'current'
            ");
            $stmt->execute([$doctorId]);

            // Wywołaj następną wizytę lekarza
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'current', called_at = datetime('now', 'localtime'), is_patient_present = 1
                WHERE id = (
                    SELECT id FROM queue_appointments
                    WHERE doctor_id = ? AND status = 'waiting'
                    ORDER BY appointment_time ASC
                    LIMIT 1
                )
            ");
            $stmt->execute([$doctorId]);

            // Pobierz nową aktualną wizytę
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND status = 'current'
            ");
            $stmt->execute([$doctorId]);
            $nextAppointment = $stmt->fetch();

            $this->db->commit();

            // Oznaczenie zmian w cache dla natychmiastowej synchronizacji
            $this->cacheManager->markChanges('appointments');
            $this->cacheManager->markChanges('queue');

            // Filtruj statusy przed zwróceniem
            if ($nextAppointment) {
                $nextAppointment = StatusFilter::filterSingleAppointment($nextAppointment, API_STATUS_SETTINGS);
                $this->success(['appointment' => $nextAppointment]);
            } else {
                $this->success(['appointment' => null, 'message' => 'Brak kolejnych wizyt']);
            }
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd wywołania następnej wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Wróć do poprzedniej wizyty (nowy endpoint bez roomId)
     */
    public function doctorPrevious() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id']);

            $doctorId = $data['doctor_id'];

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Znajdź ostatnią zakończoną wizytę lekarza
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND (status = 'closed' OR status = 'completed')
                ORDER BY completed_at DESC
                LIMIT 1
            ");
            $stmt->execute([$doctorId]);
            $lastCompleted = $stmt->fetch();

            if (!$lastCompleted) {
                $this->db->rollBack();
                $this->error('Brak poprzedniej wizyty do przywrócenia', 404);
            }

            // Ustaw aktualną wizytę jako oczekującą
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'waiting', called_at = NULL
                WHERE doctor_id = ? AND status = 'current'
            ");
            $stmt->execute([$doctorId]);

            // Przywróć poprzednią wizytę jako aktualną
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'current', completed_at = NULL, called_at = datetime('now', 'localtime')
                WHERE id = ?
            ");
            $stmt->execute([$lastCompleted['id']]);

            $this->db->commit();

            // Oznaczenie zmian w cache dla natychmiastowej synchronizacji
            $this->cacheManager->markChanges('appointments');
            $this->cacheManager->markChanges('queue');

            // Filtruj statusy przed zwróceniem
            $lastCompleted = StatusFilter::filterSingleAppointment($lastCompleted, API_STATUS_SETTINGS);
            $this->success(['appointment' => $lastCompleted]);
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd cofania wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Pomiń aktualną wizytę (nowy endpoint bez roomId)
     */
    public function doctorSkipCurrent() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id']);

            $doctorId = $data['doctor_id'];

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Oznacz aktualną wizytę lekarza jako pominiętą i ustaw is_patient_present = 0
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now', 'localtime'), is_completed = 1, is_patient_present = 0
                WHERE doctor_id = ? AND status = 'current'
            ");
            $stmt->execute([$doctorId]);

            // Wywołaj następną wizytę lekarza (nie ustawiaj automatycznie is_patient_present=1)
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'current', called_at = datetime('now', 'localtime')
                WHERE id = (
                    SELECT id FROM queue_appointments
                    WHERE doctor_id = ? AND status = 'waiting'
                    ORDER BY appointment_time ASC
                    LIMIT 1
                )
            ");
            $stmt->execute([$doctorId]);

            // Pobierz nową aktualną wizytę
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND status = 'current'
            ");
            $stmt->execute([$doctorId]);
            $nextAppointment = $stmt->fetch();

            $this->db->commit();

            // Oznaczenie zmian w cache dla natychmiastowej synchronizacji
            $this->cacheManager->markChanges('appointments');
            $this->cacheManager->markChanges('queue');

            // Filtruj statusy przed zwróceniem
            if ($nextAppointment) {
                $nextAppointment = StatusFilter::filterSingleAppointment($nextAppointment, API_STATUS_SETTINGS);
                $this->success(['appointment' => $nextAppointment]);
            } else {
                $this->success(['appointment' => null, 'message' => 'Brak kolejnych wizyt']);
            }
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd pomijania wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Wywołaj konkretną wizytę (nowy endpoint bez roomId)
     */
    public function doctorCallSpecific() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id', 'appointment_id']);

            $doctorId = $data['doctor_id'];
            $appointmentId = $data['appointment_id'];

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Sprawdź czy wizyta istnieje i należy do tego lekarza
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE id = ? AND doctor_id = ?
            ");
            $stmt->execute([$appointmentId, $doctorId]);
            $appointment = $stmt->fetch();

            if (!$appointment) {
                $this->db->rollBack();
                $this->error('Wizyta nie została znaleziona lub nie należy do tego lekarza', 404);
            }

            // Zakończ aktualną wizytę lekarza (jeśli istnieje) i ustaw is_patient_present = 0
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now', 'localtime'), is_completed = 1, is_patient_present = 0
                WHERE doctor_id = ? AND status = 'current'
            ");
            $stmt->execute([$doctorId]);

            // Wywołaj konkretną wizytę (nie ustawiaj automatycznie is_patient_present=1)
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'current', called_at = datetime('now', 'localtime')
                WHERE id = ?
            ");
            $stmt->execute([$appointmentId]);

            $this->db->commit();

            // Oznaczenie zmian w cache dla natychmiastowej synchronizacji
            $this->cacheManager->markChanges('appointments');
            $this->cacheManager->markChanges('queue');

            // Filtruj statusy przed zwróceniem
            $appointment = StatusFilter::filterSingleAppointment($appointment, API_STATUS_SETTINGS);
            $this->success([
                'appointment' => $appointment,
                'message' => 'Wizyta została wywołana'
            ]);
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd wywołania konkretnej wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Nawiguj do poprzedniej wizyty (bez kończenia aktualnej) - nowy endpoint bez roomId
     */
    public function doctorNavigatePrevious() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id']);

            $doctorId = $data['doctor_id'];
            $date = $data['date'] ?? date('Y-m-d');

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Pobierz aktualną wizytę
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ? AND status = 'current'
                LIMIT 1
            ");
            $stmt->execute([$doctorId, $date]);
            $currentAppointment = $stmt->fetch();

            if (!$currentAppointment) {
                $this->db->rollBack();
                $this->error('Brak aktualnej wizyty', 404);
            }

            // Znajdź poprzednią wizytę (wcześniejszą godzinę)
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ?
                AND appointment_time < ?
                AND status IN ('waiting', 'closed', 'completed')
                ORDER BY appointment_time DESC
                LIMIT 1
            ");
            $stmt->execute([$doctorId, $date, $currentAppointment['appointment_time']]);
            $previousAppointment = $stmt->fetch();

            if (!$previousAppointment) {
                $this->db->rollBack();
                $this->error('Brak poprzedniej wizyty', 404);
            }

            // Ustaw aktualną wizytę jako oczekującą (zachowaj oryginalną wartość is_patient_present)
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'waiting', called_at = NULL
                WHERE id = ?
            ");
            $stmt->execute([$currentAppointment['id']]);

            // Ustaw poprzednią wizytę jako aktualną (nie zmieniaj is_patient_present automatycznie)
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'current', called_at = datetime('now', 'localtime'), completed_at = NULL, is_completed = 0
                WHERE id = ?
            ");
            $stmt->execute([$previousAppointment['id']]);

            $this->db->commit();

            // Oznaczenie zmian w cache dla natychmiastowej synchronizacji
            $this->cacheManager->markChanges('appointments');
            $this->cacheManager->markChanges('queue');

            // Filtruj statusy przed zwróceniem
            $previousAppointment = StatusFilter::filterSingleAppointment($previousAppointment, API_STATUS_SETTINGS);
            $this->success(['appointment' => $previousAppointment]);
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd nawigacji do poprzedniej wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Nawiguj do następnej wizyty (bez kończenia aktualnej) - nowy endpoint bez roomId
     */
    public function doctorNavigateNext() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id']);

            $doctorId = $data['doctor_id'];
            $date = $data['date'] ?? date('Y-m-d');

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Pobierz aktualną wizytę
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ? AND status = 'current'
                LIMIT 1
            ");
            $stmt->execute([$doctorId, $date]);
            $currentAppointment = $stmt->fetch();

            if (!$currentAppointment) {
                $this->db->rollBack();
                $this->error('Brak aktualnej wizyty', 404);
            }

            // Znajdź następną wizytę (późniejszą godzinę)
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ?
                AND appointment_time > ?
                AND status IN ('waiting', 'closed', 'completed')
                ORDER BY appointment_time ASC
                LIMIT 1
            ");
            $stmt->execute([$doctorId, $date, $currentAppointment['appointment_time']]);
            $nextAppointment = $stmt->fetch();

            if (!$nextAppointment) {
                $this->db->rollBack();
                $this->error('Brak następnej wizyty', 404);
            }

            // Ustaw aktualną wizytę jako oczekującą
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'waiting', called_at = NULL, is_patient_present = 0
                WHERE id = ?
            ");
            $stmt->execute([$currentAppointment['id']]);

            // Ustaw następną wizytę jako aktualną
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'current', called_at = datetime('now', 'localtime'), completed_at = NULL, is_patient_present = 1, is_completed = 0
                WHERE id = ?
            ");
            $stmt->execute([$nextAppointment['id']]);

            $this->db->commit();

            // Oznaczenie zmian w cache dla natychmiastowej synchronizacji
            $this->cacheManager->markChanges('appointments');
            $this->cacheManager->markChanges('queue');

            // Filtruj statusy przed zwróceniem
            $nextAppointment = StatusFilter::filterSingleAppointment($nextAppointment, API_STATUS_SETTINGS);
            $this->success(['appointment' => $nextAppointment]);
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd nawigacji do następnej wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Zamknij konkretną wizytę (nowy endpoint)
     */
    public function doctorCloseAppointment() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id', 'appointment_id']);

            $doctorId = $data['doctor_id'];
            $appointmentId = $data['appointment_id'];

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Sprawdź czy wizyta istnieje i należy do tego lekarza
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE id = ? AND doctor_id = ?
            ");
            $stmt->execute([$appointmentId, $doctorId]);
            $appointment = $stmt->fetch();

            if (!$appointment) {
                $this->db->rollBack();
                $this->error('Wizyta nie została znaleziona lub nie należy do tego lekarza', 404);
            }

            // Zamknij wizytę (ustaw status na 'closed', completed_at i is_completed = 1)
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now', 'localtime'), is_completed = 1
                WHERE id = ?
            ");
            $stmt->execute([$appointmentId]);

            // Jeśli zamykana wizyta była aktualną wizytą, wyczyść aktualną wizytę lekarza
            if ($appointment['status'] === 'current') {
                // Nie rób nic więcej - wizyta jest już zamknięta
            }

            $this->db->commit();

            // Oznaczenie zmian w cache dla natychmiastowej synchronizacji
            $this->cacheManager->markChanges('appointments');
            $this->cacheManager->markChanges('queue');

            // Filtruj statusy przed zwróceniem
            $appointment = StatusFilter::filterSingleAppointment($appointment, API_STATUS_SETTINGS);
            $this->success([
                'appointment' => $appointment,
                'message' => 'Wizyta została zamknięta'
            ]);
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd zamykania wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Pobierz statystyki dla lekarza (nowy endpoint bez roomId)
     */
    public function doctorStats() {
        try {
            $doctorId = $_GET['doctor_id'] ?? null;
            $date = $_GET['date'] ?? date('Y-m-d');

            if (!$doctorId) {
                $this->error('Brak ID lekarza', 400);
            }

            // Pobierz statystyki wizyt lekarza dla danego dnia
            $stmt = $this->db->prepare("
                SELECT
                    COUNT(*) as total_appointments,
                    SUM(CASE WHEN status = 'closed' OR status = 'completed' THEN 1 ELSE 0 END) as completed_appointments,
                    SUM(CASE WHEN status = 'waiting' THEN 1 ELSE 0 END) as waiting_appointments,
                    SUM(CASE WHEN status = 'current' THEN 1 ELSE 0 END) as current_appointments
                FROM queue_appointments
                WHERE doctor_id = ? AND appointment_date = ?
            ");
            $stmt->execute([$doctorId, $date]);
            $stats = $stmt->fetch();

            $this->success([
                'doctor_id' => $doctorId,
                'date' => $date,
                'stats' => $stats
            ]);
        } catch (Exception $e) {
            $this->error('Błąd pobierania statystyk: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Przełącz status obecności pacjenta
     */
    public function togglePatientPresence() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['doctor_id', 'appointment_id', 'is_present']);

            $doctorId = $data['doctor_id'];
            $appointmentId = $data['appointment_id'];
            $isPresent = $data['is_present'] ? 1 : 0;

            // Rozpocznij transakcję
            $this->db->beginTransaction();

            // Sprawdź czy wizyta istnieje i należy do tego lekarza
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments
                WHERE id = ? AND doctor_id = ?
            ");
            $stmt->execute([$appointmentId, $doctorId]);
            $appointment = $stmt->fetch();

            if (!$appointment) {
                $this->db->rollBack();
                $this->error('Wizyta nie została znaleziona lub nie należy do tego lekarza', 404);
            }

            // Zaktualizuj status obecności pacjenta
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET is_patient_present = ?
                WHERE id = ?
            ");
            $stmt->execute([$isPresent, $appointmentId]);

            $this->db->commit();

            // Oznaczenie zmian w cache dla natychmiastowej synchronizacji
            $this->cacheManager->markChanges('appointments');
            $this->cacheManager->markChanges('queue');

            $message = $isPresent ? 'Oznaczono pacjenta jako obecnego' : 'Oznaczono pacjenta jako nieobecnego';
            $this->success([
                'appointment_id' => $appointmentId,
                'is_present' => $isPresent,
                'message' => $message
            ]);
        } catch (Exception $e) {
            $this->db->rollBack();
            $this->error('Błąd zmiany statusu obecności pacjenta: ' . $e->getMessage(), 500);
        }
    }

}
