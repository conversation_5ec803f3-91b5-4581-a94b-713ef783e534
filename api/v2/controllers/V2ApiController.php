<?php
require_once __DIR__ . '/../core/ApiController.php';
require_once __DIR__ . '/../core/EnhancedCacheManager.php';
require_once __DIR__ . '/../core/CacheUpdater.php';
require_once __DIR__ . '/../core/StatusFilter.php';

class V2ApiController extends ApiController {
    private $cacheManager;

    public function __construct() {
        parent::__construct();
        $this->cacheManager = new EnhancedCacheManager();
    }

    /**
     * Import danych z Chrome Extension
     */
    public function importData() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);

            // Logowanie przychodzących danych
            error_log('[V2API] Import request received. Raw data: ' . json_encode($input));
            error_log('[V2API] Import request keys: ' . implode(', ', array_keys($input ?? [])));

            if (!$input) {
                return $this->error('Brak danych do importu', 400);
            }

            $source = $input['source'] ?? 'chrome_extension';
            $syncCode = $input['syncCode'] ?? null;

            error_log('[V2API] Source: ' . $source . ', syncCode: ' . $syncCode);

            // Sprawdź czy dane są w nowym formacie (z Chrome Extension)
            if (isset($input['syncData']) && isset($input['syncData']['days'])) {
                error_log('[V2API] Processing new format data');
                return $this->importFromChromeExtension($input, $source, $syncCode);
            }

            error_log('[V2API] Invalid format - expected syncData');
            return $this->error('Nieprawidłowy format danych - oczekiwano syncData', 400);
        } catch (Exception $e) {
            error_log('[V2API] Error: ' . $e->getMessage());
            return $this->error('Błąd podczas importu: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Import danych z Chrome Extension (nowy format)
     */
    private function importFromChromeExtension($input, $source, $syncCode) {
        $syncData = $input['syncData'];
        $days = $syncData['days'] ?? [];

        // Zapisz dane do pliku log (przed sprawdzeniem czy days jest puste)
        $this->saveSyncLog($input, $syncCode);

        if (empty($days)) {
            return $this->error('Brak danych dni do importu', 400);
        }

        $errors = [];

        try {
            // OPTYMALIZACJA 1: Pobierz wszystkie mapowania lekarzy na początku
            $doctorMappings = $this->getAllDoctorMappings($syncCode);

            // OPTYMALIZACJA 2: Pobierz wszystkie wizyty z bazy danych dla dni importu
            $existingAppointments = $this->getExistingAppointmentsForDays($days, $syncCode);

            // OPTYMALIZACJA 3: Przygotuj dane do importu w pamięci
            $appointmentsToProcess = [];
            $visitsWithVisitTrue = [];

            foreach ($days as $day) {
                if (!isset($day['doctors']) || !is_array($day['doctors'])) {
                    continue;
                }

                foreach ($day['doctors'] as $doctor) {
                    if (!isset($doctor['appointments']) || !is_array($doctor['appointments'])) {
                        continue;
                    }

                    // Pobierz mapowanie lekarza z cache
                    $mappedDoctorId = $doctorMappings[$doctor['doctorId']] ?? null;

                    if (!$mappedDoctorId) {
                        $errors[] = "Brak mapowania dla lekarza {$doctor['doctorId']} ({$doctor['doctorName']})";
                        continue;
                    }

                    // Aktualizuj datę last_seen dla tego lekarza
                    $this->updateDoctorLastSeen($doctor['doctorId'], $syncCode);

                    foreach ($doctor['appointments'] as $appointment) {
                        // Parsuj statusTag dla tagów reservation
                        $isPatientPresent = 0;

                        if (isset($appointment['statusTag']) && $appointment['statusTag']) {
                            $statusTag = $appointment['statusTag'];
                            // Sprawdź czy to tag reservation w formacie: "tags_reservation_{appointmentId}_{tagId}"
                            if (preg_match('/^tags_reservation_\d+_(\d+)$/', $statusTag, $matches)) {
                                $tagId = (int)$matches[1];
                                // Jeśli tag ID to 4 lub 7, ustaw is_patient_present na 1
                                if ($tagId === 4 || $tagId === 7) {
                                    $isPatientPresent = 1;
                                }
                            }
                        }

                        $appointmentData = [
                            'id' => $appointment['appointmentId'] ?? null,
                            'doctor_id' => $mappedDoctorId,
                            // 'client_id' => $syncCode, // USUNIĘTE - kolumna client_id została usunięta z queue_appointments
                            'patient_name' => trim(($appointment['patientFirstName'] ?? '') . ' ' . ($appointment['patientLastName'] ?? '')),
                            'appointment_time' => $day['date'] . ' ' . ($appointment['appointmentStart'] ?? ''),
                            'appointment_duration' => $appointment['appointmentDuration'] ?? 20,
                            'mobile' => $appointment['mobile'] ?? null,
                            'res_confirmed' => $appointment['resConfirmed'] ?? false,
                            'visit' => $appointment['visit'] ?? false,
                            'locked' => $appointment['locked'] ?? false,
                            'is_patient_present' => $isPatientPresent,
                            'statusTag' => $appointment['statusTag'] ?? null
                        ];

                        // Zbierz wizyty z visit=true dla każdego lekarza
                        if ($appointment['visit'] === true) {
                            $visitsWithVisitTrue[$mappedDoctorId][] = $appointmentData;
                        }

                        $appointmentsToProcess[] = $appointmentData;
                    }
                }
            }

            // OPTYMALIZACJA 4: Określ statusy wizyt w pamięci
            $this->determineAppointmentStatuses($appointmentsToProcess, $visitsWithVisitTrue);

            // OPTYMALIZACJA 5: Porównaj z istniejącymi wizytami i przygotuj zmiany
            $changes = $this->prepareAppointmentChanges($appointmentsToProcess, $existingAppointments);

            // OPTYMALIZACJA 6: Zaktualizuj dane w jednej transakcji
            $importedCount = $this->applyAppointmentChanges($changes);

            // Wyczyść duplikaty wizyt (zachowaj tylko najnowsze)
            $this->cleanDuplicateAppointments($syncCode);

            // Dodaj tracking codes dla istniejących wizyt bez tracking codes
            $this->addMissingTrackingCodes($syncCode);

            // Aktualizuj datę ostatniej synchronizacji
            $this->updateLastSyncDate($syncCode);

            // Wyczyść cache
            $this->cacheManager->markChanges('appointments');

            return $this->success([
                'imported' => $importedCount,
                'total_days' => count($days),
                'errors' => $errors,
                'source' => $source,
                'syncCode' => $syncCode
            ]);
        } catch (Exception $e) {
            error_log("[V2API] Error in optimized import: " . $e->getMessage());
            return $this->error('Błąd podczas importu: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Aktualizuj datę last_seen dla lekarza w queue_doctors
     */
    private function updateDoctorLastSeen($externalDoctorId, $syncCode) {
        try {
            // Aktualizuj datę external_last_sync (użyj lokalnego czasu)
            $stmt = $this->db->prepare("
                UPDATE queue_doctors
                SET external_last_sync = datetime('now', 'localtime')
                WHERE external_doctor_id = ?
            ");
            $result = $stmt->execute([$externalDoctorId]);

            if ($result) {
                error_log("[V2API] Updated external_last_sync for doctor $externalDoctorId");
            }

            return $result;
        } catch (Exception $e) {
            error_log("[V2API] Error updating doctor last_seen: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Pobierz wszystkie mapowania lekarzy (optymalizacja)
     */
    private function getAllDoctorMappings($syncCode) {
        try {
            // Pobierz wszystkie mapowania lekarzy z queue_doctors
            $stmt = $this->db->prepare("
                SELECT external_doctor_id, id
                FROM queue_doctors
                WHERE external_doctor_id IS NOT NULL AND external_doctor_id != ''
            ");
            $stmt->execute();
            $mappings = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Konwertuj na tablicę asocjacyjną
            $result = [];
            foreach ($mappings as $mapping) {
                $result[$mapping['external_doctor_id']] = $mapping['id'];
            }

            return $result;
        } catch (Exception $e) {
            error_log("[V2API] Error getting all doctor mappings: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Pobierz mapowanie lekarza z queue_doctors (stara funkcja - zachowana dla kompatybilności)
     */
    private function getMappedDoctorId($externalDoctorId, $syncCode) {
        try {
            // Pobierz mapowanie lekarza z queue_doctors
            $stmt = $this->db->prepare("
                SELECT id
                FROM queue_doctors
                WHERE external_doctor_id = ? AND external_doctor_id IS NOT NULL AND external_doctor_id != ''
            ");
            $stmt->execute([$externalDoctorId]);
            $mapping = $stmt->fetch(PDO::FETCH_ASSOC);

            return $mapping ? $mapping['id'] : null;
        } catch (Exception $e) {
            error_log("[V2API] Error getting mapped doctor ID: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Aktualizuj datę ostatniej synchronizacji - UWAGA: Funkcjonalność wyłączona
     * Tabela import_settings została usunięta - używamy tabeli settings
     */
    private function updateLastSyncDate($syncCode) {
        try {
            // Zapisz datę ostatniej synchronizacji w tabeli settings
            $stmt = $this->db->prepare("
                INSERT OR REPLACE INTO settings (key, value, updated_at)
                VALUES (?, ?, datetime('now'))
            ");
            $stmt->execute(['import.' . $syncCode . '.last_sync', date('c')]);
            error_log("[V2API] Updated last_sync for syncCode: $syncCode in settings table");
        } catch (Exception $e) {
            error_log("[V2API] Error updating last_sync: " . $e->getMessage());
        }
    }

    /**
     * Zapisz dane synchronizacji do pliku log
     */
    private function saveSyncLog($input, $syncCode) {
        try {
            $logDir = __DIR__ . '/../data_sync_log';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            $timestamp = date('Y-m-d_H-i-s');
            $filename = "sync_{$syncCode}_{$timestamp}.json";
            $filepath = $logDir . '/' . $filename;

            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'syncCode' => $syncCode,
                'data' => $input
            ];

            file_put_contents($filepath, json_encode($logData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            error_log("[V2API] Saved sync log: $filename");
        } catch (Exception $e) {
            error_log("[V2API] Error saving sync log: " . $e->getMessage());
        }
    }

    /**
     * Import pojedynczej wizyty z nową logiką
     */
    private function importSingleAppointment($appointment, $visitsWithVisitTrue = []) {
        // Upewnij się, że kolumna status_tag istnieje w tabeli
        $this->ensureStatusTagColumnExists();

        // Sprawdź czy wizyta już istnieje
        $existingAppointment = null;
        if (!empty($appointment['id'])) {
            $checkStmt = $this->db->prepare("
                SELECT id, tracking_code, is_confirmed, is_patient_present, status, locked, visit FROM queue_appointments
                WHERE external_id = ? AND doctor_id = ?
            ");
            $checkStmt->execute([
                $appointment['id'],
                $appointment['doctor_id'] ?? null
            ]);
            $existingAppointment = $checkStmt->fetch(PDO::FETCH_ASSOC);
        }

        // Generuj unikalny tracking_code jeśli wizyta nie istnieje
        $trackingCode = null;
        if ($existingAppointment) {
            $trackingCode = $existingAppointment['tracking_code'];
        }

        if (empty($trackingCode)) {
            $trackingCode = $this->generateUniqueTrackingCode();
        }

        // NOWA LOGIKA: Określ status wizyty zgodnie z wymaganiami

        // 1. LOGIKA resConfirmed: raz true = zawsze true
        $finalIsConfirmed = $appointment['res_confirmed'] ? 1 : 0;
        if ($existingAppointment && $existingAppointment['is_confirmed'] == 1) {
            $finalIsConfirmed = 1; // Zachowaj true jeśli było już ustawione
        }

        // 2. LOGIKA visit: visit=true oznacza że wizyta się odbyła
        $finalIsPatientPresent = $appointment['is_patient_present'] ?? 0;
        $finalVisit = $appointment['visit'] ? 1 : 0;
        $finalStatus = 'waiting'; // Domyślnie waiting

        if ($appointment['visit'] === true) {
            // Wizyta się odbyła - sprawdź czy to najnowsza wizyta z visit=true
            $isNewestVisit = $this->isNewestVisitWithVisitTrueInMemory($appointment, $visitsWithVisitTrue);

            if ($isNewestVisit) {
                // To najnowsza wizyta z visit=true - ustaw jako current (właśnie trwa)
                $finalStatus = 'current';
                $finalIsPatientPresent = 1;
            } else {
                // To starsza wizyta z visit=true - zamknij ją
                $finalStatus = 'closed';
                $finalIsPatientPresent = 1;
            }
        }

        // 3. LOGIKA locked: true = status closed na zawsze (ale tylko jeśli visit nie ustawił current)
        if ($appointment['locked'] === true && $finalStatus !== 'current') {
            $finalStatus = 'closed';
        } else if ($existingAppointment && $existingAppointment['locked'] == 1 && $finalStatus !== 'current') {
            $finalStatus = 'closed'; // Zachowaj closed jeśli było już ustawione
        }

        // Zachowaj wartości locked i visit jeśli były już ustawione w bazie
        $finalLocked = $appointment['locked'] ? 1 : 0;
        if ($existingAppointment && $existingAppointment['locked'] == 1) {
            $finalLocked = 1; // Zachowaj locked=1 jeśli było już ustawione
        }

        if ($existingAppointment && $existingAppointment['visit'] == 1) {
            $finalVisit = 1; // Zachowaj visit=1 jeśli było już ustawione
        }

        // Jeśli wizyta ma być wywołana, wywołaj ją przez HTTP (tylko dla najnowszej z visit=true)
        if ($finalStatus === 'current' && $existingAppointment && $appointment['visit'] === true) {
            $this->callPatientViaHttp($existingAppointment['id']);
        }

        // Parsuj datę i czas
        $appointmentTime = $appointment['appointment_time'] ?? null;
        $appointmentDate = null;
        $time = null;

        if ($appointmentTime) {
            // Format: "2025-09-11 09:00"
            $parts = explode(' ', $appointmentTime);
            if (count($parts) >= 2) {
                $appointmentDate = $parts[0];
                $time = $parts[1];
            }
        }

        // Użyj UPDATE lub INSERT zamiast INSERT OR REPLACE
        if ($existingAppointment) {
            // Aktualizuj istniejącą wizytę
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET doctor_id = ?, patient_name = ?, appointment_time = ?, appointment_duration = ?,
                    appointment_date = ?, status = ?, phone_number = ?, is_confirmed = ?,
                    is_patient_present = ?, status_tag = ?, locked = ?, visit = ?, is_sms_sent = ?
                WHERE external_id = ? -- UWAGA: Kolumna client_id została usunięta z queue_appointments
            ");

            return $stmt->execute([
                $appointment['doctor_id'] ?? null,
                $appointment['patient_name'] ?? '',
                $time,
                $appointment['appointment_duration'] ?? 20,
                $appointmentDate,
                $finalStatus,
                $appointment['mobile'] ?? null,
                $finalIsConfirmed,
                $finalIsPatientPresent,
                $appointment['statusTag'] ?? null,
                $finalLocked,
                $finalVisit,
                $appointment['is_sms_sent'] ?? 0,
                $appointment['id'],
                $appointment['client_id'] ?? null
            ]);
        } else {
            // Dodaj nową wizytę
            $stmt = $this->db->prepare("
                INSERT INTO queue_appointments
                (external_id, doctor_id, client_id, patient_name, appointment_time, appointment_duration, appointment_date, status, phone_number, is_confirmed, is_patient_present, status_tag, created_at, tracking_code, locked, visit, is_sms_sent, is_completed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), ?, ?, ?, ?, ?)
            ");

            return $stmt->execute([
                $appointment['id'] ?? null,
                $appointment['doctor_id'] ?? null,
                $appointment['client_id'] ?? null,
                $appointment['patient_name'] ?? '',
                $time,
                $appointment['appointment_duration'] ?? 20,
                $appointmentDate,
                $finalStatus,
                $appointment['mobile'] ?? null,
                $finalIsConfirmed,
                $finalIsPatientPresent,
                $appointment['statusTag'] ?? null,
                $trackingCode,
                $finalLocked,
                $finalVisit,
                $appointment['is_sms_sent'] ?? 0,
                $finalStatus === 'closed' ? 1 : 0
            ]);
        }
    }

    /**
     * Czyści duplikaty wizyt - zachowuje tylko najnowsze dla każdego external_id
     */
    private function cleanDuplicateAppointments($syncCode) {
        try {
            // Znajdź duplikaty wizyt (te same external_id i client_id)
            $stmt = $this->db->prepare("
                SELECT external_id, COUNT(*) as count, GROUP_CONCAT(id) as ids
                FROM queue_appointments 
                WHERE external_id IS NOT NULL -- UWAGA: Kolumna client_id została usunięta z queue_appointments
                GROUP BY external_id 
                HAVING COUNT(*) > 1
            ");
            $stmt->execute([$syncCode]);
            $duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($duplicates as $duplicate) {
                $externalId = $duplicate['external_id'];
                $ids = explode(',', $duplicate['ids']);

                // Zachowaj tylko najnowszy rekord (najwyższe ID)
                $keepId = max($ids);
                $deleteIds = array_diff($ids, [$keepId]);

                if (!empty($deleteIds)) {
                    $placeholders = str_repeat('?,', count($deleteIds) - 1) . '?';
                    $deleteStmt = $this->db->prepare("
                        DELETE FROM queue_appointments 
                        WHERE id IN ($placeholders)
                    ");
                    $deleteStmt->execute($deleteIds);

                    error_log("[V2API] Usunięto duplikaty wizyt dla external_id {$externalId}: " . implode(', ', $deleteIds));
                }
            }
        } catch (Exception $e) {
            error_log("[V2API] Błąd czyszczenia duplikatów: " . $e->getMessage());
        }
    }

    /**
     * Zamyka wszystkie inne wizyty lekarza (status called -> closed)
     */
    private function closeOtherAppointmentsForDoctor($doctorId, $currentAppointmentId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now', 'localtime'), is_completed = 1
                WHERE doctor_id = ? AND status = 'current' AND external_id != ? -- UWAGA: Kolumna client_id została usunięta
            ");
            $stmt->execute([$doctorId, $currentAppointmentId]);

            error_log("[V2API] Closed other appointments for doctor {$doctorId}, except appointment {$currentAppointmentId}");
        } catch (Exception $e) {
            error_log("[V2API] Error closing other appointments: " . $e->getMessage());
        }
    }

    /**
     * Wywołuje pacjenta przez HTTP request - USUNIĘTE (niepotrzebne)
     */
    private function callPatientViaHttp($appointmentId) {
        // Metoda usunięta - niepotrzebne wywołania do admina
        return true;
    }

    /**
     * Zamyka aktualną wizytę
     */
    private function closeCurrentAppointment($appointmentId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now', 'localtime'), is_patient_present = 0, is_completed = 1
                WHERE id = ?
            ");

            $result = $stmt->execute([$appointmentId]);
            error_log("[V2API] Zamknięcie wizyty - ID: {$appointmentId}, Wynik: " . ($result ? 'sukces' : 'błąd'));

            return $result;
        } catch (Exception $e) {
            error_log("[V2API] Błąd zamykania wizyty: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Pobieranie wizyt dla PWA
     */
    public function getAppointments($doctorId, $date = null) {
        try {
            if (!$doctorId) {
                return $this->error('Brak ID lekarza', 400);
            }

            $date = $date ?: date('Y-m-d');

            $stmt = $this->db->prepare("
                SELECT qa.*, 
                       (qd.first_name || ' ' || qd.last_name) as doctor_name, 
                       u.username as client_name, -- UWAGA: Kolumna company_name została usunięta z users
                       qa.phone_number as mobile,
                       qa.is_confirmed as res_confirmed
                FROM queue_appointments qa
                LEFT JOIN queue_doctors qd ON qa.doctor_id = qd.id
                LEFT JOIN users u ON 1=1 -- UWAGA: Kolumna client_id została usunięta z queue_appointments
                WHERE qa.doctor_id = ? AND qa.appointment_date = ?
                ORDER BY qa.appointment_time ASC
            ");

            $stmt->execute([$doctorId, $date]);
            $appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Filtruj statusy na podstawie ustawień
            $appointments = StatusFilter::filterAppointments($appointments, API_STATUS_SETTINGS);
            StatusFilter::logFilteringInfo(API_STATUS_SETTINGS, count($appointments));

            return $this->success([
                'appointments' => $appointments,
                'date' => $date,
                'doctor_id' => $doctorId
            ]);
        } catch (Exception $e) {
            return $this->error('Błąd podczas pobierania wizyt: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Aktualizacja wizyt z PWA
     */
    public function updateAppointment($appointmentId, $action) {
        try {
            if (!$appointmentId || !$action) {
                return $this->error('Brak wymaganych parametrów', 400);
            }

            $validActions = ['call-next', 'skip', 'navigate-previous', 'navigate-next'];
            if (!in_array($action, $validActions)) {
                return $this->error('Nieprawidłowa akcja', 400);
            }

            $appointment = $this->getAppointmentById($appointmentId);
            if (!$appointment) {
                return $this->error('Wizyta nie została znaleziona', 404);
            }

            $newStatus = $this->determineNewStatus($action, $appointment['status']);

            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = ?
                WHERE id = ?
            ");

            $result = $stmt->execute([$newStatus, $appointmentId]);

            if ($result) {
                // Oznaczenie zmian w cache
                $this->cacheManager->markChanges('appointments');

                return $this->success([
                    'appointment_id' => $appointmentId,
                    'action' => $action,
                    'new_status' => $newStatus,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            return $this->error('Nie udało się zaktualizować wizyty', 500);
        } catch (Exception $e) {
            return $this->error('Błąd podczas aktualizacji wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Wywołanie konkretnej wizyty
     */
    public function callAppointment($appointmentId) {
        try {
            if (!$appointmentId) {
                return $this->error('Brak ID wizyty', 400);
            }

            $appointment = $this->getAppointmentById($appointmentId);
            if (!$appointment) {
                return $this->error('Wizyta nie została znaleziona', 404);
            }

            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'called'
                WHERE id = ?
            ");

            $result = $stmt->execute([$appointmentId]);

            if ($result) {
                // Oznaczenie zmian w cache
                $this->cacheManager->markChanges('appointments');

                return $this->success([
                    'appointment_id' => $appointmentId,
                    'status' => 'called',
                    'patient_name' => $appointment['patient_name'],
                    'appointment_time' => $appointment['appointment_time'],
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            return $this->error('Nie udało się wywołać wizyty', 500);
        } catch (Exception $e) {
            return $this->error('Błąd podczas wywołania wizyty: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Status kolejki dla wyświetlaczy
     */
    public function getQueueStatus($displayId) {
        try {
            if (!$displayId) {
                return $this->error('Brak ID wyświetlacza', 400);
            }

            // Pobierz datę z parametrów GET (domyślnie dzisiaj)
            $date = $_GET['date'] ?? date('Y-m-d');

            // Pobierz lekarzy z ich wizytami - filtrowanie względem wyświetlacza
            // Lekarze muszą być przypisani do tego wyświetlacza lub nie mieć przypisanego żadnego
            $stmt = $this->db->prepare("
                SELECT qd.*, qr.name as room_name, null as room_number -- UWAGA: Kolumna room_number została usunięta z queue_rooms
                FROM queue_doctors qd
                LEFT JOIN queue_rooms qr ON qd.default_room_id = qr.id
                WHERE qd.active = 1
                AND (qd.display_id IS NULL OR qd.display_id = ?) -- Filtrowanie względem wyświetlacza
                ORDER BY qr.name, qd.first_name, qd.last_name -- UWAGA: Kolumna room_number została usunięta
            ");

            $stmt->execute([$displayId]); // Dodanie parametru displayId
            $doctors = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $doctorsData = [];
            foreach ($doctors as $doctor) {
                // Pobierz aktualną wizytę dla wybranej daty
                $stmt = $this->db->prepare("
                    SELECT * FROM queue_appointments
                    WHERE doctor_id = ? AND status = 'current'
                    AND date(appointment_date) = ?
                    ORDER BY id DESC LIMIT 1
                ");
                $stmt->execute([$doctor['id'], $date]);
                $current = $stmt->fetch(PDO::FETCH_ASSOC);

                // Pobierz wizyty oczekujące dla wybranej daty
                $stmt = $this->db->prepare("
                    SELECT * FROM queue_appointments
                    WHERE doctor_id = ? AND status = 'waiting'
                    AND date(appointment_date) = ?
                    ORDER BY appointment_time ASC LIMIT 3
                ");
                $stmt->execute([$doctor['id'], $date]);
                $waiting = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Upewnij się że ścieżka do zdjęcia ma poprawny format
                $photoUrl = $doctor['photo_url'];
                if (!empty($photoUrl)) {
                    // Upewnij się że ścieżka do zdjęcia zaczyna się od /uploads/
                    if (!str_starts_with($photoUrl, '/uploads/')) {
                        // Jeśli ścieżka zaczyna się od uploads/ (bez leading slash), dodaj slash na początku
                        if (str_starts_with($photoUrl, 'uploads/')) {
                            $photoUrl = '/' . $photoUrl;
                        }
                        // Jeśli ścieżka nie zawiera uploads/ w ogóle, dodaj pełną ścieżkę
                        else if (!str_contains($photoUrl, 'uploads/')) {
                            $photoUrl = '/uploads/' . $photoUrl;
                        }
                    }
                }

                $doctorsData[] = [
                    'id' => $doctor['id'],
                    'name' => $doctor['first_name'] . ' ' . $doctor['last_name'],
                    'specialization' => $doctor['specialization'],
                    'photo_url' => $photoUrl,
                    'room' => [
                        'id' => $doctor['default_room_id'],
                        'name' => $doctor['room_name'],
                        'room_number' => null // UWAGA: Kolumna room_number została usunięta z queue_rooms
                    ],
                    'current' => $current ? [
                        'id' => $current['id'],
                        'patient_name' => $current['patient_name'],
                        'appointment_time' => $current['appointment_time'],
                        'appointment_date' => $current['appointment_date']
                    ] : null,
                    'waiting' => array_map(function ($appointment) {
                        return [
                            'id' => $appointment['id'],
                            'patient_name' => $appointment['patient_name'],
                            'appointment_time' => $appointment['appointment_time'],
                            'appointment_date' => $appointment['appointment_date']
                        ];
                    }, $waiting)
                ];
            }

            return $this->success([
                'display_id' => $displayId,
                'doctors' => $doctorsData,
                'total' => count($doctorsData),
                'date' => $date,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            return $this->error('Błąd podczas pobierania statusu kolejki: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Sprawdzenie zmian w cache - obsługuje wiele typów encji
     * Zwraca timestamp ostatniej zmiany dla każdego typu encji
     * Obsługuje zarówno pojedynczy typ jak i wiele typów oddzielonych przecinkami
     */
    public function checkChanges($entityType) {
        try {
            if (!$entityType) {
                return $this->error('Brak typu encji', 400);
            }

            // Sprawdź czy to wiele typów encji oddzielonych przecinkami
            $entityTypes = explode(',', $entityType);
            $entityTypes = array_map('trim', $entityTypes); // Usuń białe znaki
            $entityTypes = array_filter($entityTypes); // Usuń puste elementy

            if (empty($entityTypes)) {
                return $this->error('Brak prawidłowych typów encji', 400);
            }

            $results = [];

            // Pobierz timestamp dla każdego typu encji
            foreach ($entityTypes as $type) {
                $timestamp = $this->cacheManager->getEntityTimestamp($type);
                $results[$type] = [
                    'entityType' => $type,
                    'lastUpdate' => $timestamp,
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            }

            // Jeśli był tylko jeden typ encji, zwróć w starym formacie dla kompatybilności
            if (count($entityTypes) === 1) {
                return $this->success($results[$entityTypes[0]]);
            }

            // Dla wielu typów encji zwróć tablicę wyników
            return $this->success([
                'entities' => $results,
                'count' => count($results),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            return $this->error('Błąd podczas sprawdzania zmian: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Pobranie wizyty po ID
     */
    private function getAppointmentById($appointmentId) {
        $stmt = $this->db->prepare("
            SELECT qa.*, qd.first_name, qd.last_name, qd.specialization, u.username as client_name
            FROM queue_appointments qa
            LEFT JOIN queue_doctors qd ON qa.doctor_id = qd.id
            LEFT JOIN users u ON 1=1 -- UWAGA: Kolumny client_id i company_name zostały usunięte
            WHERE qa.id = ?
        ");

        $stmt->execute([$appointmentId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Określenie nowego statusu na podstawie akcji
     */
    private function determineNewStatus($action, $currentStatus) {
        switch ($action) {
            case 'call-next':
                return 'called';
            case 'skip':
                return 'skipped';
            case 'navigate-previous':
                return 'previous';
            case 'navigate-next':
                return 'next';
            default:
                return $currentStatus;
        }
    }

    /**
     * Generuje unikalny 16-znakowy kod śledzenia
     * @return string Unikalny kod śledzenia
     */
    private function generateUniqueTrackingCode() {
        do {
            // Generuj kod: 4 cyfry + 12 znaków alfanumerycznych
            $code = sprintf('%04d%s', rand(1000, 9999), substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 12));

            // Sprawdź czy kod już istnieje
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM queue_appointments WHERE tracking_code = ?");
            $stmt->execute([$code]);
            $exists = $stmt->fetchColumn() > 0;
        } while ($exists);

        return $code;
    }

    /**
     * Pobierz istniejącą wizytę po external_id i client_id
     */
    private function getExistingAppointment($externalId) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, status, visit FROM queue_appointments
                WHERE external_id = ? -- UWAGA: Kolumna client_id została usunięta z queue_appointments
            ");
            $stmt->execute([$externalId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("[V2API] Error getting existing appointment: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Zamknij inne aktywne wizyty dla tego lekarza przed wywołaniem nowej
     */
    private function closeOtherActiveAppointmentsForDoctor($doctorId, $excludeAppointmentId) {
        try {
            error_log("[V2API] Zamykam inne aktywne wizyty dla lekarza $doctorId, wykluczając $excludeAppointmentId");

            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now', 'localtime'), is_patient_present = 0, is_completed = 1
                WHERE doctor_id = ?
                -- UWAGA: Kolumna client_id została usunięta z queue_appointments
                AND external_id != ?
                AND status IN ('called', 'current')
            ");

            $result = $stmt->execute([$doctorId, $excludeAppointmentId]);
            $affectedRows = $stmt->rowCount();

            if ($affectedRows > 0) {
                error_log("[V2API] Zamknięto $affectedRows aktywnych wizyt dla lekarza $doctorId");
            }

            return $result;
        } catch (Exception $e) {
            error_log("[V2API] Błąd zamykania aktywnych wizyt: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Pobiera wszystkie istniejące wizyty z bazy danych dla dni importu
     */
    private function getExistingAppointmentsForDays($days) { // UWAGA: Usunięto parametr syncCode
        try {
            $dates = [];
            foreach ($days as $day) {
                $dates[] = $day['date'];
            }

            if (empty($dates)) {
                return [];
            }

            $placeholders = str_repeat('?,', count($dates) - 1) . '?';
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments 
                WHERE appointment_date IN ($placeholders) -- UWAGA: Kolumna client_id została usunięta z queue_appointments
            ");

            // UWAGA: Usunięto syncCode z parametrów
            $stmt->execute($dates);
            $appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Konwertuj na tablicę asocjacyjną po external_id
            $result = [];
            foreach ($appointments as $appointment) {
                $result[$appointment['external_id']] = $appointment;
            }

            return $result;
        } catch (Exception $e) {
            error_log("[V2API] Error getting existing appointments: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Określa statusy wizyt w pamięci na podstawie logiki visit/locked
     */
    private function determineAppointmentStatuses(&$appointments, $visitsWithVisitTrue) {
        foreach ($appointments as &$appointment) {
            $appointment['final_status'] = 'waiting';
            $appointment['final_is_patient_present'] = $appointment['is_patient_present'];
            $appointment['final_visit'] = $appointment['visit'] ? 1 : 0;
            $appointment['final_locked'] = $appointment['locked'] ? 1 : 0;
            $appointment['final_is_confirmed'] = $appointment['res_confirmed'] ? 1 : 0;
            $appointment['final_is_completed'] = 0;

            // Logika visit: visit=true oznacza że wizyta się odbyła
            if ($appointment['visit'] === true) {
                $isNewestVisit = $this->isNewestVisitWithVisitTrueInMemory($appointment, $visitsWithVisitTrue);

                if ($isNewestVisit) {
                    // To najnowsza wizyta z visit=true - ustaw jako current
                    $appointment['final_status'] = 'current';
                    $appointment['final_is_patient_present'] = 1;
                } else {
                    // To starsza wizyta z visit=true - zamknij ją
                    $appointment['final_status'] = 'closed';
                    $appointment['final_is_patient_present'] = 1;
                    $appointment['final_is_completed'] = 1;
                }
            }

            // Logika locked: true = status closed (ale tylko jeśli visit nie ustawił current)
            if ($appointment['locked'] === true && $appointment['final_status'] !== 'current') {
                $appointment['final_status'] = 'closed';
                $appointment['final_is_completed'] = 1;
            }
        }
    }

    /**
     * Przygotowuje zmiany między nowymi a istniejącymi wizytami
     */
    private function prepareAppointmentChanges($newAppointments, $existingAppointments) {
        $changes = [
            'insert' => [],
            'update' => [],
            'delete' => []
        ];

        foreach ($newAppointments as $newAppointment) {
            $externalId = $newAppointment['id'];

            if (isset($existingAppointments[$externalId])) {
                $existing = $existingAppointments[$externalId];

                // Sprawdź czy są różnice
                $hasChanges = false;
                $updateData = [];

                $fieldsToCheck = [
                    'patient_name' => 'patient_name',
                    'appointment_time' => 'appointment_time',
                    'appointment_duration' => 'appointment_duration',
                    'phone_number' => 'mobile',
                    'status' => 'final_status',
                    'is_patient_present' => 'final_is_patient_present',
                    'visit' => 'final_visit',
                    'locked' => 'final_locked',
                    'is_confirmed' => 'final_is_confirmed',
                    'status_tag' => 'statusTag',
                    'is_sms_sent' => 'is_sms_sent',
                    'is_completed' => 'final_is_completed'
                ];

                foreach ($fieldsToCheck as $dbField => $newField) {
                    $newValue = $newAppointment[$newField];
                    $existingValue = $existing[$dbField];

                    // Konwertuj wartości dla porównania
                    if ($dbField === 'visit' || $dbField === 'locked' || $dbField === 'is_confirmed' || $dbField === 'is_patient_present') {
                        $newValue = (int)$newValue;
                        $existingValue = (int)$existingValue;
                    }

                    if ($newValue != $existingValue) {
                        $hasChanges = true;
                        $updateData[$dbField] = $newValue;
                    }
                }

                if ($hasChanges) {
                    $updateData['id'] = $existing['id'];
                    $updateData['external_id'] = $externalId;
                    // Zachowaj istniejący tracking_code
                    if (!empty($existing['tracking_code'])) {
                        $updateData['tracking_code'] = $existing['tracking_code'];
                    }
                    $changes['update'][] = $updateData;
                }
            } else {
                // Nowa wizyta do wstawienia
                $insertData = [
                    'external_id' => $externalId,
                    'doctor_id' => $newAppointment['doctor_id'],
                    'client_id' => $newAppointment['client_id'],
                    'patient_name' => $newAppointment['patient_name'],
                    'appointment_time' => $newAppointment['appointment_time'],
                    'appointment_duration' => $newAppointment['appointment_duration'],
                    'phone_number' => $newAppointment['mobile'],
                    'status' => $newAppointment['final_status'],
                    'is_patient_present' => $newAppointment['final_is_patient_present'],
                    'visit' => $newAppointment['final_visit'],
                    'locked' => $newAppointment['final_locked'],
                    'is_confirmed' => $newAppointment['final_is_confirmed'],
                    'status_tag' => $newAppointment['statusTag'],
                    'appointment_date' => date('Y-m-d', strtotime($newAppointment['appointment_time'])),
                    'tracking_code' => $this->generateUniqueTrackingCode(),
                    'is_sms_sent' => $newAppointment['is_sms_sent'] ?? 0,
                    'is_completed' => $newAppointment['final_status'] === 'closed' ? 1 : 0
                ];
                $changes['insert'][] = $insertData;
            }
        }

        return $changes;
    }

    /**
     * Zastosowuje zmiany w jednej transakcji
     */
    private function applyAppointmentChanges($changes) {
        try {
            $this->db->beginTransaction();

            $importedCount = 0;

            // Wstaw nowe wizyty
            if (!empty($changes['insert'])) {
                $stmt = $this->db->prepare("
                    INSERT INTO queue_appointments
                    (external_id, doctor_id, client_id, patient_name, appointment_time, appointment_duration,
                     phone_number, status, is_patient_present, visit, locked, is_confirmed, status_tag, appointment_date, tracking_code, created_at, is_sms_sent, is_completed)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), ?, ?)
                ");

                foreach ($changes['insert'] as $data) {
                    $stmt->execute([
                        $data['external_id'],
                        $data['doctor_id'],
                        $data['client_id'],
                        $data['patient_name'],
                        $data['appointment_time'],
                        $data['appointment_duration'],
                        $data['phone_number'],
                        $data['status'],
                        $data['is_patient_present'],
                        $data['visit'],
                        $data['locked'],
                        $data['is_confirmed'],
                        $data['status_tag'],
                        $data['appointment_date'],
                        $data['tracking_code'],
                        $data['is_sms_sent'] ?? 0,
                        $data['is_completed'] ?? 0
                    ]);
                    $importedCount++;
                }
            }

            // Zaktualizuj istniejące wizyty
            if (!empty($changes['update'])) {
                foreach ($changes['update'] as $data) {
                    $id = $data['id'];
                    unset($data['id']);
                    unset($data['external_id']);

                    $fields = array_keys($data);
                    $placeholders = str_repeat('?,', count($fields) - 1) . '?';
                    $setClause = implode(' = ?, ', $fields) . ' = ?';

                    $stmt = $this->db->prepare("
                        UPDATE queue_appointments 
                        SET $setClause
                        WHERE id = ?
                    ");

                    $values = array_values($data);
                    $values[] = $id;
                    $stmt->execute($values);
                    $importedCount++;
                }
            }

            $this->db->commit();
            return $importedCount;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log("[V2API] Error applying appointment changes: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Dodaje tracking codes dla istniejących wizyt bez tracking codes
     */
    private function addMissingTrackingCodes() { // UWAGA: Usunięto parametr syncCode
        try {
            // Znajdź wizyty bez tracking codes
            $stmt = $this->db->prepare("
                SELECT id FROM queue_appointments 
                WHERE tracking_code IS NULL -- UWAGA: Kolumna client_id została usunięta z queue_appointments
            ");
            $stmt->execute(); // UWAGA: Usunięto parametr syncCode
            $appointmentsWithoutTracking = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($appointmentsWithoutTracking)) {
                return;
            }

            // Dodaj tracking codes
            $stmt = $this->db->prepare("
                UPDATE queue_appointments 
                SET tracking_code = ? 
                WHERE id = ?
            ");

            foreach ($appointmentsWithoutTracking as $appointment) {
                $trackingCode = $this->generateUniqueTrackingCode();
                $stmt->execute([$trackingCode, $appointment['id']]);
            }

            error_log("[V2API] Added tracking codes for " . count($appointmentsWithoutTracking) . " appointments");
        } catch (Exception $e) {
            error_log("[V2API] Error adding missing tracking codes: " . $e->getMessage());
        }
    }

    /**
     * Sprawdza czy dana wizyta jest najnowszą z visit=true dla danego lekarza (w pamięci)
     */
    private function isNewestVisitWithVisitTrueInMemory($currentAppointment, $visitsWithVisitTrue) {
        try {
            $doctorId = $currentAppointment['doctor_id'];
            $currentAppointmentTime = $currentAppointment['appointment_time'];

            // Sprawdź czy lekarz ma wizyty z visit=true
            if (!isset($visitsWithVisitTrue[$doctorId]) || empty($visitsWithVisitTrue[$doctorId])) {
                return true; // Jeśli to pierwsza wizyta z visit=true dla tego lekarza
            }

            // Sortuj wizyty po czasie (najnowsza na początku)
            $doctorVisits = $visitsWithVisitTrue[$doctorId];
            usort($doctorVisits, function ($a, $b) {
                return strcmp($b['appointment_time'], $a['appointment_time']); // DESC
            });

            // Sprawdź czy aktualna wizyta ma najpóźniejszy czas
            $latestTime = $doctorVisits[0]['appointment_time'];

            // Porównaj czasy (format YYYY-MM-DD HH:MM)
            if ($currentAppointmentTime >= $latestTime) {
                return true; // To najnowsza wizyta
            }

            return false; // To starsza wizyta

        } catch (Exception $e) {
            error_log("[V2API] Błąd sprawdzania najnowszej wizyty w pamięci: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Upewnij się, że kolumna status_tag istnieje w tabeli queue_appointments
     */
    private function ensureStatusTagColumnExists() {
        try {
            // Sprawdź czy kolumna status_tag istnieje
            $stmt = $this->db->prepare("PRAGMA table_info(queue_appointments)");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $hasStatusTagColumn = false;
            foreach ($columns as $column) {
                if ($column['name'] === 'status_tag') {
                    $hasStatusTagColumn = true;
                    break;
                }
            }

            // Dodaj kolumnę status_tag jeśli nie istnieje
            if (!$hasStatusTagColumn) {
                $this->db->exec("ALTER TABLE queue_appointments ADD COLUMN status_tag TEXT");
                error_log("[V2API] Dodano kolumnę status_tag do tabeli queue_appointments");
            }
        } catch (Exception $e) {
            error_log("[V2API] Błąd sprawdzania/dodawania kolumny status_tag: " . $e->getMessage());
        }
    }

    /**
     * Pobierz ustawienia wyświetlacza
     */
    public function getDisplaySettings($displayId) {
        try {
            if (!$displayId) {
                return $this->error('Brak ID wyświetlacza', 400);
            }

            // Pobierz ustawienia wyświetlacza z tabeli client_displays
            $stmt = $this->db->prepare("SELECT volume, show_subtitles, subtitle_font_size FROM client_displays WHERE id = ?");
            $stmt->execute([$displayId]);
            $display = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$display) {
                return $this->error('Wyświetlacz nie został znaleziony', 404);
            }

            $settings = [
                'display_volume' => (int)($display['volume'] ?? 100),
                'show_subtitles' => (bool)($display['show_subtitles'] ?? true),
                'subtitle_font_size' => (int)($display['subtitle_font_size'] ?? 16)
            ];

            return $this->success($settings);
        } catch (Exception $e) {
            return $this->error('Błąd podczas pobierania ustawień wyświetlacza: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Pobierz ustawienie z bazy danych
     */
    private function getSetting($key, $defaultValue = null) {
        $stmt = $this->db->prepare("SELECT value FROM settings WHERE key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result ? $result['value'] : $defaultValue;
    }
}
