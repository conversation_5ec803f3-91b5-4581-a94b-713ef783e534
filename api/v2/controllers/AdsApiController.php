<?php

class AdsApiController extends ApiController {

    /**
     * Pobierz reklamy dla klienta z algorytmem sprawiedliwego losowania
     */
    public function getAds($clientId) {
        try {
            // Pobierz 5 materiałów z najmniejszą liczbą wyświetleń
            $stmt = $this->db->prepare("
                SELECT c.id, c.name, c.media_type, c.media_url, c.youtube_id,
                       c.duration, 0.0001 as rate_per_second, c.description,
                       COALESCE(c.ads_views, 0) as views_count
                FROM ads c
                WHERE c.approval_status = 'approved'
                ORDER BY COALESCE(c.ads_views, 0) ASC, c.id ASC
                LIMIT 5
            ");

            $stmt->execute();
            $topAds = $stmt->fetchAll();

            if (empty($topAds)) {
                $this->success([
                    'ads' => [],
                    'message' => 'No ads available'
                ]);
                return;
            }

            // Losuj spośród 5 materiałów z najmniejszą liczbą wyświetleń
            $randomIndex = array_rand($topAds);
            $selectedAd = $topAds[$randomIndex];

            // Loguj informacje o wyborze
            $this->log(sprintf(
                'Fair ad selection: Selected ad ID %d (views: %d) from %d top ads with lowest views',
                $selectedAd['id'],
                $selectedAd['views_count'],
                count($topAds)
            ), 'INFO');

            // Przygotuj URL mediów
            if ($selectedAd['media_type'] === 'image' || $selectedAd['media_type'] === 'video') {
                $selectedAd['media_url'] = $this->getFullMediaUrl($selectedAd['media_url']);
            }

            // Usuń pole views_count z odpowiedzi (tylko do wewnętrznego użytku)
            unset($selectedAd['views_count']);

            $this->success([
                'ads' => [$selectedAd],
                'client_id' => $clientId,
                'timestamp' => date('c')
            ]);
        } catch (Exception $e) {
            $this->log('Error getting ads: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to get ads', 500);
        }
    }

    /**
     * Zapisz wyświetlenie reklamy i zwróć kolejny materiał do odtworzenia
     */
    public function recordView() {
        try {
            $data = $this->getRequestData();
            $this->validateRequired($data, ['ads_id']);

            $adsId = $data['ads_id'];
            $displayId = $data['display_id'] ?? null;

            // Sprawdź czy reklama istnieje
            $stmt = $this->db->prepare("
                SELECT c.*
                FROM ads c
                WHERE c.id = ?
            ");
            $stmt->execute([$adsId]);
            $campaign = $stmt->fetch();

            if (!$campaign) {
                $this->error('Ad not found', 404);
            }

            // Zwiększ licznik wyświetleń reklamy
            $stmt = $this->db->prepare("
                UPDATE ads
                SET ads_views = COALESCE(ads_views, 0) + 1
                WHERE id = ?
            ");
            $stmt->execute([$adsId]);

            // Pobierz aktualną liczbę wyświetleń
            $stmt = $this->db->prepare("
                SELECT ads_views FROM ads WHERE id = ?
            ");
            $stmt->execute([$adsId]);
            $result = $stmt->fetch();
            $totalViews = $result['ads_views'] ?? 0;

            // Pobierz kolejny materiał do odtworzenia
            $this->log(sprintf(
                'Record view: Getting next ad after recording view for ad ID %d',
                $adsId
            ), 'INFO');
            $nextAd = $this->getNextAd($adsId);
            
            if ($nextAd) {
                $this->log(sprintf(
                    'Record view: Next ad selected - ID: %d, Type: %s, URL: %s',
                    $nextAd['id'],
                    $nextAd['media_type'],
                    $nextAd['media_url'] ?? 'N/A'
                ), 'INFO');
            } else {
                $this->log(sprintf(
                    'Record view: No next ad available after recording view for ad ID %d',
                    $adsId
                ), 'WARNING');
            }

            $response = [
                'view_recorded' => true,
                'ads_id' => $adsId,
                'total_views' => $totalViews,
                'next_ad' => $nextAd
            ];

            $this->success($response, 'View recorded successfully');
        } catch (Exception $e) {
            $this->log('Error recording view: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to record view', 500);
        }
    }

    /**
     * Pobierz kolejny materiał do odtworzenia (z wykluczeniem aktualnego)
     */
    private function getNextAd($excludeId = null) {
        try {
            // Pobierz 5 materiałów z najmniejszą liczbą wyświetleń (z wykluczeniem aktualnego)
            $excludeClause = $excludeId ? "AND c.id != ?" : "";
            $params = $excludeId ? [$excludeId] : [];
            
            $stmt = $this->db->prepare("
                SELECT c.id, c.name, c.media_type, c.media_url, c.youtube_id,
                       c.duration, c.description, COALESCE(c.ads_views, 0) as views_count
                FROM ads c
                WHERE c.approval_status = 'approved' $excludeClause
                ORDER BY COALESCE(c.ads_views, 0) ASC, c.id ASC
                LIMIT 5
            ");

            $stmt->execute($params);
            $topAds = $stmt->fetchAll();

            if (empty($topAds)) {
                $this->log(sprintf(
                    'Next ad selection: No ads available (excluding ID %s)',
                    $excludeId ?? 'none'
                ), 'WARNING');
                return null;
            }

            // Losuj spośród 5 materiałów z najmniejszą liczbą wyświetleń
            $randomIndex = array_rand($topAds);
            $selectedAd = $topAds[$randomIndex];

            // Loguj informacje o wyborze
            $this->log(sprintf(
                'Next ad selection: Selected ad ID %d (views: %d) from %d top ads with lowest views',
                $selectedAd['id'],
                $selectedAd['views_count'],
                count($topAds)
            ), 'INFO');

            // Przygotuj URL mediów
            if ($selectedAd['media_type'] === 'image' || $selectedAd['media_type'] === 'video') {
                $selectedAd['media_url'] = $this->getFullMediaUrl($selectedAd['media_url']);
            }

            // Sprawdź dostępność pliku VTT dla video
            if ($selectedAd['media_type'] === 'video' && !empty($selectedAd['media_url'])) {
                $selectedAd['subtitles_url'] = $this->getSubtitlesUrl($selectedAd['media_url']);
                $selectedAd['has_subtitles'] = $this->checkSubtitlesExist($selectedAd['subtitles_url']);
            } else {
                $selectedAd['subtitles_url'] = null;
                $selectedAd['has_subtitles'] = false;
            }

            // Usuń pole views_count z odpowiedzi (tylko do wewnętrznego użytku)
            unset($selectedAd['views_count']);

            return $selectedAd;
        } catch (Exception $e) {
            $this->log('Error getting next ad: ' . $e->getMessage(), 'ERROR');
            return null;
        }
    }

    /**
     * Generuj URL do pliku napisów VTT na podstawie URL video
     */
    private function getSubtitlesUrl($videoUrl) {
        // Zamień rozszerzenie video na .vtt
        $pathInfo = pathinfo($videoUrl);
        if (isset($pathInfo['dirname']) && isset($pathInfo['filename'])) {
            return $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.vtt';
        }
        return null;
    }

    /**
     * Sprawdź czy plik napisów VTT istnieje
     */
    private function checkSubtitlesExist($subtitlesUrl) {
        if (!$subtitlesUrl) {
            return false;
        }

        // Konwertuj URL na ścieżkę systemową
        $filePath = $_SERVER['DOCUMENT_ROOT'] . $subtitlesUrl;

        // Sprawdź czy plik istnieje
        return file_exists($filePath) && is_readable($filePath);
    }



    /**
     * Pobierz pełny URL do pliku multimedialnego
     */
    private function getFullMediaUrl($mediaUrl) {
        if (strpos($mediaUrl, 'http') === 0) {
            return $mediaUrl; // Już pełny URL
        }

        // Względny URL - dodaj bazowy URL (uproszczone bez Config)
        return '/' . ltrim($mediaUrl, '/');
    }
}
