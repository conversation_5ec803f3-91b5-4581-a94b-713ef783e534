<?php
require_once __DIR__ . '/../core/ApiController.php';
require_once __DIR__ . '/../core/EnhancedCacheManager.php';
require_once __DIR__ . '/../core/CacheUpdater.php';
require_once __DIR__ . '/../core/StatusFilter.php';

/**
 * Kontroler do importu danych z systemu iGabinet
 */
class IgabinetImportController extends ApiController {
    private $cacheManager;
    private $importSettings;

    public function __construct() {
        parent::__construct();
        $this->cacheManager = new EnhancedCacheManager();
        $this->importSettings = new ImportSettings();
    }

    /**
     * Import danych z systemu iGabinet
     */
    public function importData() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);

            // Logowanie przychodzących danych
            error_log('[iGabinet] Import request received. Raw data: ' . json_encode($input));
            error_log('[iGabinet] Import request keys: ' . implode(', ', array_keys($input ?? [])));

            if (!$input) {
                return $this->error('Brak danych do importu', 400);
            }

            // Sprawdź czy import jest włączony
            if (!$this->importSettings->isImportEnabled()) {
                return $this->error('Import jest wyłączony w ustawieniach systemu', 403);
            }

            // Sprawdź czy wybrane źródło jest zgodne z tym kontrolerem
            $importSource = $this->importSettings->getImportSource();
            if ($importSource !== 'igabinet' && $importSource !== 'default') {
                return $this->error('Nieprawidłowe źródło importu dla iGabinet', 403);
            }

            $source = $input['source'] ?? 'igabinet';
            $syncCode = $input['syncCode'] ?? null;

            error_log('[iGabinet] Source: ' . $source . ', syncCode: ' . $syncCode);

            // Sprawdź czy dane są w poprawnym formacie
            if (isset($input['syncData']) && isset($input['syncData']['days'])) {
                error_log('[iGabinet] Processing iGabinet data');
                return $this->importFromIgabinet($input, $source, $syncCode);
            }

            error_log('[iGabinet] Invalid format - expected syncData');
            return $this->error('Nieprawidłowy format danych - oczekiwano syncData', 400);
        } catch (Exception $e) {
            error_log('[iGabinet] Error: ' . $e->getMessage());
            return $this->error('Błąd podczas importu: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Import danych z systemu iGabinet
     */
    private function importFromIgabinet($input, $source, $syncCode) {
        $syncData = $input['syncData'];
        $days = $syncData['days'] ?? [];

        if (empty($days)) {
            return $this->error('Brak danych dni do importu', 400);
        }

        // Zbierz unikalnych lekarzy z importu
        $externalDoctors = [];
        foreach ($days as $day) {
            if (!isset($day['doctors']) || !is_array($day['doctors'])) {
                continue;
            }

            foreach ($day['doctors'] as $doctor) {
                $doctorId = $doctor['doctorId'] ?? null;
                $doctorName = $doctor['doctorName'] ?? 'Nieznany lekarz';
                
                if ($doctorId && !isset($externalDoctors[$doctorId])) {
                    $externalDoctors[$doctorId] = [
                        'external_id' => $doctorId,
                        'name' => $doctorName
                    ];
                }
            }
        }

        // Sprawdź mapowanie lekarzy
        $validation = $this->importSettings->validateDoctorsMapping(array_values($externalDoctors), $syncCode);
        
        if (!$validation['all_mapped']) {
            // Utwórz plik z niezmapowanymi lekarzami
            $filename = $this->importSettings->createUnmappedDoctorsFile(
                $validation['unmapped_doctors'],
                $syncCode,
                $source
            );
            
            // Zapisz dane do pliku log
            $this->saveSyncLog($input, $syncCode);
            
            error_log("[iGabinet] Import odrzucony - {$validation['unmapped_count']} niezmapowanych lekarzy");
            
            return $this->error('Nie można przeprowadzić importu - znaleziono niezmapowanych lekarzy', 400, [
                'unmapped_count' => $validation['unmapped_count'],
                'unmapped_doctors_file' => $filename,
                'unmapped_doctors' => $validation['unmapped_doctors']
            ]);
        }

        // Zapisz dane do pliku log
        $this->saveSyncLog($input, $syncCode);

        $errors = [];

        try {
            // OPTYMALIZACJA 1: Pobierz wszystkie mapowania lekarzy na początku
            $doctorMappings = $this->getAllDoctorMappings($syncCode);

            // OPTYMALIZACJA 2: Pobierz wszystkie wizyty z bazy danych dla dni importu
            $existingAppointments = $this->getExistingAppointmentsForDays($days, $syncCode);

            // OPTYMALIZACJA 3: Przygotuj dane do importu w pamięci
            $appointmentsToProcess = [];

            foreach ($days as $day) {
                if (!isset($day['doctors']) || !is_array($day['doctors'])) {
                    continue;
                }

                foreach ($day['doctors'] as $doctor) {
                    if (!isset($doctor['appointments']) || !is_array($doctor['appointments'])) {
                        continue;
                    }

                    // Pobierz mapowanie lekarza z cache
                    $mappedDoctorId = $doctorMappings[$doctor['doctorId']] ?? null;

                    if (!$mappedDoctorId) {
                        // Ten błąd nie powinien wystąpić, bo już sprawdziliśmy mapowanie
                        $errors[] = "Brak mapowania dla lekarza {$doctor['doctorId']} ({$doctor['doctorName']})";
                        continue;
                    }

                    // Aktualizuj datę last_seen dla tego lekarza
                    $this->updateDoctorLastSeen($doctor['doctorId'], $syncCode);

                    foreach ($doctor['appointments'] as $appointment) {
                        // Parsuj statusTag dla tagów reservation
                        $isPatientPresent = 0;

                        if (isset($appointment['statusTag']) && $appointment['statusTag']) {
                            $statusTag = $appointment['statusTag'];
                            // Sprawdź czy to tag reservation w formacie: "tags_reservation_{appointmentId}_{tagId}"
                            if (preg_match('/^tags_reservation_\d+_(\d+)$/', $statusTag, $matches)) {
                                $tagId = (int)$matches[1];
                                // Jeśli tag ID to 4 lub 7, ustaw is_patient_present na 1
                                if ($tagId === 4 || $tagId === 7) {
                                    $isPatientPresent = 1;
                                }
                            }
                        }

                        $appointmentData = [
                            'id' => $appointment['appointmentId'] ?? null,
                            'doctor_id' => $mappedDoctorId,
                            'patient_name' => trim(($appointment['patientFirstName'] ?? '') . ' ' . ($appointment['patientLastName'] ?? '')),
                            'appointment_time' => $appointment['appointmentStart'] ?? '',
                            'appointment_duration' => $appointment['appointmentDuration'] ?? 20,
                            'appointment_date' => $day['date'],
                            'phone_number' => $appointment['mobile'] ?? null,
                            'is_confirmed' => ($appointment['resConfirmed'] ?? false) ? 1 : 0,
                            'is_patient_present' => $isPatientPresent,
                            'external_id' => $appointment['appointmentId'] ?? null,
                            // Pola usunięte z nowej struktury: visit, locked, status_tag
                            // Nowe pola: is_completed, is_sms_sent
                            'is_completed' => 0, // Domyślnie nieukończona
                            'is_sms_sent' => 0  // Domyślnie SMS nie wysłany
                        ];

                        $appointmentsToProcess[] = $appointmentData;
                    }
                }
            }

            // OPTYMALIZACJA 4: Określ statusy wizyt w pamięci (uproszczona logika bez visit/locked)
            $this->determineAppointmentStatuses($appointmentsToProcess);

            // OPTYMALIZACJA 5: Porównaj z istniejącymi wizytami i przygotuj zmiany
            $changes = $this->prepareAppointmentChanges($appointmentsToProcess, $existingAppointments);

            // OPTYMALIZACJA 6: Zaktualizuj dane w jednej transakcji
            $importedCount = $this->applyAppointmentChanges($changes);

            // Wyczyść duplikaty wizyt (zachowaj tylko najnowsze)
            $this->cleanDuplicateAppointments($syncCode);

            // Dodaj tracking codes dla istniejących wizyt bez tracking codes
            $this->addMissingTrackingCodes($syncCode);

            // Aktualizuj datę ostatniej synchronizacji
            $this->updateLastSyncDate($syncCode);

            // Wyczyść cache
            $this->cacheManager->markChanges('appointments');

            return $this->success([
                'imported' => $importedCount,
                'total_days' => count($days),
                'errors' => $errors,
                'source' => $source,
                'syncCode' => $syncCode
            ]);
        } catch (Exception $e) {
            error_log("[iGabinet] Error in optimized import: " . $e->getMessage());
            return $this->error('Błąd podczas importu: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Aktualizuj datę last_seen dla lekarza w queue_doctors
     */
    private function updateDoctorLastSeen($externalDoctorId, $syncCode) {
        try {
            // Aktualizuj datę external_last_sync (użyj lokalnego czasu)
            $stmt = $this->db->prepare("
                UPDATE queue_doctors
                SET external_last_sync = datetime('now', 'localtime')
                WHERE external_doctor_id = ?
            ");
            $result = $stmt->execute([$externalDoctorId]);

            if ($result) {
                error_log("[iGabinet] Updated external_last_sync for doctor $externalDoctorId");
            }

            return $result;
        } catch (Exception $e) {
            error_log("[iGabinet] Error updating doctor last_seen: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Pobierz wszystkie mapowania lekarzy (optymalizacja)
     */
    private function getAllDoctorMappings($syncCode) {
        try {
            // Pobierz wszystkie mapowania lekarzy z queue_doctors
            $stmt = $this->db->prepare("
                SELECT external_doctor_id, id
                FROM queue_doctors
                WHERE external_doctor_id IS NOT NULL AND external_doctor_id != ''
            ");
            $stmt->execute();
            $mappings = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Konwertuj na tablicę asocjacyjną
            $result = [];
            foreach ($mappings as $mapping) {
                $result[$mapping['external_doctor_id']] = $mapping['id'];
            }

            return $result;
        } catch (Exception $e) {
            error_log("[iGabinet] Error getting all doctor mappings: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Pobierz mapowanie lekarza z queue_doctors (stara funkcja - zachowana dla kompatybilności)
     */
    private function getMappedDoctorId($externalDoctorId, $syncCode) {
        try {
            // Pobierz mapowanie lekarza z queue_doctors
            $stmt = $this->db->prepare("
                SELECT id
                FROM queue_doctors
                WHERE external_doctor_id = ? AND external_doctor_id IS NOT NULL AND external_doctor_id != ''
            ");
            $stmt->execute([$externalDoctorId]);
            $mapping = $stmt->fetch(PDO::FETCH_ASSOC);

            return $mapping ? $mapping['id'] : null;
        } catch (Exception $e) {
            error_log("[iGabinet] Error getting mapped doctor ID: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Aktualizuj datę ostatniej synchronizacji - UWAGA: Funkcjonalność wyłączona
     * Tabela import_settings została usunięta - używamy tabeli settings
     */
    private function updateLastSyncDate($syncCode) {
        try {
            // Zapisz datę ostatniej synchronizacji w tabeli settings
            $stmt = $this->db->prepare("
                INSERT OR REPLACE INTO settings (key, value, updated_at)
                VALUES (?, ?, datetime('now'))
            ");
            $stmt->execute(['import.' . $syncCode . '.last_sync', date('c')]);
            error_log("[iGabinet] Updated last_sync for syncCode: $syncCode in settings table");
        } catch (Exception $e) {
            error_log("[iGabinet] Error updating last_sync: " . $e->getMessage());
        }
    }

    /**
     * Zapisz dane synchronizacji do pliku log
     */
    private function saveSyncLog($input, $syncCode) {
        try {
            $logDir = __DIR__ . '/../data_sync_log';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            $timestamp = date('Y-m-d_H-i-s');
            $filename = "igabinet_sync_{$syncCode}_{$timestamp}.json";
            $filepath = $logDir . '/' . $filename;

            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'syncCode' => $syncCode,
                'source' => 'igabinet',
                'data' => $input
            ];

            file_put_contents($filepath, json_encode($logData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            error_log("[iGabinet] Saved sync log: $filename");
        } catch (Exception $e) {
            error_log("[iGabinet] Error saving sync log: " . $e->getMessage());
        }
    }

    /**
     * Import pojedynczej wizyty z uproszczoną logiką (dostosowaną do nowej struktury bazy)
     */
    private function importSingleAppointment($appointment) {
        // Sprawdź czy wizyta już istnieje
        $existingAppointment = null;
        if (!empty($appointment['external_id'])) {
            $checkStmt = $this->db->prepare("
                SELECT id, tracking_code, is_confirmed, is_patient_present, status FROM queue_appointments
                WHERE external_id = ? AND doctor_id = ?
            ");
            $checkStmt->execute([
                $appointment['external_id'],
                $appointment['doctor_id'] ?? null
            ]);
            $existingAppointment = $checkStmt->fetch(PDO::FETCH_ASSOC);
        }

        // Generuj unikalny tracking_code jeśli wizyta nie istnieje
        $trackingCode = null;
        if ($existingAppointment) {
            $trackingCode = $existingAppointment['tracking_code'];
        }

        if (empty($trackingCode)) {
            $trackingCode = $this->generateUniqueTrackingCode();
        }

        // Uproszczona logika - wszystkie wizyty domyślnie są 'waiting'
        $finalStatus = 'waiting';
        $finalIsConfirmed = $appointment['is_confirmed'] ?? 0;
        $finalIsPatientPresent = $appointment['is_patient_present'] ?? 0;
        $finalIsCompleted = $appointment['is_completed'] ?? 0;
        $finalIsSmsSent = $appointment['is_sms_sent'] ?? 0;

        // Użyj UPDATE lub INSERT
        if ($existingAppointment) {
            // Aktualizuj istniejącą wizytę
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET doctor_id = ?, patient_name = ?, appointment_time = ?, appointment_duration = ?,
                    appointment_date = ?, status = ?, phone_number = ?, is_confirmed = ?,
                    is_patient_present = ?, is_completed = ?, is_sms_sent = ?
                WHERE external_id = ?
            ");

            return $stmt->execute([
                $appointment['doctor_id'] ?? null,
                $appointment['patient_name'] ?? '',
                $appointment['appointment_time'] ?? '',
                $appointment['appointment_duration'] ?? 20,
                $appointment['appointment_date'] ?? null,
                $finalStatus,
                $appointment['phone_number'] ?? null,
                $finalIsConfirmed,
                $finalIsPatientPresent,
                $finalIsCompleted,
                $finalIsSmsSent,
                $appointment['external_id']
            ]);
        } else {
            // Dodaj nową wizytę
            $stmt = $this->db->prepare("
                INSERT INTO queue_appointments
                (external_id, doctor_id, patient_name, appointment_time, appointment_duration, appointment_date,
                 status, phone_number, is_confirmed, is_patient_present, is_completed, is_sms_sent,
                 created_at, tracking_code)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), ?)
            ");

            return $stmt->execute([
                $appointment['external_id'] ?? null,
                $appointment['doctor_id'] ?? null,
                $appointment['patient_name'] ?? '',
                $appointment['appointment_time'] ?? '',
                $appointment['appointment_duration'] ?? 20,
                $appointment['appointment_date'] ?? null,
                $finalStatus,
                $appointment['phone_number'] ?? null,
                $finalIsConfirmed,
                $finalIsPatientPresent,
                $finalIsCompleted,
                $finalIsSmsSent,
                $trackingCode
            ]);
        }
    }

    /**
     * Czyści duplikaty wizyt - zachowuje tylko najnowsze dla każdego external_id
     */
    private function cleanDuplicateAppointments($syncCode) {
        try {
            // Znajdź duplikaty wizyt (te same external_id i client_id)
            $stmt = $this->db->prepare("
                SELECT external_id, COUNT(*) as count, GROUP_CONCAT(id) as ids
                FROM queue_appointments 
                WHERE external_id IS NOT NULL -- UWAGA: Kolumna client_id została usunięta z queue_appointments
                GROUP BY external_id 
                HAVING COUNT(*) > 1
            ");
            $stmt->execute([$syncCode]);
            $duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($duplicates as $duplicate) {
                $externalId = $duplicate['external_id'];
                $ids = explode(',', $duplicate['ids']);

                // Zachowaj tylko najnowszy rekord (najwyższe ID)
                $keepId = max($ids);
                $deleteIds = array_diff($ids, [$keepId]);

                if (!empty($deleteIds)) {
                    $placeholders = str_repeat('?,', count($deleteIds) - 1) . '?';
                    $deleteStmt = $this->db->prepare("
                        DELETE FROM queue_appointments 
                        WHERE id IN ($placeholders)
                    ");
                    $deleteStmt->execute($deleteIds);

                    error_log("[iGabinet] Usunięto duplikaty wizyt dla external_id {$externalId}: " . implode(', ', $deleteIds));
                }
            }
        } catch (Exception $e) {
            error_log("[iGabinet] Błąd czyszczenia duplikatów: " . $e->getMessage());
        }
    }

    /**
     * Zamyka wszystkie inne wizyty lekarza (status called -> closed)
     */
    private function closeOtherAppointmentsForDoctor($doctorId, $currentAppointmentId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed'
                WHERE doctor_id = ? AND status = 'current' AND external_id != ? -- UWAGA: Kolumna client_id została usunięta
            ");
            $stmt->execute([$doctorId, $currentAppointmentId]);

            error_log("[iGabinet] Closed other appointments for doctor {$doctorId}, except appointment {$currentAppointmentId}");
        } catch (Exception $e) {
            error_log("[iGabinet] Error closing other appointments: " . $e->getMessage());
        }
    }

    /**
     * Wywołuje pacjenta przez HTTP request - USUNIĘTE (niepotrzebne)
     */
    private function callPatientViaHttp($appointmentId) {
        // Metoda usunięta - niepotrzebne wywołania do admina
        return true;
    }

    /**
     * Zamyka aktualną wizytę
     */
    private function closeCurrentAppointment($appointmentId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now', 'localtime'), is_completed = 1
                WHERE id = ?
            ");

            $result = $stmt->execute([$appointmentId]);
            error_log("[iGabinet] Zamknięcie wizyty - ID: {$appointmentId}, Wynik: " . ($result ? 'sukces' : 'błąd'));

            return $result;
        } catch (Exception $e) {
            error_log("[iGabinet] Błąd zamykania wizyty: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generuje unikalny 16-znakowy kod śledzenia
     * @return string Unikalny kod śledzenia
     */
    private function generateUniqueTrackingCode() {
        do {
            // Generuj kod: 4 cyfry + 12 znaków alfanumerycznych
            $code = sprintf('%04d%s', rand(1000, 9999), substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 12));

            // Sprawdź czy kod już istnieje
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM queue_appointments WHERE tracking_code = ?");
            $stmt->execute([$code]);
            $exists = $stmt->fetchColumn() > 0;
        } while ($exists);

        return $code;
    }

    /**
     * Pobierz istniejącą wizytę po external_id i client_id
     */
    private function getExistingAppointment($externalId) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, status, visit FROM queue_appointments
                WHERE external_id = ? -- UWAGA: Kolumna client_id została usunięta z queue_appointments
            ");
            $stmt->execute([$externalId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("[iGabinet] Error getting existing appointment: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Zamknij inne aktywne wizyty dla tego lekarza przed wywołaniem nowej
     */
    private function closeOtherActiveAppointmentsForDoctor($doctorId, $excludeAppointmentId) {
        try {
            error_log("[iGabinet] Zamykam inne aktywne wizyty dla lekarza $doctorId, wykluczając $excludeAppointmentId");

            $stmt = $this->db->prepare("
                UPDATE queue_appointments
                SET status = 'closed', completed_at = datetime('now', 'localtime'), is_completed = 1
                WHERE doctor_id = ?
                -- UWAGA: Kolumna client_id została usunięta z queue_appointments
                AND external_id != ?
                AND status IN ('called', 'current')
            ");

            $result = $stmt->execute([$doctorId, $excludeAppointmentId]);
            $affectedRows = $stmt->rowCount();

            if ($affectedRows > 0) {
                error_log("[iGabinet] Zamknięto $affectedRows aktywnych wizyt dla lekarza $doctorId");
            }

            return $result;
        } catch (Exception $e) {
            error_log("[iGabinet] Błąd zamykania aktywnych wizyt: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Pobiera wszystkie istniejące wizyty z bazy danych dla dni importu
     */
    private function getExistingAppointmentsForDays($days) { // UWAGA: Usunięto parametr syncCode
        try {
            $dates = [];
            foreach ($days as $day) {
                $dates[] = $day['date'];
            }

            if (empty($dates)) {
                return [];
            }

            $placeholders = str_repeat('?,', count($dates) - 1) . '?';
            $stmt = $this->db->prepare("
                SELECT * FROM queue_appointments 
                WHERE appointment_date IN ($placeholders) -- UWAGA: Kolumna client_id została usunięta z queue_appointments
            ");

            // UWAGA: Usunięto syncCode z parametrów
            $stmt->execute($dates);
            $appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Konwertuj na tablicę asocjacyjną po external_id
            $result = [];
            foreach ($appointments as $appointment) {
                $result[$appointment['external_id']] = $appointment;
            }

            return $result;
        } catch (Exception $e) {
            error_log("[iGabinet] Error getting existing appointments: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Określa statusy wizyt w pamięci (uproszczona logika bez visit/locked)
     */
    private function determineAppointmentStatuses(&$appointments) {
        foreach ($appointments as &$appointment) {
            $appointment['final_status'] = 'waiting';
            $appointment['final_is_patient_present'] = $appointment['is_patient_present'];
            $appointment['final_is_confirmed'] = $appointment['is_confirmed'];
            $appointment['final_is_completed'] = $appointment['is_completed'];
            $appointment['final_is_sms_sent'] = $appointment['is_sms_sent'];
            
            // Uproszczona logika - wszystkie wizyty domyślnie są 'waiting'
            // Status będzie zmieniany przez interfejs lekarza
        }
    }

    /**
     * Przygotowuje zmiany między nowymi a istniejącymi wizytami
     */
    private function prepareAppointmentChanges($newAppointments, $existingAppointments) {
        $changes = [
            'insert' => [],
            'update' => [],
            'delete' => []
        ];

        foreach ($newAppointments as $newAppointment) {
            $externalId = $newAppointment['id'];

            if (isset($existingAppointments[$externalId])) {
                $existing = $existingAppointments[$externalId];

                // Sprawdź czy są różnice
                $hasChanges = false;
                $updateData = [];

                $fieldsToCheck = [
                    'patient_name' => 'patient_name',
                    'appointment_time' => 'appointment_time',
                    'appointment_duration' => 'appointment_duration',
                    'phone_number' => 'phone_number',
                    'status' => 'final_status',
                    'is_patient_present' => 'final_is_patient_present',
                    'is_confirmed' => 'final_is_confirmed',
                    'is_completed' => 'final_is_completed',
                    'is_sms_sent' => 'final_is_sms_sent'
                ];

                foreach ($fieldsToCheck as $dbField => $newField) {
                    $newValue = $newAppointment[$newField];
                    $existingValue = $existing[$dbField];

                    // Konwertuj wartości dla porównania
                    if (in_array($dbField, ['is_confirmed', 'is_patient_present', 'is_completed', 'is_sms_sent'])) {
                        $newValue = (int)$newValue;
                        $existingValue = (int)$existingValue;
                    }

                    if ($newValue != $existingValue) {
                        $hasChanges = true;
                        $updateData[$dbField] = $newValue;
                    }
                }

                if ($hasChanges) {
                    $updateData['id'] = $existing['id'];
                    $updateData['external_id'] = $externalId;
                    // Zachowaj istniejący tracking_code
                    if (!empty($existing['tracking_code'])) {
                        $updateData['tracking_code'] = $existing['tracking_code'];
                    }
                    $changes['update'][] = $updateData;
                }
            } else {
                // Nowa wizyta do wstawienia
                $insertData = [
                    'external_id' => $externalId,
                    'doctor_id' => $newAppointment['doctor_id'],
                    'patient_name' => $newAppointment['patient_name'],
                    'appointment_time' => $newAppointment['appointment_time'],
                    'appointment_duration' => $newAppointment['appointment_duration'],
                    'phone_number' => $newAppointment['phone_number'],
                    'status' => $newAppointment['final_status'],
                    'is_patient_present' => $newAppointment['final_is_patient_present'],
                    'is_confirmed' => $newAppointment['final_is_confirmed'],
                    'is_completed' => $newAppointment['final_is_completed'],
                    'is_sms_sent' => $newAppointment['final_is_sms_sent'],
                    'appointment_date' => $newAppointment['appointment_date'],
                    'tracking_code' => $this->generateUniqueTrackingCode()
                ];
                $changes['insert'][] = $insertData;
            }
        }

        return $changes;
    }

    /**
     * Zastosowuje zmiany w jednej transakcji
     */
    private function applyAppointmentChanges($changes) {
        try {
            $this->db->beginTransaction();

            $importedCount = 0;

            // Wstaw nowe wizyty
            if (!empty($changes['insert'])) {
                $stmt = $this->db->prepare("
                    INSERT INTO queue_appointments
                    (external_id, doctor_id, patient_name, appointment_time, appointment_duration,
                     phone_number, status, is_patient_present, is_confirmed, is_completed, is_sms_sent,
                     appointment_date, tracking_code, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'))
                ");

                foreach ($changes['insert'] as $data) {
                    $stmt->execute([
                        $data['external_id'],
                        $data['doctor_id'],
                        $data['patient_name'],
                        $data['appointment_time'],
                        $data['appointment_duration'],
                        $data['phone_number'],
                        $data['status'],
                        $data['is_patient_present'],
                        $data['is_confirmed'],
                        $data['is_completed'],
                        $data['is_sms_sent'],
                        $data['appointment_date'],
                        $data['tracking_code']
                    ]);
                    $importedCount++;
                }
            }

            // Zaktualizuj istniejące wizyty
            if (!empty($changes['update'])) {
                foreach ($changes['update'] as $data) {
                    $id = $data['id'];
                    unset($data['id']);
                    unset($data['external_id']);

                    $fields = array_keys($data);
                    $placeholders = str_repeat('?,', count($fields) - 1) . '?';
                    $setClause = implode(' = ?, ', $fields) . ' = ?';

                    $stmt = $this->db->prepare("
                        UPDATE queue_appointments 
                        SET $setClause
                        WHERE id = ?
                    ");

                    $values = array_values($data);
                    $values[] = $id;
                    $stmt->execute($values);
                    $importedCount++;
                }
            }

            $this->db->commit();
            return $importedCount;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log("[iGabinet] Error applying appointment changes: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Dodaje tracking codes dla istniejących wizyt bez tracking codes
     */
    private function addMissingTrackingCodes() { // UWAGA: Usunięto parametr syncCode
        try {
            // Znajdź wizyty bez tracking codes
            $stmt = $this->db->prepare("
                SELECT id FROM queue_appointments 
                WHERE tracking_code IS NULL -- UWAGA: Kolumna client_id została usunięta z queue_appointments
            ");
            $stmt->execute(); // UWAGA: Usunięto parametr syncCode
            $appointmentsWithoutTracking = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($appointmentsWithoutTracking)) {
                return;
            }

            // Dodaj tracking codes
            $stmt = $this->db->prepare("
                UPDATE queue_appointments 
                SET tracking_code = ? 
                WHERE id = ?
            ");

            foreach ($appointmentsWithoutTracking as $appointment) {
                $trackingCode = $this->generateUniqueTrackingCode();
                $stmt->execute([$trackingCode, $appointment['id']]);
            }

            error_log("[iGabinet] Added tracking codes for " . count($appointmentsWithoutTracking) . " appointments");
        } catch (Exception $e) {
            error_log("[iGabinet] Error adding missing tracking codes: " . $e->getMessage());
        }
    }

}