<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KtoOstatni - Debug Dodatku Chrome</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .section h2 {
            margin-top: 0;
            color: #4facfe;
            font-size: 1.5em;
        }

        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        button.primary {
            background: #4facfe;
            border-color: #4facfe;
        }

        button.primary:hover {
            background: #3d8bfe;
        }

        .output {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status.success {
            background: rgba(46, 213, 115, 0.3);
            border: 1px solid #2ed573;
        }

        .status.error {
            background: rgba(255, 71, 87, 0.3);
            border: 1px solid #ff4757;
        }

        .status.info {
            background: rgba(79, 172, 254, 0.3);
            border: 1px solid #4facfe;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .code {
            background: rgba(0, 0, 0, 0.4);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .highlight {
            background: rgba(255, 255, 0, 0.2);
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 KtoOstatni - Debug Dodatku Chrome</h1>

        <div class="grid">
            <!-- Status dodatku -->
            <div class="section">
                <h2>📊 Status Dodatku</h2>
                <button onclick="checkExtensionStatus()" class="primary">Sprawdź Status</button>
                <button onclick="checkAlarms()">Sprawdź Alarmy</button>
                <button onclick="getConfig()">Pobierz Konfigurację</button>
                <div id="extensionStatus" class="output"></div>
            </div>

            <!-- Testowanie synchronizacji -->
            <div class="section">
                <h2>🔄 Testowanie Synchronizacji</h2>
                <button onclick="testManualSync()" class="primary">Test Ręcznej Synchronizacji</button>
                <button onclick="testContentScript()">Test Content Script</button>
                <button onclick="testAPI()">Test API Endpoint</button>
                <div id="syncStatus" class="output"></div>
            </div>

            <!-- Konfiguracja -->
            <div class="section">
                <h2>⚙️ Konfiguracja</h2>
                <div>
                    <label>Kod synchronizacji:</label>
                    <input type="text" id="syncCodeInput" value="igab000000000001" style="margin: 5px; padding: 5px; border-radius: 3px; border: 1px solid #ccc;">
                </div>
                <div>
                    <label>Interwał (minuty):</label>
                    <select id="intervalSelect" style="margin: 5px; padding: 5px; border-radius: 3px; border: 1px solid #ccc;">
                        <option value="1">1 minuta</option>
                        <option value="5">5 minut</option>
                        <option value="15" selected>15 minut</option>
                        <option value="30">30 minut</option>
                    </select>
                </div>
                <button onclick="saveTestConfig()" class="primary">Zapisz Konfigurację</button>
                <button onclick="enableAutoSync()">Włącz Auto-Sync</button>
                <button onclick="disableAutoSync()">Wyłącz Auto-Sync</button>
                <div id="configStatus" class="output"></div>
            </div>

            <!-- Logi -->
            <div class="section">
                <h2>📝 Logi i Debugowanie</h2>
                <button onclick="clearLogs()">Wyczyść Logi</button>
                <button onclick="exportLogs()">Eksportuj Logi</button>
                <div class="code">
                    <strong>Instrukcje debugowania:</strong><br>
                    1. Otwórz <span class="highlight">chrome://extensions/</span><br>
                    2. Znajdź "KtoOstatni" i kliknij "Szczegóły"<br>
                    3. Kliknij "Sprawdź widoki: background page"<br>
                    4. Sprawdź konsolę w narzędziach deweloperskich
                </div>
                <div id="debugLogs" class="output"></div>
            </div>
        </div>

        <!-- Instrukcje -->
        <div class="section">
            <h2>📋 Instrukcje Testowania</h2>
            <div class="code">
                <strong>Krok 1:</strong> Sprawdź status dodatku - czy jest zainstalowany i aktywny<br>
                <strong>Krok 2:</strong> Skonfiguruj kod synchronizacji i interwał<br>
                <strong>Krok 3:</strong> Włącz automatyczną synchronizację<br>
                <strong>Krok 4:</strong> Sprawdź czy alarmy są aktywne<br>
                <strong>Krok 5:</strong> Przetestuj ręczną synchronizację<br>
                <strong>Krok 6:</strong> Sprawdź logi w background page
            </div>
        </div>
    </div>

    <script>
        let logs = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logs.push(logEntry);
            
            const debugLogs = document.getElementById('debugLogs');
            debugLogs.innerHTML = logs.slice(-20).join('<br>');
            debugLogs.scrollTop = debugLogs.scrollHeight;
            
            console.log('KtoOstatni Debug:', message);
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function checkExtensionStatus() {
            log('Sprawdzanie statusu dodatku...');
            
            try {
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    throw new Error('Chrome runtime niedostępny - dodatek nie jest zainstalowany');
                }

                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                if (response.success) {
                    const config = response.config;
                    showStatus('extensionStatus', `
                        ✅ Dodatek aktywny<br>
                        📝 Kod sync: ${config.syncCode || 'brak'}<br>
                        ⚙️ Włączony: ${config.isEnabled ? 'TAK' : 'NIE'}<br>
                        🔄 Auto-sync: ${config.autoSync ? 'TAK' : 'NIE'}<br>
                        ⏰ Interwał: ${config.syncInterval} min<br>
                        📅 Ostatnia sync: ${config.lastSync ? new Date(config.lastSync).toLocaleString() : 'nigdy'}
                    `, 'success');
                    log('Status dodatku pobrany pomyślnie');
                } else {
                    throw new Error(response.error);
                }
            } catch (error) {
                showStatus('extensionStatus', `❌ Błąd: ${error.message}`, 'error');
                log(`Błąd sprawdzania statusu: ${error.message}`, 'error');
            }
        }

        async function checkAlarms() {
            log('Sprawdzanie alarmów...');
            
            try {
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ action: 'getAlarmStatus' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                if (response.success) {
                    let statusHtml = '';
                    if (response.hasAlarm) {
                        statusHtml = `
                            ✅ Alarm auto-sync aktywny<br>
                            📅 Następna sync: ${response.alarmInfo.scheduledTime}<br>
                            ⏰ Interwał: ${response.alarmInfo.periodInMinutes} min
                        `;
                    } else {
                        statusHtml = '❌ Brak aktywnego alarmu auto-sync';
                    }
                    
                    if (response.allAlarms.length > 0) {
                        statusHtml += '<br><br>📋 Wszystkie alarmy:<br>';
                        response.allAlarms.forEach(alarm => {
                            statusHtml += `• ${alarm.name}: ${alarm.scheduledTime} (${alarm.periodInMinutes} min)<br>`;
                        });
                    }
                    
                    showStatus('extensionStatus', statusHtml, response.hasAlarm ? 'success' : 'error');
                    log('Status alarmów pobrany pomyślnie');
                } else {
                    throw new Error(response.error);
                }
            } catch (error) {
                showStatus('extensionStatus', `❌ Błąd sprawdzania alarmów: ${error.message}`, 'error');
                log(`Błąd sprawdzania alarmów: ${error.message}`, 'error');
            }
        }

        // Dodaj pozostałe funkcje...
        async function getConfig() {
            await checkExtensionStatus();
        }

        async function testManualSync() {
            log('Rozpoczynam test ręcznej synchronizacji...');
            showStatus('syncStatus', '🔄 Synchronizacja w toku...', 'info');
            
            try {
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ action: 'manualSync' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                if (response.success) {
                    showStatus('syncStatus', '✅ Synchronizacja zakończona pomyślnie', 'success');
                    log('Ręczna synchronizacja zakończona pomyślnie');
                } else {
                    throw new Error(response.error);
                }
            } catch (error) {
                showStatus('syncStatus', `❌ Błąd synchronizacji: ${error.message}`, 'error');
                log(`Błąd ręcznej synchronizacji: ${error.message}`, 'error');
            }
        }

        async function saveTestConfig() {
            const syncCode = document.getElementById('syncCodeInput').value;
            const interval = parseInt(document.getElementById('intervalSelect').value);
            
            log(`Zapisuję konfigurację: kod=${syncCode}, interwał=${interval}min`);
            
            try {
                const config = {
                    syncCode: syncCode,
                    syncInterval: interval,
                    isEnabled: true,
                    autoSync: true
                };

                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ action: 'saveConfig', config: config }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                if (response.success) {
                    showStatus('configStatus', '✅ Konfiguracja zapisana pomyślnie', 'success');
                    log('Konfiguracja zapisana pomyślnie');
                    
                    // Sprawdź alarmy po zapisaniu
                    setTimeout(checkAlarms, 1000);
                } else {
                    throw new Error(response.error);
                }
            } catch (error) {
                showStatus('configStatus', `❌ Błąd zapisywania: ${error.message}`, 'error');
                log(`Błąd zapisywania konfiguracji: ${error.message}`, 'error');
            }
        }

        function clearLogs() {
            logs = [];
            document.getElementById('debugLogs').innerHTML = '';
            log('Logi wyczyszczone');
        }

        // Inicjalizacja
        document.addEventListener('DOMContentLoaded', function() {
            log('Strona debug załadowana');
            checkExtensionStatus();
        });
    </script>
</body>
</html>
