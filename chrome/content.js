// Content script dla <PERSON>toOstatni - Synchronizacja wizyt z iGabinet (API Version)
console.log('KtoOstatni: Content script załadowany na:', window.location.href);
console.log('KtoOstatni: Chrome runtime dostępny:', typeof chrome !== 'undefined' && !!chrome.runtime);

// Sprawdź czy jesteśmy na stronie iGabinet
const isIGabinet = window.location.href.includes('igabinet.pl');
const isTestPage = window.location.href.includes('test_page.html');

console.log('KtoOstatni: isIGabinet:', isIGabinet, 'isTestPage:', isTestPage);

// Funkcja wstrzykująca skrypt do głównego kontekstu strony
function injectScript(filePath, tag) {
    console.log('KtoOstatni: Wstrzykiwanie skryptu:', filePath);
    
    const node = document.getElementsByTagName(tag)[0] || document.documentElement;
    const script = document.createElement('script');
    script.setAttribute('type', 'text/javascript');
    script.setAttribute('src', filePath);
    
    // Obsługa błędów ładowania skryptu
    script.onerror = () => {
        console.error('KtoOstatni: Błąd wstrzykiwania skryptu:', filePath);
    };
    
    script.onload = () => {
        console.log('KtoOstatni: Skrypt wstrzyknięty pomyślnie:', filePath);
    };
    
    node.appendChild(script);
}

// Wstrzyknij skrypt do przechwytywania wiadomości WebSocket
if (typeof chrome !== 'undefined' && chrome.runtime) {
    try {
        // Wstrzyknij skrypt jak najwcześniej
        document.addEventListener('DOMContentLoaded', () => {
            injectScript(chrome.runtime.getURL('injector.js'), 'head');
        });
        
        // Próba wstrzyknięcia natychmiastowego (jeśli DOM jest już gotowy)
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            injectScript(chrome.runtime.getURL('injector.js'), 'head');
        }
    } catch (error) {
        console.error('KtoOstatni: Błąd podczas wstrzykiwania injector.js:', error);
    }
} else {
    console.error('KtoOstatni: Chrome runtime niedostępny, nie można wstrzyknąć injector.js');
}

// Inicjalizacja obserwera dla elementów li.underline-tag
function initializeUnderlineTagObserver() {
    // Sprawdź czy jesteśmy na stronie iGabinet lub stronie testowej
    const isIGabinet = window.location.href.includes('igabinet.pl');
    const isTestPage = window.location.href.includes('test_page.html');
    
    if (isIGabinet || isTestPage) {
        console.log('KtoOstatni: Jesteśmy na odpowiedniej stronie dla obserwera li.underline-tag');
        
        // Funkcja do inicjalizacji obserwera po załadowaniu DOM
        const initObserver = () => {
            try {
                const underlineTagObserver = setupUnderlineTagObserver();
                console.log('KtoOstatni: Obserwer dla elementów li.underline-tag uruchomiony');
            } catch (error) {
                console.error('KtoOstatni: Błąd inicjalizacji obserwera li.underline-tag:', error);
            }
        };
        
        // Inicjalizuj obserwera po załadowaniu DOM
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initObserver);
        } else {
            initObserver();
        }
        
        // Wyślij sygnał gotowości do background script
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.sendMessage({
                action: 'contentScriptReady',
                url: window.location.href,
                timestamp: new Date().toISOString()
            }).then(() => {
                console.log('KtoOstatni: Sygnał gotowości wysłany pomyślnie');
            }).catch(error => {
                console.log('KtoOstatni: Nie można wysłać sygnału gotowości:', error);
            });
        } else {
            console.error('KtoOstatni: Chrome runtime niedostępny!');
        }
    } else {
        console.log('KtoOstatni: Nie jesteśmy na odpowiedniej stronie, obserwer li.underline-tag nieaktywny');
    }
}

// Funkcja obserwująca pojawienie się elementów li z klasą underline-tag
function setupUnderlineTagObserver() {
    console.log('KtoOstatni: Inicjalizacja obserwera dla elementów li.underline-tag');
    
    // Konfiguracja MutationObserver
    const observerConfig = {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'id']
    };
    
    // Funkcja wywoływana przy wykryciu zmian w DOM
    const mutationCallback = function(mutations) {
        mutations.forEach(function(mutation) {
            // Obsługa dodania nowych węzłów
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(function(node) {
                    // Sprawdź czy dodany węzeł jest elementem DOM
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Sprawdź czy sam węzeł jest li.underline-tag
                        if (node.tagName === 'LI' && node.classList.contains('underline-tag')) {
                            logUnderlineTagInfo(node);
                        }
                        
                        // Sprawdź czy węzeł zawiera li.underline-tag wewnątrz
                        const underlineTags = node.querySelectorAll('li.underline-tag');
                        if (underlineTags.length > 0) {
                            underlineTags.forEach(tag => logUnderlineTagInfo(tag));
                        }
                    }
                });
            }
            
            // Obsługa zmian atrybutów
            if (mutation.type === 'attributes') {
                const node = mutation.target;
                
                // Sprawdź czy zmieniony element jest li z klasą underline-tag
                if (node.tagName === 'LI' && node.classList.contains('underline-tag')) {
                    console.log('KtoOstatni: Wykryto zmianę atrybutu w elemencie li.underline-tag:', mutation.attributeName);
                    logUnderlineTagInfo(node);
                }
            }
        });
    };
    
// Globalna mapa do przechowywania informacji o statusTag dla każdego appointmentId
const appointmentStatusTags = new Map();

// Funkcja logująca informacje o znalezionym elemencie i zapisująca dane do globalnej mapy
function logUnderlineTagInfo(element) {
    const tagId = element.id;
    
    // Znajdź rodzica z atrybutem data-term-id
    let parent = element.parentElement;
    let termId = null;
    
    while (parent && !termId) {
        if (parent.hasAttribute('data-term-id')) {
            termId = parent.getAttribute('data-term-id');
            break;
        }
        parent = parent.parentElement;
    }
    
    // Zapisz informacje do globalnej mapy (termId to appointmentId)
    if (termId && tagId) {
        appointmentStatusTags.set(termId, tagId);
        console.log('KtoOstatni: Zapisano statusTag dla appointmentId:', {
            appointmentId: termId,
            statusTag: tagId
        });
    }
    
    console.log('KtoOstatni: Wykryto element li.underline-tag:', {
        id: tagId,
        parentTermId: termId
    });
}

// Funkcja do pobierania statusTag dla danego appointmentId
function getStatusTagForAppointment(appointmentId) {
    return appointmentStatusTags.get(appointmentId) || null;
}

// Funkcja do pobierania wszystkich zebranych statusTagów
function getAllStatusTags() {
    return Object.fromEntries(appointmentStatusTags);
}

// Udostępnij funkcje dla strony testowej
window.getAllStatusTags = getAllStatusTags;
window.getStatusTagForAppointment = getStatusTagForAppointment;

// Nasłuchuj na wiadomości od strony testowej
window.addEventListener('message', function(event) {
    if (event.data && event.data.action === 'getStatusTags') {
        console.log('KtoOstatni: Otrzymano żądanie statusTags od strony');
        
        // Wyślij odpowiedź z zebranymi statusTag
        window.postMessage({
            action: 'statusTagsResponse',
            statusTags: getAllStatusTags()
        }, '*');
    }
});
    
    // Utwórz i uruchom obserwera
    const observer = new MutationObserver(mutationCallback);
    observer.observe(document.body, observerConfig);
    
    // Sprawdź czy już istnieją takie elementy w DOM
    const existingTags = document.querySelectorAll('li.underline-tag');
    if (existingTags.length > 0) {
        console.log('KtoOstatni: Znaleziono istniejące elementy li.underline-tag:', existingTags.length);
        existingTags.forEach(tag => logUnderlineTagInfo(tag));
    }
    
    return observer;
}

// Uruchom inicjalizację obserwera dla elementów li.underline-tag
initializeUnderlineTagObserver();

// Nasłuchuj na wiadomości z popup i background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('KtoOstatni: Content script otrzymał wiadomość:', request);

    if (request.action === 'ping') {
        sendResponse({
            success: true,
            message: 'Content script jest aktywny',
            url: window.location.href,
            timestamp: new Date().toISOString()
        });
        return true;
    }

    if (request.action === 'extractScheduleData') {
        // Pobierz parametry z żądania
        const syncCode = request.syncCode || null;
        const skipUnknownPatient = request.skipUnknownPatient || false;
        const syncTomorrowAppointments = request.syncTomorrowAppointments || false;
        console.log('KtoOstatni: Otrzymano żądanie extractScheduleData z kodem:', syncCode);
        console.log('KtoOstatni: Pomijaj nieznany pacjent:', skipUnknownPatient);
        console.log('KtoOstatni: Synchronizuj wizyty z jutro:', syncTomorrowAppointments);

        extractScheduleFromPage(syncCode, skipUnknownPatient, syncTomorrowAppointments)
            .then(data => {
                sendResponse({
                    success: true,
                    data: data,
                    timestamp: new Date().toISOString()
                });
            })
            .catch(error => {
                console.error('KtoOstatni: Błąd wyciągania danych:', error);
                sendResponse({
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            });
        return true; // Asynchroniczna odpowiedź
    }

    if (request.action === 'getScheduleDataForDate') {
        // Pobierz dane za określony dzień
        const targetDate = request.date;
        const skipUnknownPatient = request.skipUnknownPatient || false;

        console.log('KtoOstatni: Otrzymano żądanie getScheduleDataForDate za dzień:', targetDate);
        console.log('KtoOstatni: Pomijaj nieznany pacjent:', skipUnknownPatient);

        extractScheduleForSpecificDate(targetDate, skipUnknownPatient)
            .then(data => {
                sendResponse({
                    success: true,
                    data: data,
                    timestamp: new Date().toISOString()
                });
            })
            .catch(error => {
                console.error('KtoOstatni: Błąd pobierania danych za dzień', targetDate, ':', error);
                sendResponse({
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            });
        return true; // Asynchroniczna odpowiedź
    }

    return false;
});

// Globalna cache dla słownika lekarzy
let doctorsDictionary = null;

// Funkcja do pobierania słownika wszystkich lekarzy (wykonywana raz)
async function fetchDoctorsDictionary() {
    if (doctorsDictionary) {
        console.log('KtoOstatni: Używam cache słownika lekarzy');
        return doctorsDictionary;
    }

    try {
        console.log('KtoOstatni: Pobieranie słownika lekarzy z API getAvailableProducts');

        // Sprawdź czy jesteśmy na stronie iGabinet
        if (!window.location.href.includes('igabinet.pl')) {
            console.log('KtoOstatni: Nie jesteśmy na stronie iGabinet, używam danych testowych');
            doctorsDictionary = [
                { "id": 10, "name": "ginekolog dr n.med. Małgorzata Olesiak-Andryszczak", "deleted": "0" },
                { "id": 20, "name": "ginekolog dr Natalia Kubat", "deleted": "0" },
                { "id": 72, "name": "ginekolog dr Joanna Nestorowicz-Czernianin", "deleted": "0" },
                { "id": 105, "name": "ginekolog dr Beata Dawiec", "deleted": "0" },
                { "id": 114, "name": "ginekolog dr Ewelina Sądaj", "deleted": "0" },
                { "id": 129, "name": "ginekolog dr Jakub Andrzejewski", "deleted": "0" },
                { "id": 1748423446, "name": "Gabinet Położnej", "deleted": "0" },
                // Dodatkowi lekarze dla testów
                { "id": 200, "name": "ginekolog dr Oliwia Kopera", "deleted": "0" },
                { "id": 201, "name": "ginekolog dr Aneta Walaszek-Gruszka", "deleted": "0" },
                { "id": 202, "name": "ginekolog dr Yuliia Baraniak", "deleted": "0" },
                { "id": 203, "name": "ginekolog dr Tomasz Kościelniak", "deleted": "0" },
                { "id": 204, "name": "ginekolog dr Agnieszka Tyszko-Tymińska", "deleted": "0" }
            ];
            return doctorsDictionary;
        }

        const response = await fetch("https://sonokard.igabinet.pl/admin/request/work_schedule_request.php", {
            method: 'POST',
            headers: {
                'accept': 'application/json',
                'accept-language': 'pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7',
                'content-type': 'application/json',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin'
            },
            body: JSON.stringify({
                "section": "getAvailableProducts"
            }),
            mode: 'cors',
            credentials: 'include'
        });

        if (!response.ok) {
            const errorText = await response.text().catch(() => '');
            console.error(`KtoOstatni: Błąd HTTP ${response.status} podczas pobierania słownika lekarzy:`, errorText);
            throw new Error(`HTTP error! status: ${response.status}, details: ${errorText}`);
        }

        const data = await response.json();
        console.log('KtoOstatni: Odpowiedź API getAvailableProducts:', data);

        // Parsuj odpowiedź
        let doctors = [];
        if (data.success && data.data && Array.isArray(data.data)) {
            doctors = data.data;
        } else if (Array.isArray(data)) {
            doctors = data;
        } else {
            console.warn('KtoOstatni: Nieprawidłowa struktura odpowiedzi słownika lekarzy:', data);
            return [];
        }

        // Zapisz do cache
        doctorsDictionary = doctors;
        console.log('KtoOstatni: Słownik lekarzy zapisany do cache:', doctors.length, 'lekarzy');
        return doctors;

    } catch (error) {
        console.error('KtoOstatni: Błąd pobierania słownika lekarzy:', error);
        return [];
    }
}

// Funkcja do pobierania ID lekarzy pracujących w danym dniu
async function fetchWorkingDoctorIds(date) {
    try {
        const formattedDate = date.toISOString().split('T')[0];
        console.log('KtoOstatni: Pobieranie lekarzy pracujących w dniu:', formattedDate);

        // Sprawdź czy jesteśmy na stronie iGabinet
        if (!window.location.href.includes('igabinet.pl')) {
            console.log('KtoOstatni: Nie jesteśmy na stronie iGabinet, zwracam testowe ID');
            return [10, 105, 114];
        }

        const response = await fetch("https://sonokard.igabinet.pl/admin/request/work_schedule_request.php", {
            method: 'POST',
            headers: {
                'accept': 'application/json',
                'accept-language': 'pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7',
                'content-type': 'application/json',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin'
            },
            body: JSON.stringify({
                "section": "getWorkingProducts",
                "date": formattedDate,
                "facility_id": [1]
            }),
            mode: 'cors',
            credentials: 'include'
        });

        if (!response.ok) {
            const errorText = await response.text().catch(() => '');
            console.error(`KtoOstatni: Błąd HTTP ${response.status} podczas pobierania pracujących lekarzy:`, errorText);
            throw new Error(`HTTP error! status: ${response.status}, details: ${errorText}`);
        }

        const data = await response.json();
        console.log('KtoOstatni: Odpowiedź API getWorkingProducts:', data);

        // Parsuj odpowiedź - getWorkingProducts zwraca tablicę ID lekarzy
        let doctorIds = [];
        if (data.success && data.data && Array.isArray(data.data)) {
            doctorIds = data.data;
        } else if (Array.isArray(data)) {
            doctorIds = data;
        } else {
            console.warn('KtoOstatni: Nieprawidłowa struktura odpowiedzi getWorkingProducts:', data);
            return [];
        }

        console.log('KtoOstatni: ID lekarzy pracujących w dniu', formattedDate, ':', doctorIds);
        return doctorIds;

    } catch (error) {
        console.error('KtoOstatni: Błąd pobierania pracujących lekarzy:', error);
        return [];
    }
}

// Usunięto nieużywane funkcje shouldSyncNextDay i updateLastNextDaySync

// Usunięto nieużywaną funkcję fetchNextWorkingDayData

// Funkcja do pobierania aktualnej daty (ważne dla zmiany dnia o północy)
function getCurrentDate() {
    // Zawsze zwraca nową instancję Date - ważne dla automatycznego odświeżania daty o północy
    const now = new Date();
    console.log('KtoOstatni: Aktualna data/czas:', now.toLocaleString());
    return now;
}

// Funkcja do pobierania danych za określony dzień
async function extractScheduleForSpecificDate(targetDate, skipUnknownPatient = false) {
    console.log('KtoOstatni: Rozpoczynam pobieranie danych za dzień:', targetDate);
    console.log('KtoOstatni: Aktualny URL:', window.location.href);
    console.log('KtoOstatni: Pomijaj nieznany pacjent:', skipUnknownPatient);

    try {
        // Sprawdź czy jesteśmy na stronie iGabinet
        if (!window.location.href.includes('igabinet.pl')) {
            throw new Error('Nie jesteś na stronie iGabinet');
        }

        // Pobierz słownik lekarzy
        const doctorsDict = await fetchDoctorsDictionary();
        if (!doctorsDict || Object.keys(doctorsDict).length === 0) {
            throw new Error('Nie udało się pobrać słownika lekarzy');
        }

        // Pobierz dane za określony dzień
        const scheduleData = await getScheduleDataForDate(targetDate, doctorsDict, skipUnknownPatient);

        if (!scheduleData || !scheduleData.syncData || !scheduleData.syncData.days || scheduleData.syncData.days.length === 0) {
            console.log('KtoOstatni: Brak danych za dzień:', targetDate);
            return {
                syncData: {
                    days: []
                }
            };
        }

        console.log('KtoOstatni: Pobrano dane za dzień:', targetDate, 'Liczba dni:', scheduleData.syncData.days.length);
        return scheduleData;

    } catch (error) {
        console.error('KtoOstatni: Błąd pobierania danych za dzień:', targetDate, error);
        throw error;
    }
}

// Funkcja do wyciągania danych harmonogramu z API iGabinet
async function extractScheduleFromPage(syncCode = null, skipUnknownPatient = false, syncTomorrowAppointments = false) {
    console.log('KtoOstatni: Rozpoczynam pobieranie danych z API iGabinet...');
    console.log('KtoOstatni: Aktualny URL:', window.location.href);
    console.log('KtoOstatni: Kod synchronizacji:', syncCode);
    console.log('KtoOstatni: Pomijaj nieznany pacjent:', skipUnknownPatient);
    console.log('KtoOstatni: Synchronizuj wizyty z jutro:', syncTomorrowAppointments);

    try {
        // Sprawdź kolejne dni robocze w poszukiwaniu wizyt
        const scheduleData = await findScheduleWithAppointmentsFromAPI(syncCode, skipUnknownPatient, syncTomorrowAppointments);

        if (scheduleData) {
            console.log('KtoOstatni: Znaleziono dane harmonogramu z API:', scheduleData);

            // Jeśli włączona synchronizacja wizyt z jutro, dane zostały już pobrane w findScheduleWithAppointmentsFromAPI
            if (syncTomorrowAppointments && false) { // Wyłączone - stara logika
                console.log('KtoOstatni: Czas na synchronizację kolejnego dnia roboczego');
                
                // Pobierz słownik lekarzy (jeśli jeszcze nie został pobrany)
                const doctorsDictionary = await fetchDoctorsDictionary();
                
                // Stara logika usunięta - dane z kolejnych dni są już pobierane w findScheduleWithAppointmentsFromAPI
            } else {
                console.log('KtoOstatni: Nie czas jeszcze na synchronizację kolejnego dnia roboczego');
            }

            return scheduleData;
        }

        throw new Error('Nie znaleziono wizyt w kolejnych dniach roboczych');

    } catch (error) {
        console.error('KtoOstatni: Błąd pobierania danych z API:', error);
        throw error;
    }
}

// Funkcja do pobierania danych za określony dzień z API
async function getScheduleDataForDate(targetDate, doctorsDict, skipUnknownPatient = false) {
    console.log('KtoOstatni: Pobieranie danych z API za dzień:', targetDate);

    try {
        // Konwertuj datę na format YYYY-MM-DD
        const dateObj = new Date(targetDate);
        const formattedDate = dateObj.toISOString().split('T')[0];

        console.log('KtoOstatni: Sprawdzam dzień:', formattedDate);

        // Pobierz dane za określony dzień
        const dayData = await getScheduleDataFromAPI(formattedDate, doctorsDict, skipUnknownPatient);

        if (!dayData || !dayData.doctors || dayData.doctors.length === 0) {
            console.log('KtoOstatni: Brak wizyt za dzień:', formattedDate);
            return {
                syncData: {
                    days: []
                }
            };
        }

        // Sprawdź czy są jakieś wizyty
        let totalAppointments = 0;
        dayData.doctors.forEach(doctor => {
            totalAppointments += doctor.appointments?.length || 0;
        });

        if (totalAppointments === 0) {
            console.log('KtoOstatni: Brak wizyt za dzień:', formattedDate);
            return {
                syncData: {
                    days: []
                }
            };
        }

        console.log(`KtoOstatni: Znaleziono ${totalAppointments} wizyt za dzień ${formattedDate}`);

        return {
            syncData: {
                days: [dayData]
            }
        };

    } catch (error) {
        console.error('KtoOstatni: Błąd pobierania danych za dzień:', targetDate, error);
        throw error;
    }
}

// Funkcja do sprawdzania kolejnych dni roboczych w poszukiwaniu wizyt z API
async function findScheduleWithAppointmentsFromAPI(syncCode = null, skipUnknownPatient = false, syncTomorrowAppointments = false) {
    console.log('KtoOstatni: Sprawdzam kolejne dni robocze w poszukiwaniu wizyt z API...');
    console.log('KtoOstatni: Używam kodu synchronizacji:', syncCode);
    console.log('KtoOstatni: Pomijaj nieznany pacjent:', skipUnknownPatient);
    console.log('KtoOstatni: Synchronizuj wizyty z jutro:', syncTomorrowAppointments);

    // 1. Najpierw pobierz słownik wszystkich lekarzy (raz)
    const doctorsDictionary = await fetchDoctorsDictionary();
    if (!doctorsDictionary || doctorsDictionary.length === 0) {
        console.error('KtoOstatni: Nie udało się pobrać słownika lekarzy');
        return null;
    }

    // Zawsze pobierz aktualną datę (ważne dla zmiany dnia o północy)
    const today = getCurrentDate();
    // Jeśli włączona synchronizacja wizyt z jutro, sprawdź więcej dni
    const maxDaysToCheck = syncTomorrowAppointments ? 10 : 5;

    for (let dayOffset = 0; dayOffset < maxDaysToCheck; dayOffset++) {
        const checkDate = new Date(today);
        checkDate.setDate(today.getDate() + dayOffset);

        // Pomiń weekendy (sobota = 6, niedziela = 0)
        if (checkDate.getDay() === 0 || checkDate.getDay() === 6) {
            console.log(`KtoOstatni: Pomijam weekend: ${checkDate.toLocaleDateString('pl-PL')}`);
            continue;
        }

        console.log(`KtoOstatni: Sprawdzam dzień ${dayOffset}: ${checkDate.toLocaleDateString('pl-PL')}`);

        try {
            // 2. Pobierz ID lekarzy pracujących w tym dniu
            console.log(`KtoOstatni: Pobieram ID lekarzy pracujących w dniu ${checkDate.toLocaleDateString('pl-PL')}`);
            const workingDoctorIds = await fetchWorkingDoctorIds(checkDate);
            console.log(`KtoOstatni: Znaleziono ${workingDoctorIds.length} lekarzy pracujących w dniu ${checkDate.toLocaleDateString('pl-PL')}:`, workingDoctorIds);

            if (!workingDoctorIds || workingDoctorIds.length === 0) {
                console.warn(`KtoOstatni: Brak lekarzy pracujących w dniu ${checkDate.toLocaleDateString('pl-PL')}`);
                continue;
            }

            // 3. Pobierz wizyty z API dla tego dnia i pracujących lekarzy
            const scheduleData = await fetchScheduleFromAPI(doctorsDictionary, workingDoctorIds, checkDate, syncCode, skipUnknownPatient, syncTomorrowAppointments);

            if (scheduleData) {
                console.log(`KtoOstatni: Znaleziono dane dla dnia ${checkDate.toLocaleDateString('pl-PL')}`);
                return scheduleData; // Zwracamy dane nawet jeśli nie ma wizyt
            } else {
                console.log(`KtoOstatni: Brak danych dla dnia ${checkDate.toLocaleDateString('pl-PL')}`);
                // Kontynuuj sprawdzanie kolejnych dni
            }
        } catch (error) {
            console.warn(`KtoOstatni: Błąd sprawdzania dnia ${checkDate.toLocaleDateString('pl-PL')}:`, error);
        }
    }

    console.log('KtoOstatni: Nie znaleziono wizyt w kolejnych dniach roboczych');
    
    // Zwróć dane dla pierwszego dnia roboczego, nawet jeśli nie ma wizyt
    const firstWorkingDay = getCurrentDate();
    let dayOffset = 0;
    while (dayOffset < maxDaysToCheck) {
        const checkDate = new Date(firstWorkingDay);
        checkDate.setDate(firstWorkingDay.getDate() + dayOffset);
        
        // Pomiń weekendy
        if (checkDate.getDay() !== 0 && checkDate.getDay() !== 6) {
            console.log(`KtoOstatni: Zwracam dane dla pierwszego dnia roboczego: ${checkDate.toLocaleDateString('pl-PL')}`);
            
            try {
                // Pobierz ID lekarzy pracujących w tym dniu
                const workingDoctorIds = await fetchWorkingDoctorIds(checkDate);
                
                if (workingDoctorIds && workingDoctorIds.length > 0) {
                    // Pobierz wizyty z API dla tego dnia
                    const scheduleData = await fetchScheduleFromAPI(doctorsDictionary, workingDoctorIds, checkDate, syncCode);
                    if (scheduleData) {
                        return scheduleData;
                    }
                } else {
                    // Stwórz dane z pustymi listami wizyt
                    const scheduleData = await fetchScheduleFromAPI(doctorsDictionary, [], checkDate, syncCode);
                    if (scheduleData) {
                        return scheduleData;
                    }
                }
            } catch (error) {
                console.warn(`KtoOstatni: Błąd tworzenia danych dla dnia ${checkDate.toLocaleDateString('pl-PL')}:`, error);
            }
        }
        dayOffset++;
    }
    
    return null;
}

// Funkcja do pobierania wizyt z API iGabinet dla konkretnej daty
async function fetchScheduleFromAPI(doctorsDictionary, workingDoctorIds, date, syncCode = null, skipUnknownPatient = false, syncTomorrowAppointments = false) {
    console.log(`KtoOstatni: Pobieranie wizyt z API dla daty: ${date.toISOString()}`);
    console.log('KtoOstatni: Słownik lekarzy:', doctorsDictionary.length, 'lekarzy');
    console.log('KtoOstatni: ID lekarzy pracujących:', workingDoctorIds);
    console.log('KtoOstatni: Kod synchronizacji do użycia:', syncCode);
    console.log('KtoOstatni: Pomijaj nieznany pacjent:', skipUnknownPatient);
    console.log('KtoOstatni: Synchronizuj wizyty z jutro:', syncTomorrowAppointments);

    try {
        // Sprawdź czy jesteśmy na stronie iGabinet
        if (!window.location.href.includes('igabinet.pl')) {
            console.log('KtoOstatni: Nie jesteśmy na stronie iGabinet, używam danych testowych');
            return createTestScheduleData(doctorsDictionary, workingDoctorIds, date, syncCode);
        }

        // Format YYYY-MM-DD dla API
        const formattedDate = date.toISOString().split('T')[0];

        // Jeśli nie ma lekarzy pracujących, zwróć dane z pustymi listami wizyt
        if (!workingDoctorIds || workingDoctorIds.length === 0) {
            console.log(`KtoOstatni: Brak lekarzy pracujących w dniu ${formattedDate}, zwracam dane z pustymi listami wizyt`);
            return processAPIScheduleData({ success: true, data: { visits: [], reservations: [], notes: [] } }, doctorsDictionary, [], date, syncCode);
        }

        console.log(`KtoOstatni: Pobieranie wizyt dla daty: ${formattedDate}`);
        console.log('KtoOstatni: Wywołuję API getEntities z body:', JSON.stringify({
            "section": "getEntities",
            "start_date": formattedDate,
            "end_date": formattedDate,
            "products": workingDoctorIds
        }));

        // Wywołaj API getEntities
        const response = await fetch("https://sonokard.igabinet.pl/admin/request/work_schedule_request.php", {
            method: 'POST',
            headers: {
                'accept': 'application/json',
                'accept-language': 'pl-PL,pl;q=0.9,en-US;q=0.8,en;q=0.7',
                'content-type': 'application/json',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin'
            },
            body: JSON.stringify({
                "section": "getEntities",
                "start_date": formattedDate,
                "end_date": formattedDate,
                "products": workingDoctorIds
            }),
            mode: 'cors',
            credentials: 'include'
        });

        if (!response.ok) {
            const errorText = await response.text().catch(() => '');
            console.error(`KtoOstatni: Błąd HTTP ${response.status} podczas pobierania wizyt:`, errorText);
            throw new Error(`HTTP error! status: ${response.status}, details: ${errorText}`);
        }

        const data = await response.json();
        console.log('KtoOstatni: Odpowiedź API getEntities:', data);

        // Przetwórz dane z API na format systemu KtoOstatni
        return processAPIScheduleData(data, doctorsDictionary, workingDoctorIds, date, syncCode, skipUnknownPatient, syncTomorrowAppointments);

    } catch (error) {
        console.error('KtoOstatni: Błąd pobierania wizyt z API:', error);
        throw error;
    }
}

// Funkcja do przetwarzania danych z API na format systemu KtoOstatni
function processAPIScheduleData(apiData, doctorsDictionary, workingDoctorIds, date, syncCode = null, skipUnknownPatient = false, syncTomorrowAppointments = false) {
    console.log('KtoOstatni: Przetwarzam dane z API...');
    console.log('KtoOstatni: Słownik lekarzy:', doctorsDictionary.length);
    console.log('KtoOstatni: ID lekarzy pracujących:', workingDoctorIds);
    console.log('KtoOstatni: Kod synchronizacji do użycia:', syncCode);
    console.log('KtoOstatni: Pomijaj nieznany pacjent:', skipUnknownPatient);
    console.log('KtoOstatni: Synchronizuj wizyty z jutro:', syncTomorrowAppointments);

    const doctors = [];
    const doctorMap = new Map();

    // Stwórz mapę WSZYSTKICH lekarzy ze słownika (nie tylko pracujących)
    console.log('KtoOstatni: Tworzę mapę WSZYSTKICH lekarzy ze słownika');
    doctorsDictionary.forEach(doctorFromDictionary => {
        const id = parseInt(doctorFromDictionary.id);
        if (!isNaN(id)) {
            const doctorName = doctorFromDictionary.name || `Lekarz ID ${id}`;

            doctorMap.set(id, {
                id: id,
                name: doctorName,
                deleted: doctorFromDictionary.deleted || "0"
            });

            doctors.push({
                doctorId: `${id}`,
                doctorName: doctorName,
                appointments: []
            });

            console.log(`KtoOstatni: Dodano lekarza ze słownika: ID ${id} - ${doctorName}`);
        }
    });

    // Dodaj lekarzy pracujących, którzy nie są w słowniku (fallback)
    workingDoctorIds.forEach(doctorId => {
        const id = parseInt(doctorId);
        if (!isNaN(id) && !doctorMap.has(id)) {
            console.warn(`KtoOstatni: Lekarz ID ${id} pracuje ale nie ma go w słowniku - dodaję z domyślną nazwą`);
            const defaultName = `Lekarz ID ${id}`;

            doctorMap.set(id, {
                id: id,
                name: defaultName,
                deleted: "0"
            });

            doctors.push({
                doctorId: `${id}`,
                doctorName: defaultName,
                appointments: []
            });
        }
    });

    console.log('KtoOstatni: Mapa lekarzy:', Array.from(doctorMap.entries()));
    let totalAppointments = 0;

    // Przetwórz wizyty z API
    // Struktura odpowiedzi API: { success: true, data: { visits: [...], reservations: [...], notes: [...] } }
    if (apiData.success && apiData.data) {
        const apiDataContent = apiData.data;

        // Przetwórz wizyty (visits)
        if (Array.isArray(apiDataContent.visits)) {
            console.log(`KtoOstatni: Znaleziono ${apiDataContent.visits.length} wizyt w odpowiedzi API`);

            apiDataContent.visits.forEach((visit, index) => {
                try {
                    console.log(`KtoOstatni: Przetwarzam wizytę ${index + 1}:`, {
                        id: visit.id,
                        product_id: visit.product?.id,
                        patient: visit.user?.name,
                        start: visit.start_date,
                        end: visit.end_date
                    });

                    // Użyj product.id z wizyty
                    const productId = visit.product?.id;
                    if (!productId) {
                        console.warn('KtoOstatni: Wizyta nie ma product.id:', visit);
                        return;
                    }

                    // Znajdź lekarza w mapie
                    const doctor = doctorMap.get(parseInt(productId));
                    if (!doctor) {
                        console.warn(`KtoOstatni: Nie znaleziono lekarza o ID ${productId} w mapie lekarzy`);
                        return;
                    }

                    // Przetwórz wizytę
                    const processedAppointment = processVisitData(visit);
                    if (processedAppointment) {
                        // Znajdź lekarza w tablicy doctors
                        const doctorIndex = doctors.findIndex(d => parseInt(d.doctorId) === parseInt(productId));
                        if (doctorIndex !== -1) {
                            doctors[doctorIndex].appointments.push(processedAppointment);
                            totalAppointments++;
                            console.log(`KtoOstatni: Dodano wizytę do lekarza ${doctor.name}`);
                        } else {
                            console.warn(`KtoOstatni: Nie znaleziono lekarza o ID ${productId} w tablicy doctors`);
                        }
                    }
                } catch (error) {
                    console.warn('KtoOstatni: Błąd przetwarzania wizyty:', error, visit);
                }
            });
        } else {
            console.log('KtoOstatni: Brak wizyt w odpowiedzi API');
        }

        // Przetwórz rezerwacje (reservations)
        if (Array.isArray(apiDataContent.reservations) && apiDataContent.reservations.length > 0) {
            console.log(`KtoOstatni: Znaleziono ${apiDataContent.reservations.length} rezerwacji w odpowiedzi API`);

            apiDataContent.reservations.forEach((reservation, index) => {
                try {
                    console.log(`KtoOstatni: Przetwarzam rezerwację ${index + 1}:`, {
                        id: reservation.id,
                        product_id: reservation.product?.id,
                        patient: reservation.user?.name,
                        start: reservation.start_date,
                        end: reservation.end_date
                    });

                    // Użyj product.id z rezerwacji
                    const productId = reservation.product?.id;
                    if (!productId) {
                        console.warn('KtoOstatni: Rezerwacja nie ma product.id:', reservation);
                        return;
                    }

                    // Znajdź lekarza w mapie
                    const doctor = doctorMap.get(parseInt(productId));
                    if (!doctor) {
                        console.warn(`KtoOstatni: Nie znaleziono lekarza o ID ${productId} w mapie lekarzy`);
                        return;
                    }

                    // Przetwórz rezerwację (używamy tej samej funkcji co dla wizyt)
                    const processedAppointment = processVisitData(reservation);
                    if (processedAppointment) {
                        // Znajdź lekarza w tablicy doctors
                        const doctorIndex = doctors.findIndex(d => parseInt(d.doctorId) === parseInt(productId));
                        if (doctorIndex !== -1) {
                            doctors[doctorIndex].appointments.push(processedAppointment);
                            totalAppointments++;
                            console.log(`KtoOstatni: Dodano rezerwację do lekarza ${doctor.name}`);
                        } else {
                            console.warn(`KtoOstatni: Nie znaleziono lekarza o ID ${productId} w tablicy doctors`);
                        }
                    }
                } catch (error) {
                    console.warn('KtoOstatni: Błąd przetwarzania rezerwacji:', error, reservation);
                }
            });
        }
    } else {
        console.warn('KtoOstatni: Nieprawidłowa struktura odpowiedzi API:', apiData);
    }

    // Upewnij się, że każdy lekarz ma pole appointments, nawet jeśli jest puste
    doctors.forEach(doctor => {
        if (!doctor.appointments) {
            doctor.appointments = [];
        }
    });

    // Przygotuj dane w nowym formacie
    const formattedDate = date.toISOString().split('T')[0]; // Format YYYY-MM-DD

    // Przygotuj dane w nowym formacie
    const daysData = [{
        date: formattedDate,
        doctors: doctors.map(doctor => ({
            doctorId: doctor.doctorId,
            doctorName: doctor.doctorName,
            appointments: doctor.appointments
                .filter(appointment => {
                    // Filtruj nieznanych pacjentów jeśli włączona opcja
                    if (skipUnknownPatient) {
                        const fullName = `${appointment.patientFirstName} ${appointment.patientLastName}`.trim();
                        const isUnknownPatient = fullName === 'Nieznany pacjent';

                        if (isUnknownPatient) {
                            console.log(`KtoOstatni: Pomijam nieznany pacjent: ${fullName}`);
                            return false;
                        }
                    }
                    return true;
                })
                .map(appointment => {
                    // Sprawdź czy mamy statusTag dla tego appointmentId
                    const statusTag = getStatusTagForAppointment(appointment.appointmentId);

                    // Oblicz nowe pola statusów
                    const isConfirmed = appointment.resConfirmed ? 1 : 0;
                    const isPatientPresent = calculatePatientPresence(statusTag);
                    const isCompleted = (appointment.locked || appointment.visit) ? 1 : 0;

                    return {
                        appointmentId: appointment.appointmentId,
                        patientFirstName: appointment.patientFirstName,
                        patientLastName: appointment.patientLastName,
                        appointmentStart: appointment.appointmentStart,
                        appointmentDuration: appointment.appointmentDuration,
                        phone_number: appointment.mobile || '',
                        status: 'waiting', // Domyślny status, zostanie zaktualizowany później
                        is_confirmed: isConfirmed,
                        is_patient_present: isPatientPresent,
                        is_completed: isCompleted,
                        is_sms_sent: 0 // Domyślnie 0, może być aktualizowane przez system
                    };
                })
        }))
    }];

    // Oblicz statusy wizyt dla każdego lekarza
    daysData.forEach(day => {
        day.doctors.forEach(doctor => {
            calculateAppointmentStatuses(doctor.appointments);
        });
    });

    // Stwórz obiekt wynikowy w nowym formacie
    const result = {
        exportDate: new Date().toISOString(),
        syncCode: syncCode || "igab1234567890123", // Użyj przekazanego kodu lub domyślnego dla Sonokard
        syncData: {
            days: daysData
        }
    };

    console.log('KtoOstatni: Używam kodu synchronizacji w wyniku:', result.syncCode);

    console.log('KtoOstatni: Przetworzone dane w nowym formacie:', result);
    return result;
}

// Funkcja do przetwarzania danych wizyty/rezerwacji z API iGabinet
function processVisitData(visitData) {
    try {
        // Sprawdź wymagane pola
        if (!visitData.id || !visitData.start_date || !visitData.end_date) {
            console.warn('KtoOstatni: Wizyta nie ma wymaganych pól:', visitData);
            return null;
        }

        // Parsuj daty
        const startDate = new Date(visitData.start_date);
        const endDate = new Date(visitData.end_date);

        // Formatuj czasy
        const startTime = startDate.toLocaleTimeString('pl-PL', {
            hour: '2-digit',
            minute: '2-digit'
        });
        const endTime = endDate.toLocaleTimeString('pl-PL', {
            hour: '2-digit',
            minute: '2-digit'
        });

        // Oblicz czas trwania w minutach
        const durationMinutes = Math.round((endDate - startDate) / 60000);

        // Wyciągnij dane pacjenta
        let patientFirstName = 'Pacjent';
        let patientLastName = '';

        if (visitData.user) {
            if (visitData.user.first_name && visitData.user.last_name) {
                patientFirstName = visitData.user.first_name;
                patientLastName = visitData.user.last_name;
            } else if (visitData.user.name) {
                // Podziel pełną nazwę na imię i nazwisko
                const nameParts = visitData.user.name.trim().split(' ');
                patientFirstName = nameParts[0] || 'Pacjent';
                patientLastName = nameParts.slice(1).join(' ') || '';
            }
        }

        // Wyciągnij dodatkowe informacje o pacjencie
        let mobile = null;
        let resConfirmed = false;
        let locked = false;
        let visit = false;

        if (visitData.user) {
            mobile = visitData.user.mobile || null;
        }

        // Sprawdź czy rezerwacja jest potwierdzona
        resConfirmed = visitData.res_confirmed || false;

        // Sprawdź czy wizyta jest zablokowana (locked) - oznacza że lekarz zakończył i zamknął wizytę
        locked = visitData.locked || false;

        // Sprawdź czy to jest wizyta (visit) czy rezerwacja
        visit = visitData.visit || false;

        // Sprawdź czy mamy statusTag dla tego appointmentId
        const statusTag = getStatusTagForAppointment(visitData.id.toString());

        const processedAppointment = {
            appointmentId: visitData.id.toString(),
            patientFirstName: patientFirstName,
            patientLastName: patientLastName,
            appointmentStart: startTime,
            visit: visit,
            appointmentDuration: durationMinutes,
            mobile: mobile,
            resConfirmed: resConfirmed,
            locked: locked,
            statusTag: statusTag // Używamy statusTag zamiast notes
        };

        console.log('KtoOstatni: Przetworzona wizyta/rezerwacja:', processedAppointment);
        return processedAppointment;

    } catch (error) {
        console.error('KtoOstatni: Błąd przetwarzania danych wizyty:', error, visitData);
        return null;
    }
}

// Funkcje pomocnicze do formatowania danych
function extractFirstName(fullName) {
    // Usuń przedrostki jak "Wizyta: " lub "Rezerwacja: "
    const cleanName = fullName.replace(/^(Wizyta|Rezerwacja):\s*/i, '').trim();

    // Podziel na części i zwróć pierwszą część jako imię
    const parts = cleanName.split(' ');
    return parts.length > 0 ? parts[0] : 'Pacjent';
}

function extractLastName(fullName) {
    // Usuń przedrostki jak "Wizyta: " lub "Rezerwacja: "
    const cleanName = fullName.replace(/^(Wizyta|Rezerwacja):\s*/i, '').trim();

    // Podziel na części i zwróć pozostałe części jako nazwisko
    const parts = cleanName.split(' ');
    return parts.length > 1 ? parts.slice(1).join(' ') : '';
}

function calculateEndTime(startTime) {
    // Domyślna długość wizyty: 20 minut
    const [hours, minutes] = startTime.split(':').map(Number);
    let endMinutes = minutes + 20;
    let endHours = hours;

    if (endMinutes >= 60) {
        endMinutes -= 60;
        endHours += 1;
    }

    return `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;
}

function calculateDuration(timeRange) {
    // Oblicz czas trwania wizyty w minutach
    const parts = timeRange.split(' - ');
    if (parts.length !== 2) return 20; // Domyślnie 20 minut

    const startParts = parts[0].split(':').map(Number);
    const endParts = parts[1].split(':').map(Number);

    const startMinutes = startParts[0] * 60 + startParts[1];
    const endMinutes = endParts[0] * 60 + endParts[1];

    return endMinutes - startMinutes;
}





// Funkcja do tworzenia danych testowych
function createTestScheduleData(doctorsDictionary, workingDoctorIds, date, syncCode = null) {
    console.log('KtoOstatni: Tworzę dane testowe dla daty:', date.toLocaleDateString('pl-PL'));
    console.log('KtoOstatni: Słownik lekarzy:', doctorsDictionary.length);
    console.log('KtoOstatni: ID lekarzy pracujących:', workingDoctorIds);
    console.log('KtoOstatni: Kod synchronizacji do użycia:', syncCode);

    // Stwórz WSZYSTKICH lekarzy ze słownika (nie tylko pracujących)
    const doctors = doctorsDictionary.map((doctorFromDictionary) => {
        const id = parseInt(doctorFromDictionary.id);
        const doctorName = doctorFromDictionary.name || `Lekarz ID ${id}`;

        return {
            doctorId: `${id}`,
            doctorName: doctorName,
            appointments: []
        };
    });

    // Dodaj lekarzy pracujących, którzy nie są w słowniku (fallback)
    workingDoctorIds.forEach(doctorId => {
        const id = parseInt(doctorId);
        if (!isNaN(id) && !doctors.find(d => parseInt(d.doctorId) === id)) {
            console.warn(`KtoOstatni: Lekarz ID ${id} pracuje ale nie ma go w słowniku - dodaję z domyślną nazwą`);
            doctors.push({
                doctorId: `${id}`,
                doctorName: `Lekarz ID ${id}`,
                appointments: []
            });
        }
    });

    // Dodaj przykładowe wizyty tylko dla pierwszego lekarza ze słownika
    if (doctors.length > 0) {
        const testAppointmentId = `test_${Date.now()}`;
        
        // Symuluj statusTag dla testowej wizyty
        appointmentStatusTags.set(testAppointmentId, 'tags_reservation_test_123');
        
        doctors[0].appointments.push({
            appointmentId: testAppointmentId,
            patientFirstName: 'Test',
            patientLastName: 'Pacjent',
            appointmentStart: '09:00',
            visit: false,
            appointmentDuration: 30,
            mobile: '+48 123 456 789',
            resConfirmed: true,
            locked: false,
            statusTag: 'tags_reservation_test_123'
        });
    }

    // Przygotuj dane w nowym formacie
    const formattedDate = date.toISOString().split('T')[0]; // Format YYYY-MM-DD

    const result = {
        exportDate: new Date().toISOString(),
        syncCode: syncCode || "igab1234567890123", // Użyj przekazanego kodu lub domyślnego dla Sonokard
        syncData: {
            days: [{
                date: formattedDate,
                doctors: doctors
            }]
        }
    };

    console.log('KtoOstatni: Dane testowe w nowym formacie z kodem:', result.syncCode);
    console.log('KtoOstatni: Pełne dane testowe:', result);
    return result;
}

// Funkcja do obliczania obecności pacjenta na podstawie statusTag
function calculatePatientPresence(statusTag) {
    if (!statusTag) return 0;

    // Sprawdź końcówkę statusTag po ostatnim _
    const parts = statusTag.split('_');
    const lastPart = parts[parts.length - 1];

    // Jeśli końcówka to 4 lub 7, pacjent jest obecny
    if (lastPart === '4' || lastPart === '7') {
        return 1;
    }

    return 0;
}

// Funkcja do obliczania statusów wizyt dla lekarza
function calculateAppointmentStatuses(appointments) {
    if (!appointments || appointments.length === 0) return;

    // Znajdź najnowszą wizytę z visit=true dla tego lekarza
    let currentAppointment = null;
    let latestVisitTime = null;

    appointments.forEach(appointment => {
        if (appointment.is_completed === 1) { // visit=true lub locked=true
            const appointmentTime = new Date(`2000-01-01 ${appointment.appointmentStart}`);

            if (!latestVisitTime || appointmentTime > latestVisitTime) {
                latestVisitTime = appointmentTime;
                currentAppointment = appointment;
            }
        }
    });

    // Ustaw statusy
    appointments.forEach(appointment => {
        if (appointment.is_completed === 1) {
            // Wizyta zakończona
            if (currentAppointment && appointment.appointmentId === currentAppointment.appointmentId) {
                // Najnowsza wizyta z visit=true to wizyta current
                appointment.status = 'current';
                appointment.is_completed = 0; // Aktualna wizyta nie jest completed
            } else {
                // Pozostałe wizyty z locked=true lub visit=true to closed
                appointment.status = 'closed';
            }
        } else {
            // Wizyta bez locked/visit to waiting
            appointment.status = 'waiting';
        }
    });
}
