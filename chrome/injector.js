(function() {
    // Komunikat informacyjny o uruchomieniu skryptu
    console.log('KtoOstatni: Inicjalizacja przechwytywania wiadomości WebSocket');

    // Upewnij się, że SubtleCrypto jest dostępne
    if (!window.crypto || !window.crypto.subtle) {
        console.error("KtoOstatni: Web Crypto API nie jest dostępne w tym kontekście.");
        return;
    }

    // 1. Zapisz oryginalną funkcję decrypt
    const originalDecrypt = window.crypto.subtle.decrypt;
    console.log("KtoOstatni: Oryginalna funkcja crypto.subtle.decrypt została zapisana.");

    // 2. Stw<PERSON>rz naszą "opakowującą" (wrapper) funkcję
    window.crypto.subtle.decrypt = function(...args) {
        // 3. Wywołaj oryginalną funkcję, aby uzyskać obietnicę (Promise)
        const decryptPromise = originalDecrypt.apply(this, args);

        // 4. <PERSON><PERSON><PERSON><PERSON> wynik, gdy obietnica zostanie rozwiązana
        decryptPromise.then(decryptedBuffer => {
            try {
                // Konwertuj ArrayBuffer na string
                const decryptedString = new TextDecoder().decode(decryptedBuffer);

                // Spróbuj sparsować jako JSON
                try {
                    const decryptedJson = JSON.parse(decryptedString);
                    
                    // Wyświetl odszyfrowaną wiadomość w konsoli
                    console.log("KtoOstatni: Odszyfrowana wiadomość WebSocket:", decryptedJson);
                    
                    // Dodatkowa analiza struktury wiadomości (jeśli to wiadomość WebSocket)
                    if (decryptedJson && typeof decryptedJson === 'object') {
                        if (decryptedJson.type || decryptedJson.action || decryptedJson.event) {
                            console.log("KtoOstatni: Wykryto wiadomość WebSocket typu:", 
                                decryptedJson.type || decryptedJson.action || decryptedJson.event);
                        }
                    }
                } catch (jsonError) {
                    // Jeśli nie jest to JSON, wyświetl surowe dane jako tekst
                    if (decryptedString.length < 1000) {
                        console.log("KtoOstatni: Odszyfrowane dane (nie JSON):", decryptedString);
                    } else {
                        console.log("KtoOstatni: Odszyfrowane dane (nie JSON, zbyt długie do wyświetlenia)");
                    }
                }
            } catch (err) {
                // Ignorujemy błędy, aby nie zepsuć działania strony
                console.log("KtoOstatni: Nie udało się przetworzyć odszyfrowanych danych");
            }
        }).catch(err => {
            // Ignorujemy błędy deszyfrowania, aby nie zaśmiecać konsoli
        });

        // 5. Zwróć ORYGINALNĄ obietnicę, aby strona działała poprawnie
        return decryptPromise;
    };

    console.log("KtoOstatni: Funkcja crypto.subtle.decrypt została nadpisana.");

    // Dodatkowe przechwytywanie WebSocket - na wszelki wypadek, gdyby niektóre wiadomości nie były szyfrowane
    const originalWebSocketSend = WebSocket.prototype.send;
    WebSocket.prototype.send = function(data) {
        try {
            if (typeof data === 'string') {
                try {
                    const jsonData = JSON.parse(data);
                    console.log("KtoOstatni: Wysłana wiadomość WebSocket:", jsonData);
                } catch (e) {
                    // Jeśli to nie JSON, wyświetl tylko jeśli jest krótkie
                    if (data.length < 1000) {
                        console.log("KtoOstatni: Wysłane dane WebSocket (nie JSON):", data);
                    }
                }
            } else if (data instanceof ArrayBuffer || data instanceof Blob) {
                console.log("KtoOstatni: Wysłane binarne dane WebSocket");
            }
        } catch (err) {
            // Ignorujemy błędy
        }
        
        // Wywołaj oryginalną funkcję
        return originalWebSocketSend.apply(this, arguments);
    };

    // Przechwytywanie onmessage dla WebSocket
    const originalWebSocketInstance = WebSocket;
    window.WebSocket = function(...args) {
        const socket = new originalWebSocketInstance(...args);
        
        const originalAddEventListener = socket.addEventListener;
        socket.addEventListener = function(type, listener, options) {
            if (type === 'message') {
                const wrappedListener = function(event) {
                    try {
                        // Próba przetworzenia danych jako JSON
                        if (typeof event.data === 'string') {
                            try {
                                const jsonData = JSON.parse(event.data);
                                console.log("KtoOstatni: Odebrana wiadomość WebSocket:", jsonData);
                            } catch (e) {
                                // Jeśli to nie JSON, wyświetl tylko jeśli jest krótkie
                                if (event.data.length < 1000) {
                                    console.log("KtoOstatni: Odebrane dane WebSocket (nie JSON):", event.data);
                                }
                            }
                        } else if (event.data instanceof ArrayBuffer || event.data instanceof Blob) {
                            console.log("KtoOstatni: Odebrane binarne dane WebSocket");
                        }
                    } catch (err) {
                        // Ignorujemy błędy
                    }
                    
                    // Wywołaj oryginalny listener
                    return listener.apply(this, arguments);
                };
                
                return originalAddEventListener.call(this, type, wrappedListener, options);
            }
            
            return originalAddEventListener.apply(this, arguments);
        };
        
        // Przechwytywanie bezpośredniego przypisania onmessage
        const originalOnMessageDescriptor = Object.getOwnPropertyDescriptor(WebSocket.prototype, 'onmessage');
        if (originalOnMessageDescriptor && originalOnMessageDescriptor.set) {
            Object.defineProperty(socket, 'onmessage', {
                set: function(listener) {
                    const wrappedListener = function(event) {
                        try {
                            // Próba przetworzenia danych jako JSON
                            if (typeof event.data === 'string') {
                                try {
                                    const jsonData = JSON.parse(event.data);
                                    console.log("KtoOstatni: Odebrana wiadomość WebSocket (onmessage):", jsonData);
                                } catch (e) {
                                    // Jeśli to nie JSON, wyświetl tylko jeśli jest krótkie
                                    if (event.data.length < 1000) {
                                        console.log("KtoOstatni: Odebrane dane WebSocket (onmessage, nie JSON):", event.data);
                                    }
                                }
                            } else if (event.data instanceof ArrayBuffer || event.data instanceof Blob) {
                                console.log("KtoOstatni: Odebrane binarne dane WebSocket (onmessage)");
                            }
                        } catch (err) {
                            // Ignorujemy błędy
                        }
                        
                        // Wywołaj oryginalny listener
                        return listener.apply(this, arguments);
                    };
                    
                    originalOnMessageDescriptor.set.call(this, wrappedListener);
                },
                get: originalOnMessageDescriptor.get
            });
        }
        
        return socket;
    };
    
    // Kopiowanie wszystkich właściwości i metod oryginalnego WebSocket
    for (const prop in originalWebSocketInstance) {
        if (originalWebSocketInstance.hasOwnProperty(prop)) {
            window.WebSocket[prop] = originalWebSocketInstance[prop];
        }
    }
    window.WebSocket.prototype = originalWebSocketInstance.prototype;
    
    console.log("KtoOstatni: Przechwytywanie WebSocket zostało skonfigurowane.");
})();
