// Background script dla <PERSON>toOstatni - Automatyczna Synchronizacja
console.log('KtoOstatni: Background script zała<PERSON>wan<PERSON>');

// Konfiguracja domyślna
const DEFAULT_CONFIG = {
    syncInterval: 15, // minuty
    isEnabled: false,
    syncCode: '', // Kod synchronizacji do wpisania przez użytkownika
    serverUrl: '', // Adres serwera do wpisania przez użytkownika
    lastSync: null,
    autoSync: false,
    skipUnknownPatient: false, // Pomijaj "Nieznany pacjent"
    syncTomorrowAppointments: false, // Synchronizuj wizyty z jutro
    autoClosePreviousDayAppointments: true, // Automatycznie zamykaj wizyty z poprzedniego dnia o północy
    lastSyncData: null // Cache ostatnio pobranych danych
};

// Inicjalizacja przy starcie
chrome.runtime.onStartup.addListener(async () => {
    console.log('KtoOstatni: Chrome startup - inicjalizacja');
    await initializeExtension();
});

chrome.runtime.onInstalled.addListener(async () => {
    console.log('KtoOstatni: Extension installed/updated - inicjalizacja');
    await initializeExtension();
});

// Inicjalizacja rozszerzenia
async function initializeExtension() {
    try {
        console.log('KtoOstatni: Rozpoczynam inicjalizację rozszerzenia...');

        const config = await getConfig();
        console.log('KtoOstatni: Aktualna konfiguracja:', {
            isEnabled: config.isEnabled,
            autoSync: config.autoSync,
            syncCode: config.syncCode ? `${config.syncCode.substring(0, 8)}...` : 'brak',
            serverUrl: config.serverUrl || 'brak',
            syncInterval: config.syncInterval,
            lastSync: config.lastSync,
            skipUnknownPatient: config.skipUnknownPatient
        });

        // Wyczyść stare alarmy na wszelki wypadek
        await chrome.alarms.clear('autoSync');
        console.log('KtoOstatni: Wyczyszczono stare alarmy');

        if (config.autoSync && config.isEnabled && config.syncCode && config.serverUrl) {
            console.log('KtoOstatni: Konfiguracja automatycznej synchronizacji...');
            await setupAutoSync(config.syncInterval);

            // Sprawdź czy alarm został utworzony
            const alarms = await chrome.alarms.getAll();
            const autoSyncAlarm = alarms.find(alarm => alarm.name === 'autoSync');
            if (autoSyncAlarm) {
                console.log('KtoOstatni: Alarm automatycznej synchronizacji aktywny:', {
                    scheduledTime: new Date(autoSyncAlarm.scheduledTime).toLocaleString(),
                    periodInMinutes: autoSyncAlarm.periodInMinutes
                });
            } else {
                console.warn('KtoOstatni: Alarm automatycznej synchronizacji nie został utworzony!');
            }
        } else {
            console.log('KtoOstatni: Automatyczna synchronizacja wyłączona:', {
                autoSync: config.autoSync,
                isEnabled: config.isEnabled,
                hasSyncCode: !!config.syncCode,
                hasServerUrl: !!config.serverUrl
            });
        }

        // Ustaw alarm zmiany dnia o północy
        await setupMidnightAlarm();

        console.log('KtoOstatni: Aplikacja skonfigurowana do pracy z własnym serwerem API');

        console.log('KtoOstatni: Inicjalizacja rozszerzenia zakończona pomyślnie');
    } catch (error) {
        console.error('KtoOstatni: Błąd inicjalizacji:', error);
    }
}

// Pobierz konfigurację z storage
async function getConfig() {
    const result = await chrome.storage.local.get(DEFAULT_CONFIG);
    const config = { ...DEFAULT_CONFIG, ...result };
    return config;
}

// Zapisz konfigurację
async function saveConfig(config) {
    await chrome.storage.local.set(config);
    console.log('KtoOstatni: Konfiguracja zapisana:', {
        ...config,
        syncCode: config.syncCode ? `${config.syncCode.substring(0, 8)}...` : 'brak'
    });
}

// Funkcja do porównywania danych z cache (localStorage)
function hasDataChanged(newData, cachedData) {
    // Jeśli nie przekazano cachedData, spróbuj pobrać z localStorage
    if (!cachedData) {
        try {
            const storedData = localStorage.getItem('ktoostatni_lastSyncData');
            if (storedData) {
                cachedData = JSON.parse(storedData);
            }
        } catch (error) {
            console.error('KtoOstatni: Błąd odczytu z localStorage podczas porównywania:', error);
        }
    }

    if (!cachedData) {
        console.log('KtoOstatni: Brak danych w localStorage - wysyłam dane');
        return true;
    }

    const newDataString = JSON.stringify(newData);
    const cachedDataString = JSON.stringify(cachedData);

    const hasChanged = newDataString !== cachedDataString;
    console.log('KtoOstatni: Porównanie danych z localStorage:', {
        hasChanged: hasChanged,
        newDataSize: newDataString.length,
        cachedDataSize: cachedDataString.length
    });

    // Dodatkowe logowanie dla debugowania
    if (!hasChanged) {
        console.log('KtoOstatni: Dane są identyczne z localStorage - szczegóły:', {
            newDataHash: newDataString.substring(0, 100) + '...',
            cachedDataHash: cachedDataString.substring(0, 100) + '...'
        });
    }

    return hasChanged;
}

// Zapisz dane do cache (localStorage)
async function saveSyncDataToCache(data) {
    try {
        // Sprawdź czy localStorage jest dostępne
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem('ktoostatni_lastSyncData', JSON.stringify(data));
            console.log('KtoOstatni: Dane zapisane do localStorage');
        } else {
            console.error('KtoOstatni: localStorage nie jest dostępne');
        }
    } catch (error) {
        console.error('KtoOstatni: Błąd zapisu danych do localStorage:', error);
        
        // Jeśli localStorage jest pełne, spróbuj wyczyścić stare dane i zapisać ponownie
        if (error.name === 'QuotaExceededError') {
            console.log('KtoOstatni: localStorage pełny, próbuję wyczyścić stare dane...');
            try {
                // Wyczyść stare dane dzienne
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('sync_data_')) {
                        keysToRemove.push(key);
                    }
                }
                
                // Usuń najstarsze dane (połowa)
                keysToRemove.slice(0, Math.ceil(keysToRemove.length / 2)).forEach(key => {
                    localStorage.removeItem(key);
                });
                
                // Spróbuj zapisać ponownie
                localStorage.setItem('ktoostatni_lastSyncData', JSON.stringify(data));
                console.log('KtoOstatni: Dane zapisane do localStorage po wyczyszczeniu');
            } catch (retryError) {
                console.error('KtoOstatni: Nie udało się zapisać danych nawet po wyczyszczeniu localStorage:', retryError);
            }
        }
    }
}

// Wyczyść cache (localStorage)
async function clearSyncCache() {
    try {
        // Sprawdź czy localStorage jest dostępne
        if (typeof localStorage !== 'undefined') {
            localStorage.removeItem('ktoostatni_lastSyncData');
            
            // Czyść również wszystkie dane dzienne z localStorage
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('sync_data_')) {
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
                console.log('KtoOstatni: Usunięto klucz z localStorage:', key);
            });
            
            console.log('KtoOstatni: Cache wyczyszczony z localStorage');
        } else {
            console.error('KtoOstatni: localStorage nie jest dostępne');
        }
    } catch (error) {
        console.error('KtoOstatni: Błąd czyszczenia cache z localStorage:', error);
    }
}

// Konfiguracja automatycznej synchronizacji
async function setupAutoSync(intervalMinutes) {
    try {
        // Wyczyść istniejące alarmy
        await chrome.alarms.clear('autoSync');

        if (intervalMinutes > 0) {
            // Utwórz nowy alarm
            await chrome.alarms.create('autoSync', {
                delayInMinutes: intervalMinutes,
                periodInMinutes: intervalMinutes
            });
            console.log(`KtoOstatni: Auto-sync skonfigurowany na ${intervalMinutes} minut`);
        }
    } catch (error) {
        console.error('KtoOstatni: Błąd konfiguracji auto-sync:', error);
    }
}

// Obsługa alarmów
chrome.alarms.onAlarm.addListener(async (alarm) => {
    if (alarm.name === 'autoSync') {
        console.log('KtoOstatni: Uruchamiam automatyczną synchronizację');
        await performAutoSync();
    } else if (alarm.name === 'midnightDayChange') {
        console.log('KtoOstatni: Alarm zmiany dnia o północy...');
        await handleMidnightDayChange();
    }
});

// Ustaw alarm zmiany dnia o północy
async function setupMidnightAlarm() {
    try {
        // Wyczyść istniejący alarm północy
        await chrome.alarms.clear('midnightDayChange');

        // Oblicz czas do najbliższej północy
        const now = new Date();
        const midnight = new Date(now);
        midnight.setHours(24, 0, 0, 0); // Następna północ

        const minutesToMidnight = Math.ceil((midnight.getTime() - now.getTime()) / 60000);

        console.log('KtoOstatni: Ustawiam alarm zmiany dnia o północy:', {
            currentTime: now.toLocaleString(),
            nextMidnight: midnight.toLocaleString(),
            minutesToMidnight: minutesToMidnight
        });

        // Ustaw alarm na najbliższą północ, potem co 24 godziny
        await chrome.alarms.create('midnightDayChange', {
            delayInMinutes: minutesToMidnight,
            periodInMinutes: 24 * 60 // Co 24 godziny
        });

        console.log('KtoOstatni: Alarm zmiany dnia ustawiony pomyślnie');
    } catch (error) {
        console.error('KtoOstatni: Błąd ustawiania alarmu północy:', error);
    }
}

// Obsługa zmiany dnia o północy
async function handleMidnightDayChange() {
    try {
        console.log('KtoOstatni: === ZMIANA DNIA O PÓŁNOCY ===');
        const now = new Date();
        console.log('KtoOstatni: Aktualna data/czas:', now.toLocaleString());

        const config = await getConfig();

        // Krok 1: Zamknij wszystkie wizyty z poprzedniego dnia (jeśli włączone)
        if (config.isEnabled && config.syncCode && config.serverUrl && config.autoClosePreviousDayAppointments) {
            console.log('KtoOstatni: Zamykam wszystkie wizyty z poprzedniego dnia...');
            await closeAllPreviousDayAppointments(config);
        } else if (!config.autoClosePreviousDayAppointments) {
            console.log('KtoOstatni: Automatyczne zamykanie wizyt z poprzedniego dnia wyłączone w ustawieniach');
        }

        // Krok 2: Wyczyść cache z poprzedniego dnia
        console.log('KtoOstatni: Czyszczę cache z poprzedniego dnia...');
        await clearPreviousDayCache();

        // Krok 3: Ustaw nowy alarm na następną północ
        await setupMidnightAlarm();

        console.log('KtoOstatni: === ZMIANA DNIA ZAKOŃCZONA ===');

    } catch (error) {
        console.error('KtoOstatni: Błąd podczas zmiany dnia:', error);
    }
}

// Zamknij wszystkie wizyty z poprzedniego dnia
async function closeAllPreviousDayAppointments(config) {
    try {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const yesterdayStr = yesterday.toISOString().split('T')[0];

        console.log('KtoOstatni: Robię porządek - zamykam wszystkie wizyty za dzień:', yesterdayStr);

        // Pobierz cache za poprzedni dzień
        const cacheKey = `sync_data_${yesterdayStr}`;
        const cachedData = await getCachedData(cacheKey);

        if (!cachedData || !cachedData.syncData || !cachedData.syncData.days) {
            console.log('KtoOstatni: Brak danych w cache za dzień:', yesterdayStr);
            return;
        }

        // Zamknij wszystkie wizyty z cache
        const closedData = closeAllAppointmentsInData(cachedData);

        // Wyślij ostatnią aktualizację z pozamykanymi wizytami
        if (closedData && closedData.syncData && closedData.syncData.days && closedData.syncData.days.length > 0) {
            console.log('KtoOstatni: Wysyłam ostatnią aktualizację z pozamykanymi wizytami za dzień:', yesterdayStr);
            const result = await sendDataToSystem(config.syncCode, closedData, config.serverUrl);

            if (result.success) {
                console.log('KtoOstatni: Ostatnia aktualizacja za dzień', yesterdayStr, 'zakończona pomyślnie');
            } else {
                console.error('KtoOstatni: Błąd ostatniej aktualizacji za dzień', yesterdayStr, ':', result.error);
            }
        }

        console.log('KtoOstatni: Zakończono porządek za poprzedni dzień');

    } catch (error) {
        console.error('KtoOstatni: Błąd podczas porządku za poprzedni dzień:', error);
    }
}

// Wyczyść cache z poprzedniego dnia (localStorage)
async function clearPreviousDayCache() {
    try {
        // Wyczyść cache danych synchronizacji z localStorage
        localStorage.removeItem('ktoostatni_lastSyncData');

        console.log('KtoOstatni: Cache z poprzedniego dnia wyczyszczony z localStorage');

    } catch (error) {
        console.error('KtoOstatni: Błąd czyszczenia cache z localStorage:', error);
    }
}

// Wykonaj ostatnią synchronizację za określony dzień
// Zamknij wszystkie wizyty w danych (dla porządku o północy)
function closeAllAppointmentsInData(data) {
    try {
        console.log('KtoOstatni: Zamykam wszystkie wizyty w danych');

        if (!data || !data.syncData || !data.syncData.days) {
            console.log('KtoOstatni: Brak danych do zamknięcia');
            return data;
        }

        // Deep copy danych
        const closedData = JSON.parse(JSON.stringify(data));
        let closedCount = 0;

        closedData.syncData.days.forEach(day => {
            if (day.doctors) {
                day.doctors.forEach(doctor => {
                    if (doctor.appointments) {
                        doctor.appointments.forEach(appointment => {
                            // Zamknij wizytę jeśli nie jest już zamknięta
                            if (appointment.status !== 'closed' && appointment.is_completed !== 1) {
                                appointment.status = 'closed';
                                appointment.is_completed = 1;
                                appointment.is_confirmed = 1;
                                closedCount++;
                            }
                        });
                    }
                });
            }
        });

        console.log('KtoOstatni: Zamknięto', closedCount, 'wizyt');
        return closedData;

    } catch (error) {
        console.error('KtoOstatni: Błąd podczas zamykania wszystkich wizyt:', error);
        return data;
    }
}

// Sprawdź czy jakieś wizyty zniknęły z API źródłowego i zamknij je (przy każdej synchronizacji)
async function checkAndCloseMissingAppointments(currentData) {
    try {
        console.log('KtoOstatni: Sprawdzam czy jakieś wizyty zniknęły z API źródłowego');

        if (!currentData || !currentData.syncData || !currentData.syncData.days) {
            console.log('KtoOstatni: Brak aktualnych danych - pomijam sprawdzanie');
            return currentData;
        }

        // Pobierz daty z aktualnych danych
        const currentDates = [];
        currentData.syncData.days.forEach(day => {
            if (day.date) {
                currentDates.push(day.date);
            }
        });

        if (currentDates.length === 0) {
            console.log('KtoOstatni: Brak dat w aktualnych danych');
            return currentData;
        }

        console.log('KtoOstatni: Sprawdzam brakujące wizyty dla dat:', currentDates);

        // Pobierz cache dla każdej daty i porównaj
        let modifiedData = JSON.parse(JSON.stringify(currentData)); // Deep copy
        let totalClosedMissing = 0;

        for (const date of currentDates) {
            const cacheKey = `sync_data_${date}`;
            const cachedData = await getCachedData(cacheKey);

            if (cachedData && cachedData.syncData && cachedData.syncData.days) {
                // Znajdź dzień w cache
                const cachedDay = cachedData.syncData.days.find(day => day.date === date);
                if (cachedDay) {
                    // Znajdź dzień w aktualnych danych
                    let currentDay = modifiedData.syncData.days.find(day => day.date === date);
                    if (!currentDay) {
                        // Utwórz dzień jeśli nie istnieje
                        currentDay = { date: date, doctors: [] };
                        modifiedData.syncData.days.push(currentDay);
                    }

                    // Porównaj wizyty i dodaj brakujące jako zamknięte
                    const closedCount = addMissingAppointmentsAsClosedForDay(currentDay, cachedDay);
                    totalClosedMissing += closedCount;
                }
            }
        }

        if (totalClosedMissing > 0) {
            console.log('KtoOstatni: Dodano', totalClosedMissing, 'brakujących wizyt jako zamknięte');
        } else {
            console.log('KtoOstatni: Brak brakujących wizyt - wszystkie wizyty z cache są nadal aktualne');
        }

        return modifiedData;

    } catch (error) {
        console.error('KtoOstatni: Błąd podczas sprawdzania brakujących wizyt:', error);
        return currentData; // Zwróć oryginalne dane w przypadku błędu
    }
}

// Dodaj brakujące wizyty jako zamknięte dla konkretnego dnia
function addMissingAppointmentsAsClosedForDay(currentDay, cachedDay) {
    try {
        // Zbierz external_id z aktualnych danych
        const currentExternalIds = new Set();
        if (currentDay.doctors) {
            currentDay.doctors.forEach(doctor => {
                if (doctor.appointments) {
                    doctor.appointments.forEach(appointment => {
                        if (appointment.external_id) {
                            currentExternalIds.add(appointment.external_id);
                        }
                    });
                }
            });
        }

        // Znajdź brakujące wizyty z cache
        let closedCount = 0;
        if (cachedDay.doctors) {
            cachedDay.doctors.forEach(cachedDoctor => {
                if (cachedDoctor.appointments) {
                    cachedDoctor.appointments.forEach(cachedAppointment => {
                        if (cachedAppointment.external_id && !currentExternalIds.has(cachedAppointment.external_id)) {
                            // Ta wizyta zniknęła - dodaj ją jako zamkniętą
                            const closedAppointment = {
                                ...cachedAppointment,
                                status: 'closed',
                                is_completed: 1,
                                is_confirmed: 1
                            };

                            // Znajdź lub utwórz lekarza w aktualnych danych
                            let targetDoctor = currentDay.doctors.find(doctor => doctor.id === cachedDoctor.id);
                            if (!targetDoctor) {
                                targetDoctor = {
                                    id: cachedDoctor.id,
                                    name: cachedDoctor.name || 'Nieznany lekarz',
                                    appointments: []
                                };
                                currentDay.doctors.push(targetDoctor);
                            }

                            // Dodaj zamkniętą wizytę
                            targetDoctor.appointments.push(closedAppointment);
                            closedCount++;
                        }
                    });
                }
            });
        }

        return closedCount;

    } catch (error) {
        console.error('KtoOstatni: Błąd podczas dodawania brakujących wizyt dla dnia:', error);
        return 0;
    }
}

// Pobierz dane z cache (localStorage)
async function getCachedData(cacheKey) {
    try {
        // Sprawdź czy localStorage jest dostępne
        if (typeof localStorage !== 'undefined') {
            const cachedData = localStorage.getItem(cacheKey);

            if (cachedData) {
                console.log('KtoOstatni: Znaleziono dane w localStorage dla klucza:', cacheKey);
                return JSON.parse(cachedData);
            } else {
                console.log('KtoOstatni: Brak danych w localStorage dla klucza:', cacheKey);
                return null;
            }
        } else {
            console.error('KtoOstatni: localStorage nie jest dostępne');
            return null;
        }
    } catch (error) {
        console.error('KtoOstatni: Błąd pobierania danych z localStorage:', error);
        
        // Jeśli dane są uszkodzone, spróbuj usunąć klucz i zwróć null
        if (error instanceof SyntaxError) {
            console.warn('KtoOstatni: Dane w localStorage są uszkodzone, usuwam klucz:', cacheKey);
            try {
                localStorage.removeItem(cacheKey);
            } catch (removeError) {
                console.error('KtoOstatni: Nie udało się usunąć uszkodzonego klucza:', removeError);
            }
        }
        
        return null;
    }
}

// Zapisz dane dzienne do cache (localStorage)
async function setCachedData(cacheKey, data) {
    try {
        // Sprawdź czy localStorage jest dostępne
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem(cacheKey, JSON.stringify(data));
            console.log('KtoOstatni: Dane zapisane do localStorage dla klucza:', cacheKey);
        } else {
            console.error('KtoOstatni: localStorage nie jest dostępne');
        }
    } catch (error) {
        console.error('KtoOstatni: Błąd zapisu danych do localStorage:', error);
        
        // Jeśli localStorage jest pełny, spróbuj wyczyścić stare dane i zapisać ponownie
        if (error.name === 'QuotaExceededError') {
            console.log('KtoOstatni: localStorage pełny, próbuję wyczyścić stare dane...');
            try {
                // Wyczyść stare dane dzienne (zostaw tylko ostatnie dni)
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('sync_data_')) {
                        keysToRemove.push(key);
                    }
                }
                
                // Sortuj klucze po dacie i usuń najstarsze (połowa)
                keysToRemove.sort();
                keysToRemove.slice(0, Math.ceil(keysToRemove.length / 2)).forEach(key => {
                    localStorage.removeItem(key);
                });
                
                // Spróbuj zapisać ponownie
                localStorage.setItem(cacheKey, JSON.stringify(data));
                console.log('KtoOstatni: Dane zapisane do localStorage po wyczyszczeniu dla klucza:', cacheKey);
            } catch (retryError) {
                console.error('KtoOstatni: Nie udało się zapisać danych nawet po wyczyszczeniu localStorage:', retryError);
            }
        }
    }
}

// Pobierz aktualne dane z API źródłowego za określony dzień
async function getCurrentDataForDate(dateStr, config) {
    try {
        console.log('KtoOstatni: Pobieranie aktualnych danych za dzień:', dateStr);

        if (!config.isEnabled) {
            console.log('KtoOstatni: Synchronizacja wyłączona');
            return null;
        }

        if (!config.syncCode || !config.serverUrl) {
            console.log('KtoOstatni: Brak konfiguracji');
            return null;
        }

        // Znajdź kartę z iGabinet
        const tabs = await chrome.tabs.query({});
        let targetTab = tabs.find(tab =>
            tab.url && (tab.url.includes('igabinet.pl') ||
                       tab.url.includes('test_page.html') ||
                       tab.url.includes('igabinet')));

        if (!targetTab) {
            console.log('KtoOstatni: Brak karty z iGabinet');
            return null;
        }

        // Pobierz dane za określony dzień
        const response = await chrome.tabs.sendMessage(targetTab.id, {
            action: 'getScheduleDataForDate',
            date: dateStr,
            skipUnknownPatient: config.skipUnknownPatient || false
        });

        if (!response || !response.success) {
            console.log('KtoOstatni: Brak danych za dzień', dateStr);
            return null;
        }

        return response.data;

    } catch (error) {
        console.error('KtoOstatni: Błąd pobierania aktualnych danych za dzień', dateStr, ':', error);
        return null;
    }
}

// Porównaj dane z cache i zamknij brakujące wizyty
async function closeMissingAppointmentsInData(currentData, cachedData, dateStr) {
    try {
        console.log('KtoOstatni: Porównuję dane z cache za dzień:', dateStr);

        if (!currentData || !currentData.syncData || !currentData.syncData.days) {
            console.log('KtoOstatni: Brak aktualnych danych - zwracam puste dane');
            return currentData;
        }

        if (!cachedData || !cachedData.syncData || !cachedData.syncData.days) {
            console.log('KtoOstatni: Brak danych w cache - nie ma co porównywać');
            return currentData;
        }

        // Zbierz wszystkie external_id z aktualnych danych
        const currentExternalIds = new Set();
        currentData.syncData.days.forEach(day => {
            if (day.doctors) {
                day.doctors.forEach(doctor => {
                    if (doctor.appointments) {
                        doctor.appointments.forEach(appointment => {
                            if (appointment.external_id) {
                                currentExternalIds.add(appointment.external_id);
                            }
                        });
                    }
                });
            }
        });

        console.log('KtoOstatni: Znaleziono', currentExternalIds.size, 'aktualnych wizyt');

        // Znajdź wizyty z cache które zniknęły z aktualnych danych
        const missingAppointments = [];
        cachedData.syncData.days.forEach(day => {
            if (day.doctors) {
                day.doctors.forEach(doctor => {
                    if (doctor.appointments) {
                        doctor.appointments.forEach(appointment => {
                            if (appointment.external_id && !currentExternalIds.has(appointment.external_id)) {
                                // Ta wizyta zniknęła - dodaj ją jako zamkniętą
                                const closedAppointment = {
                                    ...appointment,
                                    status: 'closed',
                                    is_completed: 1,
                                    is_confirmed: 1
                                };
                                missingAppointments.push({
                                    appointment: closedAppointment,
                                    doctorId: doctor.id,
                                    dayDate: day.date
                                });
                            }
                        });
                    }
                });
            }
        });

        if (missingAppointments.length === 0) {
            console.log('KtoOstatni: Brak brakujących wizyt - wszystkie wizyty z cache są nadal aktualne');
            return currentData;
        }

        console.log('KtoOstatni: Znaleziono', missingAppointments.length, 'brakujących wizyt - dodaję je jako zamknięte');

        // Dodaj brakujące wizyty jako zamknięte do aktualnych danych
        const modifiedData = JSON.parse(JSON.stringify(currentData)); // Deep copy

        missingAppointments.forEach(missing => {
            // Znajdź odpowiedni dzień
            let targetDay = modifiedData.syncData.days.find(day => day.date === missing.dayDate);
            if (!targetDay) {
                // Utwórz nowy dzień jeśli nie istnieje
                targetDay = {
                    date: missing.dayDate,
                    doctors: []
                };
                modifiedData.syncData.days.push(targetDay);
            }

            // Znajdź odpowiedniego lekarza
            let targetDoctor = targetDay.doctors.find(doctor => doctor.id === missing.doctorId);
            if (!targetDoctor) {
                // Utwórz nowego lekarza jeśli nie istnieje
                targetDoctor = {
                    id: missing.doctorId,
                    name: missing.appointment.doctor_name || 'Nieznany lekarz',
                    appointments: []
                };
                targetDay.doctors.push(targetDoctor);
            }

            // Dodaj zamkniętą wizytę
            targetDoctor.appointments.push(missing.appointment);
        });

        console.log('KtoOstatni: Dodano', missingAppointments.length, 'zamkniętych wizyt do danych');
        return modifiedData;

    } catch (error) {
        console.error('KtoOstatni: Błąd podczas zamykania brakujących wizyt:', error);
        return currentData; // Zwróć oryginalne dane w przypadku błędu
    }
}

async function performFinalSyncForDate(dateStr, config) {
    try {
        console.log('KtoOstatni: Rozpoczynam ostatnią synchronizację za dzień:', dateStr);

        if (!config.isEnabled) {
            console.log('KtoOstatni: Synchronizacja wyłączona - pomijam ostatnią synchronizację');
            return;
        }

        if (!config.syncCode || !config.serverUrl) {
            console.log('KtoOstatni: Brak konfiguracji - pomijam ostatnią synchronizację');
            return;
        }

        // Znajdź kartę z iGabinet
        const tabs = await chrome.tabs.query({});
        let targetTab = tabs.find(tab =>
            tab.url && (tab.url.includes('igabinet.pl') ||
                       tab.url.includes('test_page.html') ||
                       tab.url.includes('igabinet')));

        if (!targetTab) {
            console.log('KtoOstatni: Brak karty z iGabinet - pomijam ostatnią synchronizację');
            return;
        }

        // Pobierz dane za określony dzień
        const response = await chrome.tabs.sendMessage(targetTab.id, {
            action: 'getScheduleDataForDate',
            date: dateStr,
            skipUnknownPatient: config.skipUnknownPatient || false
        });

        if (!response || !response.success) {
            console.log('KtoOstatni: Brak danych za dzień', dateStr, '- pomijam ostatnią synchronizację');
            return;
        }

        const scheduleData = response.data;

        // Sprawdź czy są jakieś wizyty
        let totalAppointments = 0;
        if (scheduleData?.syncData?.days) {
            scheduleData.syncData.days.forEach(day => {
                if (day.doctors) {
                    day.doctors.forEach(doctor => {
                        totalAppointments += doctor.appointments?.length || 0;
                    });
                }
            });
        }

        if (totalAppointments === 0) {
            console.log('KtoOstatni: Brak wizyt za dzień', dateStr, '- pomijam ostatnią synchronizację');
            return;
        }

        console.log(`KtoOstatni: Znaleziono ${totalAppointments} wizyt za dzień ${dateStr} - wysyłam ostatnią synchronizację`);

        // Wyślij dane do systemu
        const result = await sendDataToSystem(config.syncCode, scheduleData, config.serverUrl);

        if (result.success) {
            console.log('KtoOstatni: Ostatnia synchronizacja za dzień', dateStr, 'zakończona pomyślnie');
        } else {
            console.error('KtoOstatni: Błąd ostatniej synchronizacji za dzień', dateStr, ':', result.error);
        }

    } catch (error) {
        console.error('KtoOstatni: Błąd podczas ostatniej synchronizacji za dzień', dateStr, ':', error);
    }
}

// Wykonaj automatyczną synchronizację
async function performAutoSync() {
    try {
        const config = await getConfig();
        console.log('KtoOstatni: Aktualna konfiguracja:', {
            isEnabled: config.isEnabled,
            hasSyncCode: !!config.syncCode,
            hasServerUrl: !!config.serverUrl,
            syncCode: config.syncCode ? `${config.syncCode.substring(0, 8)}...` : 'brak',
            serverUrl: config.serverUrl || 'brak'
        });

        if (!config.isEnabled || !config.syncCode || !config.serverUrl) {
            console.log('KtoOstatni: Auto-sync wyłączony lub brak kodu synchronizacji/adresu serwera');
            console.log('KtoOstatni: Szczegóły:', {
                isEnabled: config.isEnabled,
                syncCode: config.syncCode,
                serverUrl: config.serverUrl
            });
            return;
        }

        console.log('KtoOstatni: Rozpoczynam automatyczną synchronizację...');

        // Znajdź kartę z iGabinet lub localhost (dla testów)
        console.log('KtoOstatni: Szukam kart z iGabinet lub localhost...');
        const tabs = await chrome.tabs.query({
            url: ['*://*.igabinet.pl/*', 'http://localhost/*', 'https://localhost/*']
        });

        console.log('KtoOstatni: Znalezione karty:', tabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));

        if (tabs.length === 0) {
            console.log('KtoOstatni: Brak otwartych kart iGabinet lub localhost');

            // Sprawdź wszystkie karty dla debugowania
            const allTabs = await chrome.tabs.query({});
            console.log('KtoOstatni: Wszystkie otwarte karty:', allTabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));

            // TYMCZASOWO: Spróbuj synchronizacji bez content script (tylko test API)
            console.log('KtoOstatni: TYMCZASOWO TESTUJĘ WYSYŁANIE PUSTYCH DANYCH DO API');

            const testData = {
                exportDate: new Date().toISOString(),
                syncCode: config.syncCode,
                syncData: {
                    days: []
                }
            };

            const syncResult = await sendDataToSystem(config.syncCode, testData, config.serverUrl);
            console.log('KtoOstatni: Wynik testu API:', syncResult);
            return;
        }

        // Znajdź najlepszą kartę (preferuj iGabinet nad localhost)
        let targetTab = tabs.find(tab => tab.url.includes('igabinet.pl'));
        if (!targetTab) {
            targetTab = tabs.find(tab => tab.url.includes('localhost') &&
                (tab.url.includes('test_page.html') || tab.url.includes('igabinet')));
        }
        if (!targetTab) {
            targetTab = tabs[0]; // Fallback na pierwszą dostępną kartę
        }

        console.log('KtoOstatni: Znaleziono kartę do synchronizacji:', targetTab.url);

        // Sprawdź czy content script jest załadowany
        let contentScriptReady = false;
        try {
            const pingResponse = await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => reject(new Error('Timeout')), 2000);
                chrome.tabs.sendMessage(targetTab.id, { action: 'ping' }, (response) => {
                    clearTimeout(timeout);
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
            contentScriptReady = pingResponse && pingResponse.success;
        } catch (error) {
            console.log('KtoOstatni: Content script nie odpowiada, próbuję wstrzyknąć:', error.message);
        }

        // Jeśli content script nie jest gotowy, spróbuj go wstrzyknąć
        if (!contentScriptReady) {
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: targetTab.id },
                    files: ['content.js']
                });
                console.log('KtoOstatni: Content script wstrzyknięty pomyślnie');

                // Poczekaj chwilę na inicjalizację
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error('KtoOstatni: Nie można wstrzyknąć content script:', error);
                return;
            }
        }

        // Wyślij żądanie do content script z kodem synchronizacji i opcjami
        const response = await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject(new Error('Timeout podczas wyciągania danych')), 30000);
            chrome.tabs.sendMessage(targetTab.id, {
                action: 'extractScheduleData',
                syncCode: config.syncCode,
                skipUnknownPatient: config.skipUnknownPatient,
                syncTomorrowAppointments: config.syncTomorrowAppointments
            }, (response) => {
                clearTimeout(timeout);
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(response);
                }
            });
        });

        if (response && response.success && response.data) {
            console.log('KtoOstatni: Otrzymano dane z content script:', {
                hasData: !!response.data,
                hasSyncData: !!response.data.syncData,
                daysCount: response.data.syncData?.days?.length || 0
            });

            // Sprawdź czy jakieś wizyty zniknęły z API źródłowego i zamknij je
            const dataWithClosedMissing = await checkAndCloseMissingAppointments(response.data);

            // Sprawdź czy dane się zmieniły względem cache (localStorage)
            // Funkcja hasDataChanged automatycznie pobierze dane z localStorage jeśli nie przekazano cachedData
            const dataChanged = hasDataChanged(dataWithClosedMissing);

            if (!dataChanged) {
                console.log('KtoOstatni: Dane nie zmieniły się względem cache');
                console.log('KtoOstatni: TYMCZASOWO WYMUSZAM WYSYŁANIE DLA DEBUGOWANIA');

                // Tymczasowo wyłączone dla debugowania
                /*
                // Wyślij notyfikację o pominięciu
                chrome.runtime.sendMessage({
                    action: 'syncCompleted',
                    success: true,
                    skipped: true,
                    message: 'Dane nie zmieniły się - synchronizacja pominięta'
                }).catch(() => { });

                return;
                */
            }

            console.log('KtoOstatni: Dane zmieniły się - wysyłam do API');
            console.log('KtoOstatni: Przygotowuję wysyłkę danych:', {
                syncCode: config.syncCode ? `${config.syncCode.substring(0, 8)}...` : 'brak',
                serverUrl: config.serverUrl,
                hasData: !!response.data
            });

            // Wyślij dane do systemu KtoOstatni (z ewentualnie zamkniętymi wizytami)
            const syncResult = await sendDataToSystem(config.syncCode, dataWithClosedMissing, config.serverUrl);

            if (syncResult.success) {
                // Zapisz dane do cache i czas ostatniej synchronizacji
                await saveSyncDataToCache(dataWithClosedMissing);
                
                // Zapisz dane dzienne do localStorage dla każdego dnia
                if (dataWithClosedMissing && dataWithClosedMissing.syncData && dataWithClosedMissing.syncData.days) {
                    for (const day of dataWithClosedMissing.syncData.days) {
                        if (day.date) {
                            const cacheKey = `sync_data_${day.date}`;
                            await setCachedData(cacheKey, {
                                syncData: {
                                    days: [day]
                                }
                            });
                        }
                    }
                }
                
                await saveConfig({
                    ...config,
                    lastSync: new Date().toISOString()
                });

                console.log('KtoOstatni: Automatyczna synchronizacja zakończona pomyślnie');

                // Wyślij notyfikację do popup (jeśli jest otwarty)
                chrome.runtime.sendMessage({
                    action: 'syncCompleted',
                    success: true,
                    data: syncResult
                }).catch(() => { }); // Ignoruj błąd jeśli popup nie jest otwarty

            } else {
                console.error('KtoOstatni: Błąd synchronizacji:', syncResult.error);

                // Wyślij notyfikację o błędzie
                chrome.runtime.sendMessage({
                    action: 'syncCompleted',
                    success: false,
                    error: syncResult.error
                }).catch(() => { });
            }
        } else {
            const errorMsg = response?.error || 'Brak odpowiedzi od content script';
            console.error('KtoOstatni: Błąd wyciągania danych:', errorMsg);

            // Wyślij notyfikację o błędzie
            chrome.runtime.sendMessage({
                action: 'syncCompleted',
                success: false,
                error: errorMsg
            }).catch(() => { });
        }

    } catch (error) {
        console.error('KtoOstatni: Błąd automatycznej synchronizacji:', error);

        // Wyślij notyfikację o błędzie
        chrome.runtime.sendMessage({
            action: 'syncCompleted',
            success: false,
            error: error.message
        }).catch(() => { });
    }
}

// Test połączenia z serwerem
async function testServerConnection(serverUrl) {
    try {
        console.log('KtoOstatni: Testowanie połączenia z serwerem:', serverUrl);

        // Test różnych wariantów URL dla /api/v2/import
        const urlVariants = [
            serverUrl,
            serverUrl.replace('/api/v2/import', '/api/v2/import/'),
            serverUrl.replace('/api/v2/import', '/api/v2/import/index.php'),
            serverUrl.replace('/api/v2/import', '/api/v2/index.php/import'),
            serverUrl.replace('/api/v2/import', '/api/v2/import.php')
        ];
        
        for (const testUrl of urlVariants) {
            try {
                console.log('KtoOstatni: Testuję URL:', testUrl);
                
                const response = await fetch(testUrl, {
                    method: 'OPTIONS',
                    headers: {
                        'Accept': 'application/json',
                        'Origin': chrome.runtime.getURL(''),
                        'User-Agent': 'KtoOstatni-Extension/3.0.0'
                    },
                    mode: 'cors',
                    credentials: 'omit'
                });
                
                // Sprawdź błędy Cloudflare
                if (response.status === 523) {
                    console.log('KtoOstatni: Błąd Cloudflare 523 dla:', testUrl, '- Serwer źródłowy niedostępny');
                    continue; // Spróbuj następny URL
                }
                
                if (response.status >= 520 && response.status <= 599) {
                    console.log(`KtoOstatni: Błąd Cloudflare ${response.status} dla:`, testUrl, '- Problem z serwerem');
                    continue; // Spróbuj następny URL
                }
                
                console.log('KtoOstatni: Test połączenia udany dla:', testUrl, 'Status:', response.status);
                return { success: true, status: response.status, workingUrl: testUrl };
            } catch (error) {
                console.log('KtoOstatni: Test nieudany dla:', testUrl, 'Błąd:', error.message);
            }
        }
        
        // Jeśli żaden URL nie zadziałał, spróbuj prostego GET na /api/v2/
        try {
            const response = await fetch(serverUrl.replace('/api/v2/import', '/api/v2/'), {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Origin': chrome.runtime.getURL(''),
                    'User-Agent': 'KtoOstatni-Extension/3.0.0'
                },
                mode: 'cors',
                credentials: 'omit'
            });
            
            // Sprawdź błędy Cloudflare
            if (response.status === 523) {
                console.log('KtoOstatni: Błąd Cloudflare 523 dla GET /api/ - Serwer źródłowy niedostępny');
                return { success: false, error: 'Błąd Cloudflare 523 - Serwer źródłowy jest niedostępny. Serwer może być tymczasowo niedostępny lub ma problemy z konfiguracją DNS.' };
            }
            
            if (response.status >= 520 && response.status <= 599) {
                console.log(`KtoOstatni: Błąd Cloudflare ${response.status} dla GET /api/ - Problem z serwerem`);
                return { success: false, error: `Błąd Cloudflare ${response.status} - Problem z serwerem. Serwer może być tymczasowo niedostępny.` };
            }
            
            console.log('KtoOstatni: Test GET na /api/v2/ udany:', response.status);
            return { success: true, status: response.status, workingUrl: serverUrl.replace('/api/v2/import', '/api/v2/') };
        } catch (error) {
            console.log('KtoOstatni: Test GET na /api/v2/ nieudany:', error.message);
        }
        
        return { success: false, error: 'Nie można połączyć się z żadnym wariantem URL' };
    } catch (error) {
        console.error('KtoOstatni: Test połączenia nieudany:', error);
        return { success: false, error: error.message };
    }
}

// Wyślij dane do systemu KtoOstatni
async function sendDataToSystem(syncCode, scheduleData, serverUrl) {
    try {
        console.log('KtoOstatni: Wysyłanie danych do systemu');
        console.log('KtoOstatni: Kod synchronizacji:', syncCode);
        console.log('KtoOstatni: Adres serwera:', serverUrl);
        console.log('KtoOstatni: Struktura danych do wysłania:', {
            hasSyncData: !!scheduleData.syncData,
            daysCount: scheduleData.syncData?.days?.length || 0
        });

        // Walidacja URL serwera
        if (!serverUrl || !serverUrl.trim()) {
            throw new Error('Brak adresu serwera - wprowadź adres w ustawieniach');
        }

        // Sprawdź czy URL zawiera protokół
        let finalUrl = serverUrl.trim();
        if (!finalUrl.startsWith('http://') && !finalUrl.startsWith('https://')) {
            finalUrl = 'http://' + finalUrl;
            console.log('KtoOstatni: Dodano protokół http:// do URL:', finalUrl);
        }

        // Sprawdź czy URL kończy się na /api/v2/import
        if (!finalUrl.includes('/api/v2/import')) {
            if (finalUrl.endsWith('/')) {
                finalUrl += 'api/v2/import';
            } else {
                finalUrl += '/api/v2/import';
            }
            console.log('KtoOstatni: Dodano endpoint /api/v2/import do URL:', finalUrl);
        }

        console.log('KtoOstatni: Finalny URL do wysyłki:', finalUrl);

        // Walidacja kodu synchronizacji
        if (!syncCode || !syncCode.trim()) {
            throw new Error('Brak kodu synchronizacji - wprowadź kod w ustawieniach');
        }

        // Test połączenia z serwerem przed wysłaniem danych
        console.log('KtoOstatni: Testuję połączenie z serwerem...');

        // Dla localhost pomijamy test połączenia (może być blokowany)
        let connectionTest;
        if (finalUrl.includes('localhost') || finalUrl.includes('127.0.0.1')) {
            console.log('KtoOstatni: Wykryto localhost - pomijam test połączenia');
            connectionTest = { success: true, workingUrl: finalUrl };
        } else {
            connectionTest = await testServerConnection(finalUrl);
            console.log('KtoOstatni: Wynik testu połączenia:', connectionTest);

            if (!connectionTest.success) {
                console.error('KtoOstatni: Test połączenia nieudany:', connectionTest.error);
                throw new Error(`Nie można połączyć się z serwerem ${finalUrl}: ${connectionTest.error}`);
            }
        }

        // Użyj workingUrl jeśli został znaleziony
        const finalServerUrl = connectionTest.workingUrl || finalUrl;
        console.log('KtoOstatni: Połączenie z serwerem OK, wysyłam dane do:', finalServerUrl);

        // Dodaj szczegółowe logowanie
        if (scheduleData.syncData && scheduleData.syncData.days) {
            let totalDoctors = 0;
            let totalAppointments = 0;

            scheduleData.syncData.days.forEach(day => {
                if (day.doctors && Array.isArray(day.doctors)) {
                    totalDoctors += day.doctors.length;

                    day.doctors.forEach((doctor, index) => {
                        const appointmentsCount = doctor.appointments ? doctor.appointments.length : 0;
                        totalAppointments += appointmentsCount;

                        console.log(`KtoOstatni: Dzień ${day.date}, Lekarz ${index + 1}:`, {
                            id: doctor.doctorId,
                            name: doctor.doctorName,
                            appointments: appointmentsCount
                        });

                        // Loguj pierwsze 3 wizyty dla tego lekarza (jeśli istnieją)
                        if (doctor.appointments && doctor.appointments.length > 0) {
                            console.log(`KtoOstatni: Przykładowe wizyty dla lekarza ${doctor.doctorName}:`);
                            doctor.appointments.slice(0, 3).forEach((appointment, i) => {
                                console.log(`KtoOstatni: Wizyta ${i + 1}:`, {
                                    id: appointment.appointmentId,
                                    patient: `${appointment.patientFirstName} ${appointment.patientLastName}`,
                                    time: `${appointment.appointmentStart} - ${appointment.visit ? 'wizyta' : 'rezerwacja'}`,
                                    duration: appointment.appointmentDuration
                                });
                            });
                        }
                    });
                }
            });

            console.log('KtoOstatni: Łączna liczba lekarzy:', totalDoctors);
            console.log('KtoOstatni: Łączna liczba wizyt:', totalAppointments);
        }

        // Przygotuj dane w formacie oczekiwanym przez /api/v2/import
        console.log('KtoOstatni: Przygotowuję dane do wysłania...');

        // Nowy format zgodny z wymaganiami - source: "drEryk"
        const apiData = {
            source: "drEryk",
            syncCode: syncCode,
            syncData: scheduleData?.syncData || {}
        };

        console.log('KtoOstatni: Finalne dane API:', {
            source: apiData.source,
            syncCode: apiData.syncCode,
            daysCount: apiData.syncData?.days?.length || 0
        });
        console.log('KtoOstatni: Dane w formacie API (JSON):', JSON.stringify(apiData, null, 2));

        console.log('KtoOstatni: Wysyłam żądanie do:', finalServerUrl);
        console.log('KtoOstatni: Metoda:', 'POST');
        console.log('KtoOstatni: Nagłówki:', {
            'Content-Type': 'application/json',
            'X-Extension-Version': '3.0.0',
            'Accept': 'application/json',
            'Origin': chrome.runtime.getURL(''),
            'User-Agent': 'KtoOstatni-Extension/3.0.0'
        });

        console.log('KtoOstatni: ROZPOCZYNAM WYSYŁANIE ŻĄDANIA HTTP...');
        const response = await fetch(finalServerUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Extension-Version': '3.0.0',
                'Accept': 'application/json',
                'Origin': chrome.runtime.getURL(''),
                'User-Agent': 'KtoOstatni-Extension/3.0.0'
            },
            body: JSON.stringify(apiData),
            mode: 'cors',
            credentials: 'omit'
        });

        console.log('KtoOstatni: ŻĄDANIE HTTP WYSŁANE! Status:', response.status);
        console.log('KtoOstatni: Response headers:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            const errorText = await response.text().catch(() => '');
            console.error(`KtoOstatni: Błąd HTTP ${response.status} podczas wysyłania danych:`, errorText);

            // Spróbuj sparsować błąd jako JSON
            let parsedError = null;
            try {
                parsedError = JSON.parse(errorText);
            } catch (e) {
                // Nie jest JSON, zostaw jako tekst
            }

            // Sprawdź czy to błąd CORS
            if (response.status === 0 || response.type === 'opaque') {
                throw new Error(`Błąd CORS - serwer ${serverUrl} nie pozwala na żądania z rozszerzenia Chrome. Sprawdź konfigurację CORS na serwerze.`);
            }

            // Sprawdź błąd Cloudflare 523 - Origin is unreachable
            if (response.status === 523) {
                throw new Error(`Błąd Cloudflare 523 - Serwer źródłowy jest niedostępny. Serwer ${serverUrl} może być tymczasowo niedostępny lub ma problemy z konfiguracją DNS. Spróbuj ponownie za kilka minut lub skontaktuj się z administratorem serwera.`);
            }

            // Sprawdź inne błędy Cloudflare
            if (response.status >= 520 && response.status <= 599) {
                throw new Error(`Błąd Cloudflare ${response.status} - Problem z serwerem. Serwer ${serverUrl} może być tymczasowo niedostępny. Spróbuj ponownie za kilka minut.`);
            }

            // Obsłuż błędy API z szczegółowymi informacjami
            const userFriendlyError = formatApiError(response.status, parsedError, errorText);
            throw new Error(userFriendlyError);
        }

        const result = await response.json();
        console.log('KtoOstatni: Odpowiedź z API:', result);
        return { success: true, data: result };

    } catch (error) {
        console.error('KtoOstatni: Błąd wysyłania danych:', error);
        console.error('KtoOstatni: Stack trace:', error.stack);
        console.error('KtoOstatni: Szczegóły żądania:', {
            syncCode: syncCode,
            serverUrl: serverUrl,
            dataStructure: {
                hasSyncData: !!scheduleData.syncData,
                daysCount: scheduleData.syncData?.days?.length || 0,
                doctorsCount: scheduleData.syncData?.days?.reduce((total, day) => total + (day.doctors?.length || 0), 0) || 0
            }
        });
        return { success: false, error: error.message };
    }
}

// Formatuj błędy API w przyjazny sposób dla użytkownika
function formatApiError(statusCode, parsedError, rawErrorText) {
    // Jeśli mamy sparsowany błąd JSON z API
    if (parsedError && parsedError.error && parsedError.message) {
        let userMessage = `Błąd synchronizacji (HTTP ${statusCode}): ${parsedError.message}`;

        // Obsłuż specyficzne błędy API
        if (parsedError.details) {
            const details = parsedError.details;

            // Błąd niezmapowanych lekarzy
            if (details.unmapped_count && details.unmapped_doctors) {
                userMessage += `\n\n🔍 Szczegóły problemu:`;
                userMessage += `\n• Liczba niezmapowanych lekarzy: ${details.unmapped_count}`;

                if (details.unmapped_doctors.length > 0) {
                    userMessage += `\n• Niezmapowani lekarze:`;
                    details.unmapped_doctors.forEach((doctor, index) => {
                        if (index < 5) { // Pokaż maksymalnie 5 lekarzy
                            userMessage += `\n  - ${doctor.name} (ID: ${doctor.external_id})`;
                        }
                    });

                    if (details.unmapped_doctors.length > 5) {
                        userMessage += `\n  ... i ${details.unmapped_doctors.length - 5} więcej`;
                    }
                }

                if (details.unmapped_doctors_file) {
                    userMessage += `\n\n📄 Pełna lista zapisana w pliku: ${details.unmapped_doctors_file}`;
                }

                userMessage += `\n\n💡 Rozwiązanie:`;
                userMessage += `\n1. Przejdź do panelu administracyjnego`;
                userMessage += `\n2. Znajdź sekcję "Mapowanie lekarzy"`;
                userMessage += `\n3. Zmapuj nieznanych lekarzy do istniejących w systemie`;
                userMessage += `\n4. Spróbuj synchronizacji ponownie`;
            }

            // Błąd walidacji danych
            else if (details.validation_errors) {
                userMessage += `\n\n🔍 Błędy walidacji:`;
                details.validation_errors.forEach(error => {
                    userMessage += `\n• ${error}`;
                });
            }

            // Błąd bazy danych
            else if (details.database_error) {
                userMessage += `\n\n🔍 Problem z bazą danych: ${details.database_error}`;
                userMessage += `\n💡 Skontaktuj się z administratorem systemu`;
            }

            // Inne szczegóły
            else {
                userMessage += `\n\n🔍 Dodatkowe informacje: ${JSON.stringify(details, null, 2)}`;
            }
        }

        // Dodaj timestamp jeśli dostępny
        if (parsedError.timestamp) {
            userMessage += `\n\n⏰ Czas błędu: ${parsedError.timestamp}`;
        }

        return userMessage;
    }

    // Standardowe błędy HTTP bez szczegółów API
    switch (statusCode) {
        case 400:
            return `Błąd żądania (HTTP 400): Nieprawidłowe dane wysłane do serwera.\n\nSprawdź konfigurację kodu synchronizacji i adresu serwera.`;

        case 401:
            return `Błąd autoryzacji (HTTP 401): Brak uprawnień do synchronizacji.\n\nSprawdź kod synchronizacji w ustawieniach.`;

        case 403:
            return `Dostęp zabroniony (HTTP 403): Serwer odrzucił żądanie.\n\nMożliwe przyczyny:\n• Import jest wyłączony w systemie\n• Kod synchronizacji jest nieprawidłowy\n• Brak uprawnień do importu`;

        case 404:
            return `Nie znaleziono (HTTP 404): Endpoint API nie istnieje.\n\nSprawdź adres serwera w ustawieniach.\nPoprawny format: localhost:8080 lub domena.pl`;

        case 422:
            return `Błąd danych (HTTP 422): Serwer nie może przetworzyć danych.\n\nMożliwe przyczyny:\n• Nieprawidłowy format danych\n• Brakujące wymagane pola\n• Błędne wartości w danych`;

        case 500:
            return `Błąd serwera (HTTP 500): Problem po stronie serwera.\n\nSpróbuj ponownie za kilka minut.\nJeśli problem się powtarza, skontaktuj się z administratorem.`;

        case 502:
            return `Błąd bramy (HTTP 502): Serwer proxy nie może połączyć się z serwerem docelowym.\n\nSerwer może być tymczasowo niedostępny.`;

        case 503:
            return `Serwis niedostępny (HTTP 503): Serwer jest przeciążony lub w trakcie konserwacji.\n\nSpróbuj ponownie za kilka minut.`;

        case 504:
            return `Timeout bramy (HTTP 504): Serwer nie odpowiedział w wyznaczonym czasie.\n\nOperacja może trwać zbyt długo. Spróbuj ponownie.`;

        default:
            return `Błąd HTTP ${statusCode}: ${rawErrorText || 'Nieznany błąd serwera'}`;
    }
}

// Nasłuchuj na wiadomości z popup i content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('KtoOstatni: Background otrzymał wiadomość:', request);

    if (request.action === 'contentScriptReady') {
        console.log('KtoOstatni: Content script gotowy na:', request.url);
        sendResponse({ success: true, message: 'Background script otrzymał sygnał' });
        return true;
    }

    if (request.action === 'getConfig') {
        getConfig().then(config => {
            sendResponse({ success: true, config: config });
        }).catch(error => {
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'saveConfig') {
        saveConfig(request.config).then(async () => {
            console.log('KtoOstatni: Zapisano konfigurację, aktualizuję auto-sync...');

            // Zaktualizuj auto-sync
            if (request.config.autoSync && request.config.isEnabled && request.config.syncCode && request.config.serverUrl) {
                console.log(`KtoOstatni: Włączam auto-sync z interwałem ${request.config.syncInterval} minut`);
                await setupAutoSync(request.config.syncInterval);
            } else {
                console.log('KtoOstatni: Wyłączam auto-sync');
                await chrome.alarms.clear('autoSync');
            }

            console.log('KtoOstatni: Konfiguracja zapisana pomyślnie');

            // Sprawdź status alarmów
            const alarms = await chrome.alarms.getAll();
            console.log('KtoOstatni: Aktywne alarmy:', alarms.map(a => ({
                name: a.name,
                scheduledTime: new Date(a.scheduledTime).toLocaleString(),
                periodInMinutes: a.periodInMinutes
            })));

            sendResponse({ success: true });
        }).catch(error => {
            console.error('KtoOstatni: Błąd zapisywania konfiguracji:', error);
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'manualSync') {
        console.log('KtoOstatni: Rozpoczynam ręczną synchronizację...');
        performAutoSync().then(() => {
            sendResponse({ success: true });
        }).catch(error => {
            console.error('KtoOstatni: Błąd ręcznej synchronizacji:', error);
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'testConnection') {
        console.log('KtoOstatni: Testowanie połączenia z serwerem:', request.serverUrl);
        testServerConnection(request.serverUrl).then(result => {
            sendResponse(result);
        }).catch(error => {
            console.error('KtoOstatni: Błąd testu połączenia:', error);
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'getAlarmStatus') {
        chrome.alarms.getAll().then(alarms => {
            const autoSyncAlarm = alarms.find(alarm => alarm.name === 'autoSync');
            sendResponse({
                success: true,
                hasAutoSyncAlarm: !!autoSyncAlarm,
                autoSyncAlarmInfo: autoSyncAlarm ? {
                    scheduledTime: new Date(autoSyncAlarm.scheduledTime).toLocaleString(),
                    periodInMinutes: autoSyncAlarm.periodInMinutes
                } : null,
                allAlarms: alarms.map(a => ({
                    name: a.name,
                    scheduledTime: new Date(a.scheduledTime).toLocaleString(),
                    periodInMinutes: a.periodInMinutes
                }))
            });
        }).catch(error => {
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    if (request.action === 'downloadData') {
        // Pobierz dane jako plik JSON
        const jsonData = JSON.stringify(request.data, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const filename = `ktoostatni_sync_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;

        chrome.downloads.download({
            url: url,
            filename: filename,
            saveAs: true
        }, (downloadId) => {
            if (chrome.runtime.lastError) {
                console.error('KtoOstatni: Błąd podczas pobierania:', chrome.runtime.lastError);
                sendResponse({ success: false, error: chrome.runtime.lastError.message });
            } else {
                console.log('KtoOstatni: Plik został pobrany z ID:', downloadId);
                sendResponse({ success: true, downloadId: downloadId });
            }

            // Zwolnij URL
            URL.revokeObjectURL(url);
        });

        return true;
    }

    if (request.action === 'clearCache') {
        clearSyncCache().then(() => {
            sendResponse({ success: true, message: 'Cache wyczyszczony' });
        }).catch(error => {
            sendResponse({ success: false, error: error.message });
        });
        return true;
    }

    console.log('KtoOstatni: Nieznana akcja:', request.action);
    sendResponse({ success: false, error: 'Nieznana akcja' });
});
