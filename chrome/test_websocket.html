<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Przechwytywania WebSocket</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ccc;
            padding: 20px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .console-log {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 20px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
        }
        h2 {
            margin-top: 30px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>Test Przechwytywania WebSocket i Deszyfrowania</h1>
    
    <div class="test-container">
        <h2>Test WebSocket</h2>
        <button id="connect-ws">Połącz z Echo WebSocket</button>
        <button id="send-text">Wyślij tekst</button>
        <button id="send-json">Wyślij JSON</button>
        <button id="disconnect-ws">Rozłącz</button>
        <div id="ws-status">Status: Rozłączony</div>
    </div>

    <div class="test-container">
        <h2>Test Szyfrowania/Deszyfrowania</h2>
        <button id="generate-key">Wygeneruj klucz</button>
        <button id="encrypt-decrypt">Zaszyfruj i odszyfruj wiadomość</button>
        <div id="crypto-status">Status: Brak operacji</div>
    </div>

    <div class="console-log" id="console-output">
        <strong>Logi konsoli:</strong><br>
    </div>

    <script>
        // Nadpisanie console.log aby pokazywać logi na stronie
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        const originalConsoleInfo = console.info;

        function formatForConsole(args) {
            return Array.from(args).map(arg => {
                if (typeof arg === 'object') {
                    return JSON.stringify(arg, null, 2);
                }
                return String(arg);
            }).join(' ');
        }

        console.log = function() {
            originalConsoleLog.apply(console, arguments);
            const output = document.getElementById('console-output');
            output.innerHTML += `<span style="color: black;">> ${formatForConsole(arguments)}</span><br>`;
            output.scrollTop = output.scrollHeight;
        };

        console.error = function() {
            originalConsoleError.apply(console, arguments);
            const output = document.getElementById('console-output');
            output.innerHTML += `<span style="color: red;">> ERROR: ${formatForConsole(arguments)}</span><br>`;
            output.scrollTop = output.scrollHeight;
        };

        console.warn = function() {
            originalConsoleWarn.apply(console, arguments);
            const output = document.getElementById('console-output');
            output.innerHTML += `<span style="color: orange;">> WARN: ${formatForConsole(arguments)}</span><br>`;
            output.scrollTop = output.scrollHeight;
        };

        console.info = function() {
            originalConsoleInfo.apply(console, arguments);
            const output = document.getElementById('console-output');
            output.innerHTML += `<span style="color: blue;">> INFO: ${formatForConsole(arguments)}</span><br>`;
            output.scrollTop = output.scrollHeight;
        };

        // Zmienne globalne
        let webSocket = null;
        let cryptoKey = null;

        // Obsługa WebSocket
        document.getElementById('connect-ws').addEventListener('click', () => {
            if (webSocket && webSocket.readyState === WebSocket.OPEN) {
                console.log('WebSocket już jest połączony');
                return;
            }

            try {
                // Używamy publicznego serwera echo WebSocket
                webSocket = new WebSocket('wss://echo.websocket.events');
                
                webSocket.onopen = () => {
                    console.log('WebSocket połączony');
                    document.getElementById('ws-status').textContent = 'Status: Połączony';
                };
                
                webSocket.onmessage = (event) => {
                    console.log('Otrzymano wiadomość WebSocket:', event.data);
                };
                
                webSocket.onerror = (error) => {
                    console.error('Błąd WebSocket:', error);
                };
                
                webSocket.onclose = () => {
                    console.log('WebSocket rozłączony');
                    document.getElementById('ws-status').textContent = 'Status: Rozłączony';
                };
            } catch (error) {
                console.error('Błąd tworzenia WebSocket:', error);
            }
        });

        document.getElementById('send-text').addEventListener('click', () => {
            if (!webSocket || webSocket.readyState !== WebSocket.OPEN) {
                console.error('WebSocket nie jest połączony');
                return;
            }

            const message = 'Testowa wiadomość tekstowa ' + new Date().toISOString();
            webSocket.send(message);
            console.log('Wysłano wiadomość tekstową:', message);
        });

        document.getElementById('send-json').addEventListener('click', () => {
            if (!webSocket || webSocket.readyState !== WebSocket.OPEN) {
                console.error('WebSocket nie jest połączony');
                return;
            }

            const jsonMessage = {
                type: 'test',
                timestamp: new Date().toISOString(),
                data: {
                    id: Math.floor(Math.random() * 1000),
                    name: 'Test message',
                    values: [1, 2, 3, 4, 5]
                }
            };
            
            webSocket.send(JSON.stringify(jsonMessage));
            console.log('Wysłano wiadomość JSON:', jsonMessage);
        });

        document.getElementById('disconnect-ws').addEventListener('click', () => {
            if (!webSocket) {
                console.log('WebSocket nie jest zainicjalizowany');
                return;
            }

            webSocket.close();
            webSocket = null;
            console.log('WebSocket rozłączony');
            document.getElementById('ws-status').textContent = 'Status: Rozłączony';
        });

        // Obsługa szyfrowania/deszyfrowania
        document.getElementById('generate-key').addEventListener('click', async () => {
            try {
                // Generowanie klucza AES-GCM
                cryptoKey = await window.crypto.subtle.generateKey(
                    {
                        name: "AES-GCM",
                        length: 256
                    },
                    true,
                    ["encrypt", "decrypt"]
                );
                
                console.log('Wygenerowano klucz AES-GCM');
                document.getElementById('crypto-status').textContent = 'Status: Klucz wygenerowany';
            } catch (error) {
                console.error('Błąd generowania klucza:', error);
            }
        });

        document.getElementById('encrypt-decrypt').addEventListener('click', async () => {
            if (!cryptoKey) {
                console.error('Najpierw wygeneruj klucz');
                return;
            }

            try {
                // Przygotuj dane do zaszyfrowania
                const jsonData = {
                    type: 'encrypted_message',
                    timestamp: new Date().toISOString(),
                    payload: {
                        id: Math.floor(Math.random() * 1000),
                        content: 'To jest tajna wiadomość',
                        user: 'testowy_użytkownik'
                    }
                };
                
                const encoder = new TextEncoder();
                const data = encoder.encode(JSON.stringify(jsonData));
                
                // Generuj losowy IV (Initialization Vector)
                const iv = window.crypto.getRandomValues(new Uint8Array(12));
                
                // Zaszyfruj dane
                document.getElementById('crypto-status').textContent = 'Status: Szyfrowanie...';
                console.log('Szyfrowanie danych:', jsonData);
                
                const encryptedData = await window.crypto.subtle.encrypt(
                    {
                        name: "AES-GCM",
                        iv: iv
                    },
                    cryptoKey,
                    data
                );
                
                console.log('Dane zaszyfrowane pomyślnie');
                document.getElementById('crypto-status').textContent = 'Status: Dane zaszyfrowane';
                
                // Odszyfruj dane
                document.getElementById('crypto-status').textContent = 'Status: Deszyfrowanie...';
                console.log('Deszyfrowanie danych...');
                
                const decryptedData = await window.crypto.subtle.decrypt(
                    {
                        name: "AES-GCM",
                        iv: iv
                    },
                    cryptoKey,
                    encryptedData
                );
                
                // Konwertuj odszyfrowane dane na tekst
                const decoder = new TextDecoder();
                const decryptedText = decoder.decode(decryptedData);
                const decryptedJson = JSON.parse(decryptedText);
                
                console.log('Dane odszyfrowane pomyślnie:', decryptedJson);
                document.getElementById('crypto-status').textContent = 'Status: Dane odszyfrowane';
            } catch (error) {
                console.error('Błąd szyfrowania/deszyfrowania:', error);
                document.getElementById('crypto-status').textContent = 'Status: Błąd operacji';
            }
        });
    </script>
</body>
</html>
