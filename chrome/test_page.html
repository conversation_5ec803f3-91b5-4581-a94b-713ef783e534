<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Obserwera Elementów li.underline-tag</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ccc;
            padding: 20px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .work-schedule__term {
            position: relative;
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .underline-tags {
            list-style-type: none;
            padding: 0;
            margin: 5px 0 0 0;
            display: flex;
        }
        .underline-tag {
            height: 5px;
            width: 100%;
            background-color: #d5f30a;
            border: 1px solid #c0db09;
        }
        .console-log {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            margin-top: 20px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Test Obserwera Elementów li.underline-tag</h1>
    
    <div class="test-container">
        <h2>Element istniejący przy załadowaniu strony:</h2>
        <div class="work-schedule__term work-schedule__term--reservation" data-term-id="100001">
            <div class="work-schedule__term-content">
                <div class="work-schedule__term-data">
                    <div class="term-content__hours">10:00 - 10:30</div>
                    <h4 class="term-content__user">Jan Kowalski</h4>
                    <div class="term-content__service">Konsultacja</div>
                </div>
            </div>
            <ul class="underline-tags">
                <li id="tags_reservation_100001_1" class="underline-tag"></li>
            </ul>
        </div>
        
        <div class="test-container">
            <h3>Status elementów li.underline-tag:</h3>
            <div id="status-tags-info">
                <p>Brak wykrytych elementów</p>
            </div>
            <button id="show-status-tags">Pokaż zebrane statusTag</button>
        </div>
    </div>

    <div class="test-container">
        <h2>Dodawanie elementów dynamicznie:</h2>
        <div id="dynamic-container"></div>
        <button id="add-element">Dodaj element z li.underline-tag</button>
        <button id="add-multiple">Dodaj 3 elementy</button>
        <button id="clear-elements">Wyczyść elementy</button>
        <button id="change-class">Zmień klasę elementu</button>
        <button id="change-id">Zmień ID elementu</button>
    </div>

    <div class="console-log" id="console-output">
        <strong>Logi konsoli:</strong><br>
    </div>

    <script>
        // Nadpisanie console.log aby pokazywać logi na stronie
        const originalConsoleLog = console.log;
        console.log = function() {
            // Wywołaj oryginalny console.log
            originalConsoleLog.apply(console, arguments);
            
            // Dodaj log do elementu na stronie
            const output = document.getElementById('console-output');
            const args = Array.from(arguments).map(arg => {
                if (typeof arg === 'object') {
                    return JSON.stringify(arg, null, 2);
                }
                return arg;
            }).join(' ');
            
            output.innerHTML += `> ${args}<br>`;
            output.scrollTop = output.scrollHeight;
            
            // Aktualizuj informacje o statusTag, jeśli log zawiera informacje o wykrytym elemencie
            if (args.includes('KtoOstatni: Wykryto element li.underline-tag')) {
                updateStatusTagsInfo();
            }
        };
        
        // Funkcja do aktualizacji informacji o statusTag
        function updateStatusTagsInfo() {
            // Sprawdź, czy funkcja getAllStatusTags jest dostępna (z content.js)
            if (typeof window.getAllStatusTags === 'function') {
                const statusTags = window.getAllStatusTags();
                const statusTagsInfo = document.getElementById('status-tags-info');
                
                if (Object.keys(statusTags).length === 0) {
                    statusTagsInfo.innerHTML = '<p>Brak wykrytych elementów</p>';
                    return;
                }
                
                let html = '<table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">';
                html += '<tr><th>appointmentId</th><th>statusTag</th></tr>';
                
                for (const [appointmentId, statusTag] of Object.entries(statusTags)) {
                    html += `<tr><td>${appointmentId}</td><td>${statusTag}</td></tr>`;
                }
                
                html += '</table>';
                statusTagsInfo.innerHTML = html;
            }
        }

        // Funkcja do generowania unikalnego ID
        function generateUniqueId() {
            return Date.now() + Math.floor(Math.random() * 1000);
        }

        // Funkcja do dodawania elementu z li.underline-tag
        function addElement() {
            const termId = generateUniqueId();
            const container = document.getElementById('dynamic-container');
            
            const termElement = document.createElement('div');
            termElement.className = 'work-schedule__term work-schedule__term--reservation';
            termElement.setAttribute('data-term-id', termId);
            
            termElement.innerHTML = `
                <div class="work-schedule__term-content">
                    <div class="work-schedule__term-data">
                        <div class="term-content__hours">11:00 - 11:30</div>
                        <h4 class="term-content__user">Anna Nowak</h4>
                        <div class="term-content__service">Badanie</div>
                    </div>
                </div>
                <ul class="underline-tags">
                    <li id="tags_reservation_${termId}_2" class="underline-tag"></li>
                </ul>
            `;
            
            container.appendChild(termElement);
            console.log(`Dodano nowy element z data-term-id=${termId}`);
        }

        // Obsługa przycisków
        document.getElementById('add-element').addEventListener('click', addElement);
        
        document.getElementById('add-multiple').addEventListener('click', () => {
            for (let i = 0; i < 3; i++) {
                setTimeout(addElement, i * 500);
            }
        });
        
        document.getElementById('clear-elements').addEventListener('click', () => {
            document.getElementById('dynamic-container').innerHTML = '';
            console.log('Wyczyszczono elementy');
        });
        
        // Przycisk do wyświetlania zebranych statusTag
        document.getElementById('show-status-tags').addEventListener('click', () => {
            console.log('Próba wyświetlenia statusTag...');
            
            // Próba dostępu do funkcji getAllStatusTags z content.js
            if (typeof window.getAllStatusTags === 'function') {
                const statusTags = window.getAllStatusTags();
                console.log('Zebrane statusTag:', statusTags);
                updateStatusTagsInfo();
            } else {
                console.log('Funkcja getAllStatusTags nie jest dostępna. Używamy window.postMessage do komunikacji z content script.');
                
                // Użyj window.postMessage do komunikacji z content script
                window.postMessage({ action: 'getStatusTags' }, '*');
                
                // Nasłuchuj na odpowiedź
                window.addEventListener('message', function(event) {
                    if (event.data && event.data.action === 'statusTagsResponse') {
                        console.log('Otrzymano statusTags z content script:', event.data.statusTags);
                        
                        // Aktualizuj informacje na stronie
                        const statusTagsInfo = document.getElementById('status-tags-info');
                        if (Object.keys(event.data.statusTags).length === 0) {
                            statusTagsInfo.innerHTML = '<p>Brak wykrytych elementów</p>';
                            return;
                        }
                        
                        let html = '<table border="1" cellpadding="5" style="border-collapse: collapse; width: 100%;">';
                        html += '<tr><th>appointmentId</th><th>statusTag</th></tr>';
                        
                        for (const [appointmentId, statusTag] of Object.entries(event.data.statusTags)) {
                            html += `<tr><td>${appointmentId}</td><td>${statusTag}</td></tr>`;
                        }
                        
                        html += '</table>';
                        statusTagsInfo.innerHTML = html;
                    }
                }, { once: true });
            }
        });
        
        // Zmiana klasy elementu
        document.getElementById('change-class').addEventListener('click', () => {
            const tags = document.querySelectorAll('.underline-tag');
            if (tags.length > 0) {
                const randomIndex = Math.floor(Math.random() * tags.length);
                const tag = tags[randomIndex];
                
                if (tag.classList.contains('modified-tag')) {
                    tag.classList.remove('modified-tag');
                    console.log(`Usunięto klasę 'modified-tag' z elementu ${tag.id}`);
                } else {
                    tag.classList.add('modified-tag');
                    console.log(`Dodano klasę 'modified-tag' do elementu ${tag.id}`);
                }
            } else {
                console.log('Brak elementów z klasą underline-tag do modyfikacji');
            }
        });
        
        // Zmiana ID elementu
        document.getElementById('change-id').addEventListener('click', () => {
            const tags = document.querySelectorAll('.underline-tag');
            if (tags.length > 0) {
                const randomIndex = Math.floor(Math.random() * tags.length);
                const tag = tags[randomIndex];
                const oldId = tag.id;
                const newId = 'modified_' + generateUniqueId();
                
                tag.id = newId;
                console.log(`Zmieniono ID elementu z '${oldId}' na '${newId}'`);
            } else {
                console.log('Brak elementów z klasą underline-tag do modyfikacji');
            }
        });
    </script>
</body>
</html>