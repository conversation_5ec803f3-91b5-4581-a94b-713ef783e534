// Popup script dla <PERSON>ni - Automatyczna Synchronizacja
document.addEventListener('DOMContentLoaded', function () {
    console.log('KtoOstatni: Popup załadowany');

    // Elementy UI
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    const toggleSwitch = document.getElementById('toggleSwitch');
    const syncCodeInput = document.getElementById('syncCode');
    const serverUrlInput = document.getElementById('serverUrl');
    const syncIntervalSelect = document.getElementById('syncInterval');
    const skipUnknownPatientCheckbox = document.getElementById('skipUnknownPatient');
    const syncTomorrowAppointmentsCheckbox = document.getElementById('syncTomorrowAppointments');
    const manualSyncButton = document.getElementById('manualSyncButton');
    const pauseButton = document.getElementById('pauseButton');
    const viewDataButton = document.getElementById('viewDataButton');
    const downloadDataButton = document.getElementById('downloadDataButton');
    const clearCacheButton = document.getElementById('clearCacheButton');
    const lastSyncElement = document.getElementById('lastSync');
    const statusElement = document.getElementById('status');
    const progressBar = document.getElementById('progressBar');
    const loadingElement = document.getElementById('loading');

    let currentConfig = {};

    // Inicjalizacja
    init();

    async function init() {
        try {
            console.log('KtoOstatni: Inicjalizacja popup...');
            await loadConfig();
            setupEventListeners();
            setupMessageListener();
            updateUI();
            console.log('KtoOstatni: Popup zainicjalizowany pomyślnie');
        } catch (error) {
            console.error('KtoOstatni: Błąd inicjalizacji popup:', error);
            updateStatus('Błąd inicjalizacji: ' + error.message);
        }
    }

    // Nasłuchuj na wiadomości z background script
    function setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('KtoOstatni: Popup otrzymał wiadomość:', message);

            if (message.action === 'syncCompleted') {
                if (message.success) {
                    if (message.skipped) {
                        updateStatus('ℹ️ ' + (message.message || 'Synchronizacja pominięta'));
                    } else {
                        updateStatus('✅ Synchronizacja zakończona pomyślnie');
                    }
                    // Odśwież konfigurację, aby zaktualizować lastSync
                    loadConfig().then(() => updateUI());
                } else {
                    // Wyświetl szczegółowy błąd synchronizacji
                    const errorMessage = message.error || 'Nieznany błąd';
                    displaySyncError(errorMessage);
                }
                hideLoading();
            }

            sendResponse({ received: true });
        });
    }

    // Załaduj konfigurację z background script
    async function loadConfig() {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response.success) {
                    currentConfig = response.config;
                    resolve();
                } else {
                    reject(new Error(response.error || 'Nieznany błąd'));
                }
            });
        });
    }

    // Zapisz konfigurację
    async function saveConfig() {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'saveConfig',
                config: currentConfig
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else if (response.success) {
                    resolve();
                } else {
                    reject(new Error(response.error || 'Nieznany błąd'));
                }
            });
        });
    }

    // Konfiguracja event listenerów
    function setupEventListeners() {
        // Toggle switch
        toggleSwitch.addEventListener('click', async () => {
            currentConfig.isEnabled = !currentConfig.isEnabled;
            currentConfig.autoSync = currentConfig.isEnabled;
            await saveConfig();
            updateUI();
        });

        // Kod synchronizacji
        syncCodeInput.addEventListener('change', async () => {
            currentConfig.syncCode = syncCodeInput.value.trim();
            await saveConfig();
        });

        // Adres serwera
        serverUrlInput.addEventListener('change', async () => {
            currentConfig.serverUrl = serverUrlInput.value.trim();
            await saveConfig();
        });

        // Interwał synchronizacji
        syncIntervalSelect.addEventListener('change', async () => {
            currentConfig.syncInterval = parseInt(syncIntervalSelect.value);
            await saveConfig();
        });

        // Checkbox pomijaj nieznany pacjent
        skipUnknownPatientCheckbox.addEventListener('change', async () => {
            currentConfig.skipUnknownPatient = skipUnknownPatientCheckbox.checked;
            await saveConfig();
        });

        // Checkbox synchronizuj wizyty z jutro
        syncTomorrowAppointmentsCheckbox.addEventListener('change', async () => {
            currentConfig.syncTomorrowAppointments = syncTomorrowAppointmentsCheckbox.checked;
            await saveConfig();
        });

        // Ręczna synchronizacja
        manualSyncButton.addEventListener('click', async () => {
            await performManualSync();
        });

        // Pauza
        pauseButton.addEventListener('click', async () => {
            currentConfig.isEnabled = false;
            currentConfig.autoSync = false;
            await saveConfig();
            updateUI();
        });

        // Podgląd danych
        viewDataButton.addEventListener('click', async () => {
            await viewLastSyncData();
        });

        // Pobierz JSON
        downloadDataButton.addEventListener('click', async () => {
            await downloadLastSyncData();
        });

        // Wyczyść cache
        clearCacheButton.addEventListener('click', async () => {
            await clearCache();
        });

        // Nasłuchuj na wiadomości z background script
        chrome.runtime.onMessage.addListener((message) => {
            if (message.action === 'syncCompleted') {
                updateStatus('Synchronizacja zakończona pomyślnie');
                loadConfig().then(() => updateUI());
            }
        });
    }

    // Aktualizuj interfejs użytkownika
    async function updateUI() {
        // Status
        if (currentConfig.isEnabled && currentConfig.syncCode) {
            statusDot.classList.add('active');
            statusText.textContent = 'Aktywny';
            toggleSwitch.classList.add('active');
        } else {
            statusDot.classList.remove('active');
            statusText.textContent = 'Wyłączony';
            toggleSwitch.classList.remove('active');
        }

        // Pola formularza
        syncCodeInput.value = currentConfig.syncCode || '';
        serverUrlInput.value = currentConfig.serverUrl || '';
        syncIntervalSelect.value = currentConfig.syncInterval || 15;
        skipUnknownPatientCheckbox.checked = currentConfig.skipUnknownPatient || false;
        syncTomorrowAppointmentsCheckbox.checked = currentConfig.syncTomorrowAppointments || false;

        // Ostatnia synchronizacja
        if (currentConfig.lastSync) {
            const lastSyncDate = new Date(currentConfig.lastSync);
            lastSyncElement.textContent = `Ostatnia synchronizacja: ${lastSyncDate.toLocaleString('pl-PL')}`;
        } else {
            lastSyncElement.textContent = 'Ostatnia synchronizacja: nigdy';
        }

        // Przyciski
        const hasValidConfig = currentConfig.syncCode && currentConfig.syncCode.length > 0 && currentConfig.serverUrl && currentConfig.serverUrl.length > 0;
        manualSyncButton.disabled = !hasValidConfig;
        pauseButton.disabled = !currentConfig.isEnabled;

        // Sprawdź status alarmów
        await checkAlarmStatus();
    }

    // Sprawdź status alarmów
    async function checkAlarmStatus() {
        try {
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({ action: 'getAlarmStatus' }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });

            if (response.success) {
                let statusMessage = '';
                
                // Status auto-sync
                if (response.hasAutoSyncAlarm && currentConfig.autoSync && currentConfig.isEnabled) {
                    statusMessage += `✅ Auto-sync aktywny (${response.autoSyncAlarmInfo.periodInMinutes} min)`;
                } else if (currentConfig.autoSync && currentConfig.isEnabled) {
                    statusMessage += '⚠️ Auto-sync skonfigurowany, ale alarm nieaktywny';
                } else {
                    statusMessage += 'ℹ️ Auto-sync wyłączony';
                }
                
                // Status cache (localStorage)
                try {
                    const cachedData = localStorage.getItem('ktoostatni_lastSyncData');
                    if (cachedData) {
                        statusMessage += ' | 💾 Cache aktywny (localStorage)';
                    } else {
                        statusMessage += ' | 💾 Cache pusty (localStorage)';
                    }
                } catch (error) {
                    statusMessage += ' | 💾 Błąd odczytu cache';
                }

                // Status pomijania nieznanych pacjentów
                if (currentConfig.skipUnknownPatient) {
                    statusMessage += ' | 🚫 Pomijaj nieznanych';
                }

                // Status synchronizacji wizyt z jutro
                if (currentConfig.syncTomorrowAppointments) {
                    statusMessage += ' | 📅 Sync jutro';
                }
                
                updateStatus(statusMessage);
            }
        } catch (error) {
            console.error('KtoOstatni: Błąd sprawdzania alarmów:', error);
        }
    }

    // Wykonaj ręczną synchronizację
    async function performManualSync() {
        try {
            setButtonsEnabled(false);
            showLoading(true);
            updateStatus('Rozpoczynam ręczną synchronizację...');
            updateProgress(50);

            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({ action: 'manualSync' }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });

            if (response.success) {
                updateStatus('Ręczna synchronizacja zakończona pomyślnie');
                updateProgress(100);
                await loadConfig();
                updateUI();
            } else {
                // Wyświetl szczegółowy błąd ręcznej synchronizacji
                const errorMessage = response.error || 'Nieznany błąd';
                displaySyncError(errorMessage);
            }

        } catch (error) {
            console.error('KtoOstatni: Błąd ręcznej synchronizacji:', error);
            displaySyncError(error.message || 'Nieznany błąd');
        } finally {
            setButtonsEnabled(true);
            showLoading(false);
            setTimeout(() => updateProgress(0), 2000);
        }
    }

    // Podgląd ostatnich danych synchronizacji
    async function viewLastSyncData() {
        try {
            updateStatus('Pobieranie danych do podglądu...');

            // Sprawdź czy jesteśmy na stronie iGabinet
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab.url.includes('igabinet.pl') && !tab.url.includes('test_page.html')) {
                updateStatus('Przejdź na stronę iGabinet lub test_page.html');
                return;
            }

            // Najpierw sprawdź czy content script jest załadowany
            try {
                const pingResponse = await new Promise((resolve, reject) => {
                    chrome.tabs.sendMessage(tab.id, { action: 'ping' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                if (!pingResponse || !pingResponse.success) {
                    updateStatus('Content script nie jest załadowany na tej stronie');
                    return;
                }

                console.log('KtoOstatni: Content script ping OK:', pingResponse);
            } catch (pingError) {
                updateStatus('Content script nie odpowiada: ' + pingError.message);
                return;
            }

            // Pobierz dane z content script
            const response = await new Promise((resolve, reject) => {
                chrome.tabs.sendMessage(tab.id, { action: 'extractScheduleData' }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });

            if (response && response.success && response.data) {
                // Otwórz nowe okno z podglądem JSON
                const jsonWindow = window.open('', '_blank', 'width=800,height=600');
                jsonWindow.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>KtoOstatni - Podgląd danych</title>
                        <style>
                            body { font-family: monospace; margin: 20px; background: #1e1e1e; color: #d4d4d4; }
                            pre { background: #2d2d2d; padding: 20px; border-radius: 8px; overflow: auto; }
                            .header { background: #007acc; color: white; padding: 15px; margin: -20px -20px 20px -20px; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h2>🚀 KtoOstatni - Podgląd danych synchronizacji</h2>
                            <p>Dane wyciągnięte z harmonogramu iGabinet</p>
                        </div>
                        <pre>${JSON.stringify(response.data, null, 2)}</pre>
                    </body>
                    </html>
                `);
                updateStatus('Podgląd danych otwarty w nowym oknie');
            } else {
                updateStatus('Błąd pobierania danych: ' + (response?.error || 'Nieznany błąd'));
            }

        } catch (error) {
            console.error('KtoOstatni: Błąd podglądu danych:', error);
            updateStatus('Błąd: ' + error.message);
        }
    }

    // Pobierz ostatnie dane jako JSON
    async function downloadLastSyncData() {
        try {
            updateStatus('Przygotowywanie pliku JSON...');

            // Sprawdź czy jesteśmy na stronie iGabinet
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab.url.includes('igabinet.pl') && !tab.url.includes('test_page.html')) {
                updateStatus('Przejdź na stronę iGabinet lub test_page.html');
                return;
            }

            // Najpierw sprawdź czy content script jest załadowany
            try {
                const pingResponse = await new Promise((resolve, reject) => {
                    chrome.tabs.sendMessage(tab.id, { action: 'ping' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                if (!pingResponse || !pingResponse.success) {
                    updateStatus('Content script nie jest załadowany na tej stronie');
                    return;
                }

                console.log('KtoOstatni: Content script ping OK:', pingResponse);
            } catch (pingError) {
                updateStatus('Content script nie odpowiada: ' + pingError.message);
                return;
            }

            // Pobierz dane z content script
            const response = await new Promise((resolve, reject) => {
                chrome.tabs.sendMessage(tab.id, { action: 'extractScheduleData' }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });

            if (response && response.success && response.data) {
                // Wyślij do background script do pobrania
                chrome.runtime.sendMessage({
                    action: 'downloadData',
                    data: response.data
                }, (downloadResponse) => {
                    if (downloadResponse && downloadResponse.success) {
                        updateStatus('Plik JSON został pobrany pomyślnie');
                    } else {
                        updateStatus('Błąd pobierania pliku: ' + (downloadResponse?.error || 'Nieznany błąd'));
                    }
                });
            } else {
                updateStatus('Błąd pobierania danych: ' + (response?.error || 'Nieznany błąd'));
            }

        } catch (error) {
            console.error('KtoOstatni: Błąd pobierania JSON:', error);
            updateStatus('Błąd: ' + error.message);
        }
    }

    // Wyczyść cache
    async function clearCache() {
        try {
            if (!confirm('Czy na pewno chcesz wyczyścić cache? Następna synchronizacja wyśle wszystkie dane ponownie.')) {
                return;
            }

            setButtonsEnabled(false);
            updateStatus('🗑️ Czyszczenie cache...');

            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'clearCache'
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });

            if (response.success) {
                updateStatus('✅ Cache wyczyszczony pomyślnie');
                // Odśwież konfigurację
                await loadConfig();
                updateUI();
            } else {
                updateStatus(`❌ Błąd czyszczenia cache: ${response.error}`);
            }

        } catch (error) {
            console.error('KtoOstatni: Błąd czyszczenia cache:', error);
            updateStatus(`❌ Błąd czyszczenia cache: ${error.message}`);
        } finally {
            setButtonsEnabled(true);
        }
    }

    // Funkcje pomocnicze
    function updateStatus(message) {
        statusElement.textContent = message;
        console.log('KtoOstatni Status:', message);
    }

    function updateProgress(percent) {
        progressBar.style.width = percent + '%';
    }

    function showLoading(show) {
        loadingElement.style.display = show ? 'block' : 'none';
    }

    function hideLoading() {
        showLoading(false);
    }

    function setButtonsEnabled(enabled) {
        manualSyncButton.disabled = !enabled;
        viewDataButton.disabled = !enabled;
        downloadDataButton.disabled = !enabled;
        clearCacheButton.disabled = !enabled;
    }

    // Wyświetl błąd synchronizacji w przyjazny sposób
    function displaySyncError(errorMessage) {
        console.error('KtoOstatni: Szczegółowy błąd synchronizacji:', errorMessage);

        // Sprawdź czy to błąd z wieloma liniami (szczegółowy błąd API)
        if (errorMessage.includes('\n')) {
            // Błąd wieloliniowy - pokaż w oknie modalnym
            showDetailedErrorModal(errorMessage);
        } else {
            // Błąd jednoliniowy - pokaż w statusie
            updateStatus(`❌ ${errorMessage}`);
        }
    }

    // Pokaż szczegółowy błąd w oknie modalnym
    function showDetailedErrorModal(errorMessage) {
        // Utwórz modal
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
        `;

        const modalContent = document.createElement('div');
        modalContent.style.cssText = `
            background: #2c3e50;
            color: white;
            border-radius: 12px;
            padding: 20px;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            border: 1px solid #34495e;
        `;

        // Formatuj błąd dla lepszej czytelności
        const formattedError = errorMessage
            .replace(/\n\n/g, '<br><br>')
            .replace(/\n/g, '<br>')
            .replace(/•/g, '&bull;')
            .replace(/🔍/g, '<span style="color: #3498db;">🔍</span>')
            .replace(/💡/g, '<span style="color: #f39c12;">💡</span>')
            .replace(/📄/g, '<span style="color: #9b59b6;">📄</span>')
            .replace(/⏰/g, '<span style="color: #95a5a6;">⏰</span>');

        modalContent.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3 style="margin: 0; color: #e74c3c;">❌ Błąd synchronizacji</h3>
                <button id="closeErrorModal" style="
                    background: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 12px;
                    cursor: pointer;
                    font-size: 14px;
                ">Zamknij</button>
            </div>
            <div style="
                background: #34495e;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid #e74c3c;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.5;
                font-size: 13px;
            ">
                ${formattedError}
            </div>
            <div style="margin-top: 15px; text-align: center;">
                <button id="copyErrorButton" style="
                    background: #3498db;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    cursor: pointer;
                    font-size: 14px;
                    margin-right: 10px;
                ">📋 Skopiuj błąd</button>
                <button id="retryButton" style="
                    background: #27ae60;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    cursor: pointer;
                    font-size: 14px;
                ">🔄 Spróbuj ponownie</button>
            </div>
        `;

        modal.appendChild(modalContent);
        document.body.appendChild(modal);

        // Obsługa przycisków
        document.getElementById('closeErrorModal').onclick = () => {
            document.body.removeChild(modal);
            updateStatus('❌ Błąd synchronizacji - sprawdź szczegóły w konsoli');
        };

        document.getElementById('copyErrorButton').onclick = () => {
            navigator.clipboard.writeText(errorMessage).then(() => {
                updateStatus('📋 Błąd skopiowany do schowka');
            }).catch(() => {
                console.log('Błąd do skopiowania:', errorMessage);
                updateStatus('📋 Błąd wyświetlony w konsoli');
            });
        };

        document.getElementById('retryButton').onclick = () => {
            document.body.removeChild(modal);
            performManualSync();
        };

        // Zamknij modal po kliknięciu w tło
        modal.onclick = (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
                updateStatus('❌ Błąd synchronizacji - sprawdź szczegóły w konsoli');
            }
        };
    }

});
