<?php

/**
 * Konfiguracja aplikacji KtoOstatni v4
 * System zarządzania kolejkami
 */

// Zapobieganie bezpośredniemu dostępowi
if (!defined('KTOOSTATNI_APP')) {
    die('Bezpośredni dostęp zabroniony');
}

// Konfiguracja bazy danych
define('DB_PATH', __DIR__ . '/db/reklama.db');

// Konfiguracja aplikacji
define('APP_NAME', 'KtoOstatni.pl');
define('APP_VERSION', '4.0');
define('CLIENT_ID', 'igab1234567890123'); // ID głównej przychodni (Sonokard)
define('CLIENT_ID_DOCTORS', 2); // ID dla lekarzy w tabeli queue_doctors

// Konfiguracja sesji
define('SESSION_NAME', 'ktoostatni_session');
define('SESSION_LIFETIME', 3600 * 8); // 8 godzin

// <PERSON><PERSON><PERSON><PERSON>
define('BASE_PATH', __DIR__);
define('ADMIN_PATH', BASE_PATH . '/admin');
define('UPLOADS_PATH', BASE_PATH . '/uploads');
define('DOCTORS_PHOTOS_PATH', UPLOADS_PATH . '/doctors');

// URL-e - automatyczne wykrywanie portu
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
define('BASE_URL', $protocol . '://' . $host);
define('ADMIN_URL', BASE_URL . '/admin');
define('UPLOADS_URL', BASE_URL . '/uploads');
define('DOCTORS_PHOTOS_URL', UPLOADS_URL . '/doctors');

// Konfiguracja systemu kolejkowego
define('DEFAULT_APPOINTMENT_DURATION', 20); // minuty
define('QUEUE_REFRESH_INTERVAL', 5); // sekundy

// Strefy czasowe
date_default_timezone_set('Europe/Warsaw');

// Konfiguracja błędów (tylko w development)
// Wykryj środowisko development na podstawie localhost lub IP lokalnego
$isLocalhost = (
    strpos($_SERVER['HTTP_HOST'] ?? '', 'localhost') === 0 ||
    strpos($_SERVER['HTTP_HOST'] ?? '', '127.0.0.1') === 0 ||
    strpos($_SERVER['HTTP_HOST'] ?? '', '::1') === 0
);

if ($isLocalhost) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    define('DEBUG_MODE', true);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    define('DEBUG_MODE', false);
}

// Autoloader dla klas
spl_autoload_register(function ($className) {
    $paths = [
        ADMIN_PATH . '/controllers/' . $className . '.php',
        ADMIN_PATH . '/models/' . $className . '.php',
        ADMIN_PATH . '/helpers/' . $className . '.php'
    ];

    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
});

// Funkcje pomocnicze
function redirect($url) {
    header('Location: ' . $url);
    exit;
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }

    static $user = null;
    if ($user === null) {
        $db = new Database();
        $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
    }

    return $user;
}

function formatTime($time) {
    // Jeśli czas zawiera datę (format: 2025-09-18 08:40), wyciągnij tylko czas
    if (strpos($time, ' ') !== false) {
        $time = substr($time, 11, 8);
    }

    // Jeśli czas ma sekundy (HH:MM:SS), usuń je
    if (substr_count($time, ':') == 2) {
        $time = substr($time, 0, 5);
    }

    // Jeśli czas jest już w formacie HH:MM, zwróć go
    if (preg_match('/^\d{2}:\d{2}$/', $time)) {
        return $time;
    }

    // W przeciwnym razie spróbuj parsować przez strtotime
    return date('H:i', strtotime($time));
}

function formatDate($date) {
    return date('d.m.Y', strtotime($date));
}

function formatDateTime($datetime) {
    return date('d.m.Y H:i', strtotime($datetime));
}

function calculateDelay($appointmentTime, $appointmentDate = null, $status = 'waiting') {
    // Wizyty zakończone nie mają opóźnienia
    if ($status === 'completed' || $status === 'closed') {
        return '';
    }

    // Normalizuj czas
    $time = formatTime($appointmentTime);

    // Jeśli nie podano daty, użyj dzisiejszej
    if ($appointmentDate === null) {
        $appointmentDate = date('Y-m-d');
    }

    // Utwórz pełną datę i czas wizyty
    $appointmentDateTime = $appointmentDate . ' ' . $time . ':00';
    $appointmentTimestamp = strtotime($appointmentDateTime);

    // Określ koniec dnia wizyty (23:59:59)
    $endOfAppointmentDay = strtotime($appointmentDate . ' 23:59:59');

    // Użyj aktualnego czasu lub końca dnia wizyty - co jest wcześniejsze
    $currentTimestamp = time();
    $compareTimestamp = min($currentTimestamp, $endOfAppointmentDay);

    // Oblicz opóźnienie tylko jeśli wizyta była w przeszłości
    if ($appointmentTimestamp < $compareTimestamp) {
        $delaySeconds = $compareTimestamp - $appointmentTimestamp;
        $delayMinutes = floor($delaySeconds / 60);

        // Formatuj jako XhYm
        $hours = floor($delayMinutes / 60);
        $minutes = $delayMinutes % 60;

        if ($hours > 0) {
            return sprintf('%dh%dm', $hours, $minutes);
        } else {
            return sprintf('%dm', $minutes);
        }
    }

    return '';
}

function normalizePatientName($name) {
    if (empty($name)) {
        return $name;
    }

    // Podziel na słowa
    $words = preg_split('/\s+/', trim($name));
    $normalizedWords = [];

    foreach ($words as $word) {
        if (empty($word)) continue;

        // Pierwsza litera wielka, reszta mała (obsługa UTF-8)
        $normalized = mb_strtoupper(mb_substr($word, 0, 1, 'UTF-8'), 'UTF-8') .
            mb_strtolower(mb_substr($word, 1, null, 'UTF-8'), 'UTF-8');
        $normalizedWords[] = $normalized;
    }

    return implode(' ', $normalizedWords);
}

function calculateDelayMinutes($appointmentTime, $appointmentDate = null, $status = 'waiting') {
    // Wizyty zakończone nie mają opóźnienia
    if ($status === 'completed' || $status === 'closed') {
        return 0;
    }

    // Normalizuj czas
    $time = formatTime($appointmentTime);

    // Jeśli nie podano daty, użyj dzisiejszej
    if ($appointmentDate === null) {
        $appointmentDate = date('Y-m-d');
    }

    // Utwórz pełną datę i czas wizyty
    $appointmentDateTime = $appointmentDate . ' ' . $time . ':00';
    $appointmentTimestamp = strtotime($appointmentDateTime);

    // Określ koniec dnia wizyty (23:59:59)
    $endOfAppointmentDay = strtotime($appointmentDate . ' 23:59:59');

    // Użyj aktualnego czasu lub końca dnia wizyty - co jest wcześniejsze
    $currentTimestamp = time();
    $compareTimestamp = min($currentTimestamp, $endOfAppointmentDay);

    // Oblicz opóźnienie tylko jeśli wizyta była w przeszłości
    if ($appointmentTimestamp < $compareTimestamp) {
        $delaySeconds = $compareTimestamp - $appointmentTimestamp;
        return floor($delaySeconds / 60);
    }

    return 0;
}



function getDoctorPhotoUrl($photoPath) {
    if (empty($photoPath)) {
        return DOCTORS_PHOTOS_URL . '/default-avatar.webp';
    }

    // Usuń początkowy slash jeśli istnieje
    $photoPath = ltrim($photoPath, '/');

    // Sprawdź czy plik istnieje
    $fullPath = BASE_PATH . '/' . $photoPath;
    if (file_exists($fullPath)) {
        return BASE_URL . '/' . $photoPath;
    }

    return DOCTORS_PHOTOS_URL . '/default-avatar.webp';
}

function getStatusBadgeClass($status) {
    switch ($status) {
        case 'working':
            return 'bg-green-100 text-green-800 border-green-200';
        case 'ready':
            return 'bg-blue-100 text-blue-800 border-blue-200';
        case 'finished':
            return 'bg-gray-100 text-gray-800 border-gray-200';
        case 'no_appointments':
            return 'bg-slate-100 text-slate-600 border-slate-200';
            // Patient statuses
        case 'waiting':
            return 'bg-blue-100 text-blue-800 border-blue-200';
        case 'current':
            return 'bg-green-100 text-green-800 border-green-200';
        case 'completed':
            return 'bg-gray-100 text-gray-800 border-gray-200';
        default:
            return 'bg-gray-100 text-gray-800 border-gray-200';
    }
}

function getStatusText($status) {
    switch ($status) {
        case 'working':
            return 'Pracuje';
        case 'ready':
            return 'Gotowy';
        case 'finished':
            return 'Zakończył';
        case 'no_appointments':
            return 'Brak wizyt';
            // Patient statuses
        case 'waiting':
            return 'Czeka';
        case 'current':
            return 'U lekarza';
        case 'completed':
        case 'closed':
            return 'Zakończona';
        default:
            return 'Nieznany';
    }
}

function getDoctorHeaderClass($status) {
    switch ($status) {
        case 'working':
            return 'bg-gradient-to-r from-green-50 to-emerald-50';
        case 'ready':
            return 'bg-gradient-to-r from-blue-50 to-indigo-50';
        case 'finished':
            return 'bg-gradient-to-r from-gray-50 to-slate-50';
        case 'no_appointments':
        default:
            return 'bg-gradient-to-r from-slate-50 to-gray-50';
    }
}

// Inicjalizacja sesji
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();

    // Sprawdź ważność sesji
    if (
        isset($_SESSION['last_activity']) &&
        (time() - $_SESSION['last_activity'] > SESSION_LIFETIME)
    ) {
        session_unset();
        session_destroy();
        session_start();
    }

    $_SESSION['last_activity'] = time();
}
