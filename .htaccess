RewriteEngine On

# Przekieruj wszystkie requesty na index.php (poza plikami statycznymi)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Zabezpieczenia
<Files "config.php">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.db">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# MIME types dla plików VTT
<IfModule mod_mime.c>
    AddType text/vtt .vtt
</IfModule>

# CORS headers dla plików VTT
<FilesMatch "\.(vtt)$">
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type"
</FilesMatch>

# Cache dla plików statycznych
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/vtt "access plus 1 week"
</IfModule>
